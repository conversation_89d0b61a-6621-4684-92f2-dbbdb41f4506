-- BHEEMDINE RLS Testing Scenarios
-- Use these queries to verify RLS policies are working correctly

-- =====================================================
-- SETUP TEST DATA
-- =====================================================

-- Create test tenants
INSERT INTO "Tenant" (id, name, slug, "isActive") VALUES
  ('11111111-1111-1111-1111-111111111111', 'Restaurant A', 'restaurant-a', true),
  ('22222222-2222-2222-2222-222222222222', 'Restaurant B', 'restaurant-b', true);

-- Create test staff
INSERT INTO "Staff" (id, "tenantId", email, name, role, pin) VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', '<EMAIL>', 'Admin A', 'ADMIN', '1234'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '22222222-2222-2222-2222-222222222222', '<EMAIL>', 'Admin B', 'ADMIN', '5678');

-- Create test rooms
INSERT INTO "Room" (id, "tenantId", "roomNumber", "qrCode", status) VALUES
  ('r1111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111', '101', 'QR-A-101', 'AVAILABLE'),
  ('r2222222-2222-2222-2222-222222222222', '22222222-2222-2222-2222-222222222222', '201', 'QR-B-201', 'AVAILABLE');

-- =====================================================
-- TEST SCENARIOS
-- =====================================================

-- Test 1: Tenant Isolation
-- Set JWT claims for Restaurant A admin
SET request.jwt.claims = '{"sub": "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa", "tenant_id": "11111111-1111-1111-1111-111111111111", "role": "staff", "staff_role": "ADMIN"}';

-- This should return only Restaurant A data
SELECT * FROM "Tenant";  -- Should see only Restaurant A
SELECT * FROM "Room";    -- Should see only Room 101
SELECT * FROM "Staff";   -- Should see only Admin A

-- Test 2: Customer Access
-- Set JWT claims for a customer
SET request.jwt.claims = '{"sub": "cccccccc-cccc-cccc-cccc-cccccccccccc", "tenant_id": "11111111-1111-1111-1111-111111111111", "role": "customer"}';

-- Customers should have limited access
SELECT * FROM "Tenant";  -- Should see Restaurant A
SELECT * FROM "Room";    -- Should see rooms (for QR scanning)
SELECT * FROM "Staff";   -- Should see nothing (no access)

-- Test 3: Anonymous Access (QR Code Scanning)
-- Clear JWT claims to simulate anonymous user
SET request.jwt.claims = '{}';

-- Anonymous users can view public data
SELECT * FROM "Room" WHERE "qrCode" = 'QR-A-101';  -- Should work
SELECT * FROM "MenuItem" WHERE "isAvailable" = true;  -- Should work
SELECT * FROM "Staff";  -- Should see nothing

-- Test 4: Cross-Tenant Access Prevention
-- Try to access Restaurant B data while authenticated as Restaurant A
SET request.jwt.claims = '{"sub": "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa", "tenant_id": "11111111-1111-1111-1111-111111111111", "role": "staff", "staff_role": "ADMIN"}';

-- Try to update Restaurant B room (should fail)
UPDATE "Room" SET status = 'OCCUPIED' WHERE id = 'r2222222-2222-2222-2222-222222222222';
-- ERROR: new row violates row-level security policy

-- Test 5: Order Creation and Visibility
-- Create a test user and order
SET request.jwt.claims = '{"sub": "cccccccc-cccc-cccc-cccc-cccccccccccc", "tenant_id": "11111111-1111-1111-1111-111111111111", "role": "customer"}';

INSERT INTO "User" (id, "tenantId", email, name) VALUES
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', '11111111-1111-1111-1111-111111111111', '<EMAIL>', 'Test Customer');

INSERT INTO "Order" (id, "tenantId", "userId", "roomId", "orderNumber", "totalAmount") VALUES
  ('o1111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 'r1111111-1111-1111-1111-111111111111', 'ORD-001', 25.50);

-- Customer can see their own order
SELECT * FROM "Order";  -- Should see ORD-001

-- Switch to different customer
SET request.jwt.claims = '{"sub": "dddddddd-dddd-dddd-dddd-dddddddddddd", "tenant_id": "11111111-1111-1111-1111-111111111111", "role": "customer"}';
SELECT * FROM "Order";  -- Should see nothing (not their order)

-- Test 6: Staff Order Management
SET request.jwt.claims = '{"sub": "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa", "tenant_id": "11111111-1111-1111-1111-111111111111", "role": "staff", "staff_role": "WAITER"}';

-- Staff can see all orders in their tenant
SELECT * FROM "Order";  -- Should see all Restaurant A orders

-- Staff can update order status
UPDATE "Order" SET status = 'CONFIRMED' WHERE id = 'o1111111-1111-1111-1111-111111111111';  -- Should succeed

-- =====================================================
-- CLEANUP
-- =====================================================

-- Reset JWT claims
RESET request.jwt.claims;

-- Optional: Remove test data
-- DELETE FROM "OrderItem" WHERE "orderId" IN (SELECT id FROM "Order" WHERE "tenantId" IN ('11111111-1111-1111-1111-111111111111', '22222222-2222-2222-2222-222222222222'));
-- DELETE FROM "Order" WHERE "tenantId" IN ('11111111-1111-1111-1111-111111111111', '22222222-2222-2222-2222-222222222222');
-- DELETE FROM "User" WHERE "tenantId" IN ('11111111-1111-1111-1111-111111111111', '22222222-2222-2222-2222-222222222222');
-- DELETE FROM "Room" WHERE "tenantId" IN ('11111111-1111-1111-1111-111111111111', '22222222-2222-2222-2222-222222222222');
-- DELETE FROM "Staff" WHERE "tenantId" IN ('11111111-1111-1111-1111-111111111111', '22222222-2222-2222-2222-222222222222');
-- DELETE FROM "Tenant" WHERE id IN ('11111111-1111-1111-1111-111111111111', '22222222-2222-2222-2222-222222222222');
-- BHEEMDINE Authentication Schema and JWT Configuration
-- This migration sets up custom auth tables and JWT claims for multi-tenant role-based access

-- =====================================================
-- AUTH SCHEMA EXTENSIONS
-- =====================================================

-- Create custom auth metadata table for storing tenant and role information
CREATE TABLE IF NOT EXISTS auth.user_metadata (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL REFERENCES public."Tenant"(id) ON DELETE RESTRICT,
  user_type TEXT NOT NULL CHECK (user_type IN ('customer', 'staff', 'guest')),
  staff_id UUID REFERENCES public."Staff"(id) ON DELETE SET NULL,
  customer_id UUID REFERENCES public."User"(id) ON DELETE SET NULL,
  room_id UUID REFERENCES public."Room"(id) ON DELETE SET NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on auth metadata
ALTER TABLE auth.user_metadata ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only read their own metadata
CREATE POLICY "Users can view own metadata" ON auth.user_metadata
  FOR SELECT USING (auth.uid() = id);

-- Create indexes for performance
CREATE INDEX idx_auth_user_metadata_tenant_id ON auth.user_metadata(tenant_id);
CREATE INDEX idx_auth_user_metadata_user_type ON auth.user_metadata(user_type);
CREATE INDEX idx_auth_user_metadata_staff_id ON auth.user_metadata(staff_id) WHERE staff_id IS NOT NULL;
CREATE INDEX idx_auth_user_metadata_customer_id ON auth.user_metadata(customer_id) WHERE customer_id IS NOT NULL;

-- =====================================================
-- JWT CUSTOM CLAIMS FUNCTION
-- =====================================================

-- Function to generate custom JWT claims
CREATE OR REPLACE FUNCTION auth.custom_jwt_claims()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  claims JSON;
  user_meta RECORD;
  staff_info RECORD;
BEGIN
  -- Get user metadata
  SELECT 
    um.*,
    t.slug as tenant_slug,
    t.name as tenant_name
  INTO user_meta
  FROM auth.user_metadata um
  JOIN public."Tenant" t ON t.id = um.tenant_id
  WHERE um.id = auth.uid()
  AND t."isActive" = true
  AND t."isDeleted" = false;

  -- If no metadata found, return minimal claims
  IF NOT FOUND THEN
    RETURN json_build_object(
      'user_id', auth.uid(),
      'role', 'anonymous'
    );
  END IF;

  -- Build base claims
  claims := json_build_object(
    'user_id', auth.uid(),
    'tenant_id', user_meta.tenant_id,
    'tenant_slug', user_meta.tenant_slug,
    'tenant_name', user_meta.tenant_name,
    'user_type', user_meta.user_type,
    'room_id', user_meta.room_id
  );

  -- Add role-specific claims
  IF user_meta.user_type = 'staff' AND user_meta.staff_id IS NOT NULL THEN
    -- Get staff details
    SELECT 
      s.role,
      s.name,
      s.email,
      s."isActive"
    INTO staff_info
    FROM public."Staff" s
    WHERE s.id = user_meta.staff_id
    AND s."tenantId" = user_meta.tenant_id;

    IF FOUND AND staff_info."isActive" THEN
      claims := claims || json_build_object(
        'role', 'staff',
        'staff_role', staff_info.role,
        'staff_id', user_meta.staff_id,
        'staff_name', staff_info.name,
        'staff_email', staff_info.email
      );
    ELSE
      -- Inactive staff get no access
      claims := json_build_object(
        'user_id', auth.uid(),
        'role', 'inactive',
        'error', 'Staff account is inactive'
      );
    END IF;
  ELSIF user_meta.user_type = 'customer' AND user_meta.customer_id IS NOT NULL THEN
    claims := claims || json_build_object(
      'role', 'customer',
      'customer_id', user_meta.customer_id
    );
  ELSIF user_meta.user_type = 'guest' THEN
    claims := claims || json_build_object(
      'role', 'guest',
      'customer_id', user_meta.customer_id
    );
  END IF;

  -- Add any custom metadata
  IF user_meta.metadata IS NOT NULL THEN
    claims := claims || json_build_object('metadata', user_meta.metadata);
  END IF;

  RETURN claims;
END;
$$;

-- =====================================================
-- AUTH HELPER FUNCTIONS
-- =====================================================

-- Function to create or get guest user
CREATE OR REPLACE FUNCTION auth.create_guest_user(
  p_tenant_id UUID,
  p_room_id UUID,
  p_name TEXT DEFAULT 'Guest'
)
RETURNS TABLE (
  auth_user_id UUID,
  customer_id UUID,
  access_token TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_auth_user_id UUID;
  v_customer_id UUID;
  v_email TEXT;
  v_password TEXT;
  v_access_token TEXT;
BEGIN
  -- Validate tenant and room
  IF NOT EXISTS (
    SELECT 1 FROM public."Room" r
    JOIN public."Tenant" t ON t.id = r."tenantId"
    WHERE r.id = p_room_id
    AND r."tenantId" = p_tenant_id
    AND t."isActive" = true
    AND t."isDeleted" = false
  ) THEN
    RAISE EXCEPTION 'Invalid tenant or room';
  END IF;

  -- Generate unique guest email
  v_email := 'guest_' || gen_random_uuid() || '@bheemdine.local';
  v_password := gen_random_uuid()::TEXT;

  -- Create auth user
  v_auth_user_id := auth.uid();
  
  -- Create guest in public.User table
  INSERT INTO public."User" (
    id, 
    "tenantId", 
    email, 
    name, 
    "roomId", 
    "isGuest"
  ) VALUES (
    gen_random_uuid(),
    p_tenant_id,
    v_email,
    p_name,
    p_room_id,
    true
  )
  RETURNING id INTO v_customer_id;

  -- Create auth metadata
  INSERT INTO auth.user_metadata (
    id,
    tenant_id,
    user_type,
    customer_id,
    room_id,
    metadata
  ) VALUES (
    v_auth_user_id,
    p_tenant_id,
    'guest',
    v_customer_id,
    p_room_id,
    jsonb_build_object(
      'created_via', 'qr_scan',
      'created_at', NOW()
    )
  );

  -- Generate access token (in real implementation, use Supabase Auth)
  v_access_token := encode(gen_random_bytes(32), 'base64');

  RETURN QUERY
  SELECT v_auth_user_id, v_customer_id, v_access_token;
END;
$$;

-- Function to authenticate staff with PIN
CREATE OR REPLACE FUNCTION auth.authenticate_staff_pin(
  p_tenant_id UUID,
  p_email TEXT,
  p_pin TEXT
)
RETURNS TABLE (
  success BOOLEAN,
  staff_id UUID,
  auth_user_id UUID,
  message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_staff RECORD;
  v_auth_user_id UUID;
BEGIN
  -- Find staff member
  SELECT 
    s.*,
    t."isActive" as tenant_active,
    t."isDeleted" as tenant_deleted
  INTO v_staff
  FROM public."Staff" s
  JOIN public."Tenant" t ON t.id = s."tenantId"
  WHERE s.email = p_email
  AND s."tenantId" = p_tenant_id
  AND s.pin = p_pin;

  IF NOT FOUND THEN
    RETURN QUERY
    SELECT false, NULL::UUID, NULL::UUID, 'Invalid email or PIN';
    RETURN;
  END IF;

  IF NOT v_staff.tenant_active OR v_staff.tenant_deleted THEN
    RETURN QUERY
    SELECT false, NULL::UUID, NULL::UUID, 'Tenant is inactive';
    RETURN;
  END IF;

  IF NOT v_staff."isActive" THEN
    RETURN QUERY
    SELECT false, NULL::UUID, NULL::UUID, 'Staff account is inactive';
    RETURN;
  END IF;

  -- Get or create auth user
  SELECT id INTO v_auth_user_id
  FROM auth.user_metadata
  WHERE staff_id = v_staff.id;

  IF v_auth_user_id IS NULL THEN
    -- Create new auth user for staff
    v_auth_user_id := gen_random_uuid();
    
    INSERT INTO auth.user_metadata (
      id,
      tenant_id,
      user_type,
      staff_id,
      metadata
    ) VALUES (
      v_auth_user_id,
      p_tenant_id,
      'staff',
      v_staff.id,
      jsonb_build_object(
        'first_login', NOW()
      )
    );
  END IF;

  -- Update last login
  UPDATE public."Staff"
  SET "lastLogin" = NOW()
  WHERE id = v_staff.id;

  RETURN QUERY
  SELECT true, v_staff.id, v_auth_user_id, 'Authentication successful';
END;
$$;

-- =====================================================
-- TRIGGERS FOR JWT CLAIMS
-- =====================================================

-- Trigger to automatically add custom claims to JWT
CREATE OR REPLACE FUNCTION auth.set_jwt_claims()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Add custom claims to the JWT
  NEW.raw_app_meta_data = COALESCE(NEW.raw_app_meta_data, '{}'::jsonb) || 
    jsonb_build_object('custom_claims', auth.custom_jwt_claims());
  
  RETURN NEW;
END;
$$;

-- Create trigger on auth.users
DROP TRIGGER IF EXISTS set_jwt_claims_trigger ON auth.users;
CREATE TRIGGER set_jwt_claims_trigger
  BEFORE INSERT OR UPDATE ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION auth.set_jwt_claims();

-- =====================================================
-- AUTH HOOKS
-- =====================================================

-- Hook function for after sign up
CREATE OR REPLACE FUNCTION auth.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_tenant_id UUID;
  v_customer_id UUID;
BEGIN
  -- Extract tenant_id from metadata or use default
  v_tenant_id := COALESCE(
    (NEW.raw_user_meta_data->>'tenant_id')::UUID,
    (SELECT id FROM public."Tenant" WHERE slug = 'default' LIMIT 1)
  );

  -- Only create customer record for non-staff users
  IF COALESCE(NEW.raw_user_meta_data->>'user_type', 'customer') = 'customer' THEN
    -- Create customer record
    INSERT INTO public."User" (
      id,
      "tenantId",
      email,
      name,
      "isGuest"
    ) VALUES (
      gen_random_uuid(),
      v_tenant_id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
      false
    )
    RETURNING id INTO v_customer_id;

    -- Create metadata entry
    INSERT INTO auth.user_metadata (
      id,
      tenant_id,
      user_type,
      customer_id
    ) VALUES (
      NEW.id,
      v_tenant_id,
      'customer',
      v_customer_id
    );
  END IF;

  RETURN NEW;
END;
$$;

-- Create trigger for new user sign up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION auth.handle_new_user();

-- =====================================================
-- SECURITY FUNCTIONS
-- =====================================================

-- Function to validate room access via QR code
CREATE OR REPLACE FUNCTION auth.validate_qr_access(
  p_qr_code TEXT
)
RETURNS TABLE (
  valid BOOLEAN,
  tenant_id UUID,
  room_id UUID,
  room_number TEXT,
  tenant_name TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    true as valid,
    r."tenantId",
    r.id as room_id,
    r."roomNumber",
    t.name as tenant_name
  FROM public."Room" r
  JOIN public."Tenant" t ON t.id = r."tenantId"
  WHERE r."qrCode" = p_qr_code
  AND r.status = 'AVAILABLE'
  AND t."isActive" = true
  AND t."isDeleted" = false;

  -- If no results, return invalid
  IF NOT FOUND THEN
    RETURN QUERY
    SELECT false, NULL::UUID, NULL::UUID, NULL::TEXT, NULL::TEXT;
  END IF;
END;
$$;

-- Function to switch tenant context (for multi-tenant staff)
CREATE OR REPLACE FUNCTION auth.switch_tenant_context(
  p_user_id UUID,
  p_new_tenant_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_staff_exists BOOLEAN;
BEGIN
  -- Check if staff exists in the new tenant
  SELECT EXISTS (
    SELECT 1 
    FROM public."Staff" s
    JOIN auth.user_metadata um ON um.staff_id = s.id
    WHERE um.id = p_user_id
    AND s."tenantId" = p_new_tenant_id
    AND s."isActive" = true
  ) INTO v_staff_exists;

  IF v_staff_exists THEN
    -- Update the tenant context
    UPDATE auth.user_metadata
    SET 
      tenant_id = p_new_tenant_id,
      updated_at = NOW()
    WHERE id = p_user_id;
    
    RETURN true;
  END IF;

  RETURN false;
END;
$$;

-- =====================================================
-- ROLE MANAGEMENT FUNCTIONS  
-- =====================================================

-- Function to promote guest to registered customer
CREATE OR REPLACE FUNCTION auth.promote_guest_to_customer(
  p_guest_id UUID,
  p_email TEXT,
  p_password TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_customer_id UUID;
  v_tenant_id UUID;
BEGIN
  -- Get guest details
  SELECT customer_id, tenant_id
  INTO v_customer_id, v_tenant_id
  FROM auth.user_metadata
  WHERE id = p_guest_id
  AND user_type = 'guest';

  IF FOUND THEN
    -- Update public.User record
    UPDATE public."User"
    SET 
      email = p_email,
      "isGuest" = false
    WHERE id = v_customer_id;

    -- Update auth metadata
    UPDATE auth.user_metadata
    SET 
      user_type = 'customer',
      updated_at = NOW()
    WHERE id = p_guest_id;

    -- Update auth.users (in real implementation)
    -- This would involve Supabase Auth API calls

    RETURN true;
  END IF;

  RETURN false;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION auth.custom_jwt_claims() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION auth.create_guest_user(UUID, UUID, TEXT) TO anon;
GRANT EXECUTE ON FUNCTION auth.authenticate_staff_pin(UUID, TEXT, TEXT) TO anon;
GRANT EXECUTE ON FUNCTION auth.validate_qr_access(TEXT) TO anon;
GRANT EXECUTE ON FUNCTION auth.switch_tenant_context(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION auth.promote_guest_to_customer(UUID, TEXT, TEXT) TO authenticated;
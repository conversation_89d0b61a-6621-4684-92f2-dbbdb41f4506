-- BHEEMDINE Row-Level Security (RLS) Migration
-- This migration enables <PERSON><PERSON> on all tables and creates policies for multi-tenant isolation
-- 
-- Key Principles:
-- 1. All queries must be scoped to the authenticated user's tenant
-- 2. Users can only access data within their assigned tenant
-- 3. Different policies for customers vs staff members
-- 4. Public read access for menu items (when accessed via QR code)

-- Enable RLS on all tables
ALTER TABLE "Tenant" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "User" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Room" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "MenuItem" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Allergen" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "MenuItemAllergen" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Order" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "OrderItem" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "OrderEvent" ENABLE ROW LEVEL SECURITY;
<PERSON>TER TABLE "Staff" ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON> custom JWT claims function to extract tenant_id and user role
-- This assumes your JWT token contains: { tenant_id: 'uuid', role: 'customer|staff', staff_role: 'ADMIN|MANAGER|etc' }
CREATE OR REPLACE FUNCTION auth.tenant_id() 
RETURNS TEXT AS $$
  SELECT COALESCE(
    current_setting('request.jwt.claims', true)::json->>'tenant_id',
    ''
  )::TEXT;
$$ LANGUAGE SQL STABLE;

CREATE OR REPLACE FUNCTION auth.user_role() 
RETURNS TEXT AS $$
  SELECT COALESCE(
    current_setting('request.jwt.claims', true)::json->>'role',
    'anonymous'
  )::TEXT;
$$ LANGUAGE SQL STABLE;

CREATE OR REPLACE FUNCTION auth.staff_role() 
RETURNS TEXT AS $$
  SELECT COALESCE(
    current_setting('request.jwt.claims', true)::json->>'staff_role',
    ''
  )::TEXT;
$$ LANGUAGE SQL STABLE;

CREATE OR REPLACE FUNCTION auth.user_id() 
RETURNS UUID AS $$
  SELECT COALESCE(
    (current_setting('request.jwt.claims', true)::json->>'sub')::UUID,
    '00000000-0000-0000-0000-000000000000'::UUID
  );
$$ LANGUAGE SQL STABLE;

-- =====================================================
-- TENANT POLICIES
-- =====================================================

-- Tenants: Only viewable by authenticated users within the tenant
CREATE POLICY "tenant_select_own" ON "Tenant"
  FOR SELECT
  USING (
    id = auth.tenant_id()::UUID 
    AND "isDeleted" = false 
    AND "isActive" = true
  );

-- Tenants: Only ADMIN staff can update
CREATE POLICY "tenant_update_admin" ON "Tenant"
  FOR UPDATE
  USING (
    id = auth.tenant_id()::UUID 
    AND auth.user_role() = 'staff' 
    AND auth.staff_role() = 'ADMIN'
  )
  WITH CHECK (
    id = auth.tenant_id()::UUID 
    AND "isDeleted" = false
  );

-- =====================================================
-- USER POLICIES
-- =====================================================

-- Users: Staff can view all users in their tenant
CREATE POLICY "user_select_staff" ON "User"
  FOR SELECT
  USING (
    "tenantId" = auth.tenant_id()::UUID 
    AND auth.user_role() = 'staff'
  );

-- Users: Customers can only view themselves
CREATE POLICY "user_select_self" ON "User"
  FOR SELECT
  USING (
    "tenantId" = auth.tenant_id()::UUID 
    AND id = auth.user_id() 
    AND auth.user_role() = 'customer'
  );

-- Users: Anonymous users can be created (for guest checkout)
CREATE POLICY "user_insert_guest" ON "User"
  FOR INSERT
  WITH CHECK (
    "tenantId" = auth.tenant_id()::UUID 
    AND "isGuest" = true
  );

-- Users: Customers can update their own profile
CREATE POLICY "user_update_self" ON "User"
  FOR UPDATE
  USING (
    "tenantId" = auth.tenant_id()::UUID 
    AND id = auth.user_id() 
    AND auth.user_role() = 'customer'
  )
  WITH CHECK (
    "tenantId" = auth.tenant_id()::UUID 
    AND id = auth.user_id()
  );

-- =====================================================
-- ROOM POLICIES
-- =====================================================

-- Rooms: Public read via QR code (anonymous access allowed)
CREATE POLICY "room_select_public" ON "Room"
  FOR SELECT
  USING (
    -- Either authenticated in the same tenant OR accessing via QR code
    "tenantId" = auth.tenant_id()::UUID 
    OR EXISTS (
      SELECT 1 FROM "Tenant" t 
      WHERE t.id = "Room"."tenantId" 
      AND t."isActive" = true 
      AND t."isDeleted" = false
    )
  );

-- Rooms: Staff can manage rooms
CREATE POLICY "room_insert_staff" ON "Room"
  FOR INSERT
  WITH CHECK (
    "tenantId" = auth.tenant_id()::UUID 
    AND auth.user_role() = 'staff'
    AND auth.staff_role() IN ('ADMIN', 'MANAGER')
  );

CREATE POLICY "room_update_staff" ON "Room"
  FOR UPDATE
  USING (
    "tenantId" = auth.tenant_id()::UUID 
    AND auth.user_role() = 'staff'
  )
  WITH CHECK (
    "tenantId" = auth.tenant_id()::UUID
  );

-- =====================================================
-- MENU ITEM POLICIES
-- =====================================================

-- MenuItems: Public read for active items (for QR code access)
CREATE POLICY "menuitem_select_public" ON "MenuItem"
  FOR SELECT
  USING (
    "isAvailable" = true
    AND EXISTS (
      SELECT 1 FROM "Tenant" t 
      WHERE t.id = "MenuItem"."tenantId" 
      AND t."isActive" = true 
      AND t."isDeleted" = false
    )
  );

-- MenuItems: Staff can manage all items in their tenant
CREATE POLICY "menuitem_all_staff" ON "MenuItem"
  FOR ALL
  USING (
    "tenantId" = auth.tenant_id()::UUID 
    AND auth.user_role() = 'staff'
    AND auth.staff_role() IN ('ADMIN', 'MANAGER', 'CHEF')
  )
  WITH CHECK (
    "tenantId" = auth.tenant_id()::UUID
  );

-- =====================================================
-- ALLERGEN POLICIES
-- =====================================================

-- Allergens: Public read (important for safety)
CREATE POLICY "allergen_select_public" ON "Allergen"
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM "Tenant" t 
      WHERE t.id = "Allergen"."tenantId" 
      AND t."isActive" = true 
      AND t."isDeleted" = false
    )
  );

-- Allergens: Staff can manage
CREATE POLICY "allergen_all_staff" ON "Allergen"
  FOR ALL
  USING (
    "tenantId" = auth.tenant_id()::UUID 
    AND auth.user_role() = 'staff'
    AND auth.staff_role() IN ('ADMIN', 'MANAGER', 'CHEF')
  )
  WITH CHECK (
    "tenantId" = auth.tenant_id()::UUID
  );

-- =====================================================
-- MENU ITEM ALLERGEN POLICIES
-- =====================================================

-- MenuItemAllergen: Public read (follows MenuItem visibility)
CREATE POLICY "menuitemallergen_select_public" ON "MenuItemAllergen"
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM "MenuItem" m 
      WHERE m.id = "MenuItemAllergen"."menuItemId" 
      AND m."isAvailable" = true
    )
  );

-- MenuItemAllergen: Staff can manage
CREATE POLICY "menuitemallergen_all_staff" ON "MenuItemAllergen"
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM "MenuItem" m 
      WHERE m.id = "MenuItemAllergen"."menuItemId" 
      AND m."tenantId" = auth.tenant_id()::UUID
    )
    AND auth.user_role() = 'staff'
    AND auth.staff_role() IN ('ADMIN', 'MANAGER', 'CHEF')
  );

-- =====================================================
-- ORDER POLICIES
-- =====================================================

-- Orders: Customers can view their own orders
CREATE POLICY "order_select_customer" ON "Order"
  FOR SELECT
  USING (
    "tenantId" = auth.tenant_id()::UUID 
    AND "userId" = auth.user_id() 
    AND auth.user_role() = 'customer'
  );

-- Orders: Staff can view all orders in their tenant
CREATE POLICY "order_select_staff" ON "Order"
  FOR SELECT
  USING (
    "tenantId" = auth.tenant_id()::UUID 
    AND auth.user_role() = 'staff'
  );

-- Orders: Customers can create orders
CREATE POLICY "order_insert_customer" ON "Order"
  FOR INSERT
  WITH CHECK (
    "tenantId" = auth.tenant_id()::UUID 
    AND "userId" = auth.user_id()
  );

-- Orders: Staff can update orders (status changes, assignments)
CREATE POLICY "order_update_staff" ON "Order"
  FOR UPDATE
  USING (
    "tenantId" = auth.tenant_id()::UUID 
    AND auth.user_role() = 'staff'
  )
  WITH CHECK (
    "tenantId" = auth.tenant_id()::UUID
  );

-- Orders: Customers can cancel their pending orders
CREATE POLICY "order_update_customer_cancel" ON "Order"
  FOR UPDATE
  USING (
    "tenantId" = auth.tenant_id()::UUID 
    AND "userId" = auth.user_id() 
    AND auth.user_role() = 'customer'
    AND status = 'PENDING'
  )
  WITH CHECK (
    "tenantId" = auth.tenant_id()::UUID 
    AND "userId" = auth.user_id()
    AND status = 'CANCELLED'
  );

-- =====================================================
-- ORDER ITEM POLICIES
-- =====================================================

-- OrderItems: Follow order visibility
CREATE POLICY "orderitem_select_via_order" ON "OrderItem"
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM "Order" o 
      WHERE o.id = "OrderItem"."orderId"
      AND (
        -- Customer viewing their own order
        (o."userId" = auth.user_id() AND auth.user_role() = 'customer')
        -- Staff viewing any order in their tenant
        OR (o."tenantId" = auth.tenant_id()::UUID AND auth.user_role() = 'staff')
      )
    )
  );

-- OrderItems: Can be created with orders
CREATE POLICY "orderitem_insert_with_order" ON "OrderItem"
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM "Order" o 
      WHERE o.id = "OrderItem"."orderId"
      AND o."tenantId" = auth.tenant_id()::UUID
      AND o."userId" = auth.user_id()
    )
  );

-- OrderItems: Staff can update (status changes)
CREATE POLICY "orderitem_update_staff" ON "OrderItem"
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM "Order" o 
      WHERE o.id = "OrderItem"."orderId"
      AND o."tenantId" = auth.tenant_id()::UUID
    )
    AND auth.user_role() = 'staff'
  );

-- =====================================================
-- ORDER EVENT POLICIES
-- =====================================================

-- OrderEvents: Read-only audit trail, same visibility as orders
CREATE POLICY "orderevent_select" ON "OrderEvent"
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM "Order" o 
      WHERE o.id = "OrderEvent"."orderId"
      AND (
        -- Customer viewing their own order events
        (o."userId" = auth.user_id() AND auth.user_role() = 'customer')
        -- Staff viewing any order events in their tenant
        OR (o."tenantId" = auth.tenant_id()::UUID AND auth.user_role() = 'staff')
      )
    )
  );

-- OrderEvents: Only system can insert (via triggers/functions)
CREATE POLICY "orderevent_insert_system" ON "OrderEvent"
  FOR INSERT
  WITH CHECK (
    "tenantId" = auth.tenant_id()::UUID
    AND (
      -- Staff members can create events
      auth.user_role() = 'staff'
      -- OR system triggers (when staffId is null)
      OR "staffId" IS NULL
    )
  );

-- =====================================================
-- STAFF POLICIES
-- =====================================================

-- Staff: Only viewable by other staff in same tenant
CREATE POLICY "staff_select_staff" ON "Staff"
  FOR SELECT
  USING (
    "tenantId" = auth.tenant_id()::UUID 
    AND auth.user_role() = 'staff'
  );

-- Staff: Only ADMIN/MANAGER can create/update staff
CREATE POLICY "staff_insert_admin" ON "Staff"
  FOR INSERT
  WITH CHECK (
    "tenantId" = auth.tenant_id()::UUID 
    AND auth.user_role() = 'staff'
    AND auth.staff_role() IN ('ADMIN', 'MANAGER')
  );

CREATE POLICY "staff_update_admin" ON "Staff"
  FOR UPDATE
  USING (
    "tenantId" = auth.tenant_id()::UUID 
    AND auth.user_role() = 'staff'
    AND auth.staff_role() IN ('ADMIN', 'MANAGER')
  )
  WITH CHECK (
    "tenantId" = auth.tenant_id()::UUID
  );

-- Staff: Can update their own last login
CREATE POLICY "staff_update_own_login" ON "Staff"
  FOR UPDATE
  USING (
    "tenantId" = auth.tenant_id()::UUID 
    AND id = auth.user_id() 
    AND auth.user_role() = 'staff'
  )
  WITH CHECK (
    "tenantId" = auth.tenant_id()::UUID 
    AND id = auth.user_id()
  );

-- =====================================================
-- HELPER FUNCTIONS FOR COMMON OPERATIONS
-- =====================================================

-- Function to check if user has access to a specific room
CREATE OR REPLACE FUNCTION has_room_access(room_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM "Room" r
    JOIN "Tenant" t ON t.id = r."tenantId"
    WHERE r.id = room_id
    AND t."isActive" = true
    AND t."isDeleted" = false
    AND (
      -- Authenticated user in same tenant
      r."tenantId" = auth.tenant_id()::UUID
      -- OR public access (for QR code scanning)
      OR auth.user_role() = 'anonymous'
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate menu item access for ordering
CREATE OR REPLACE FUNCTION can_order_menu_item(menu_item_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM "MenuItem" m
    JOIN "Tenant" t ON t.id = m."tenantId"
    WHERE m.id = menu_item_id
    AND m."isAvailable" = true
    AND t."isActive" = true
    AND t."isDeleted" = false
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions to authenticated users
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Grant limited permissions to anonymous users (for QR code access)
GRANT USAGE ON SCHEMA public TO anon;
GRANT SELECT ON "Tenant", "Room", "MenuItem", "Allergen", "MenuItemAllergen" TO anon;
GRANT INSERT ON "User", "Order", "OrderItem" TO anon;
GRANT EXECUTE ON FUNCTION has_room_access(UUID) TO anon;
GRANT EXECUTE ON FUNCTION can_order_menu_item(UUID) TO anon;
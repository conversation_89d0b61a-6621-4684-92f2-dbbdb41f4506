{"name": "b<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Digital menu and ordering system for restaurants and hotels", "scripts": {"test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "test:python": "python3 -m pytest tests/ -v", "test:all": "npm run test && npm run test:python", "lint": "eslint src/**/*.{ts,tsx} --fix", "type-check": "tsc --noEmit"}, "dependencies": {"html2canvas": "^1.4.1", "immer": "^10.1.1", "jspdf": "^3.0.1", "lucide-react": "^0.294.0", "next": "^15.3.5", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-qr-code": "^2.0.18", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.10.4", "@types/qrcode": "^1.5.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "eslint": "^8.55.0", "jsdom": "^23.0.1", "typescript": "^5.3.3", "vitest": "^1.0.4"}}
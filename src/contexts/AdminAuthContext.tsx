// Admin Auth Context Provider for TapDine tenant admin dashboard
'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { tenantAdminAuth } from '@/lib/auth/admin-auth';
import type { 
  AuthContextType, 
  AuthState, 
  AuthUser, 
  LoginCredentials, 
  AuthResponse,
  TenantInfo,
  AdminPermission,
  ADMIN_PERMISSIONS
} from '@/types/auth';

// Create context
const AdminAuthContext = createContext<AuthContextType | null>(null);

// Provider props
interface AdminAuthProviderProps {
  children: React.ReactNode;
  redirectTo?: string; // Where to redirect after login
  publicRoutes?: string[]; // Routes that don't require auth
}

export function AdminAuthProvider({ 
  children, 
  redirectTo = '/admin/dashboard',
  publicRoutes = ['/admin/login', '/admin/forgot-password', '/admin/reset-password']
}: AdminAuthProviderProps) {
  
  // =====================================================
  // STATE MANAGEMENT
  // =====================================================
  
  const router = useRouter();
  
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    loading: true,
    error: null,
    isAuthenticated: false,
    isAdmin: false,
    isTenantAdmin: false,
    currentTenant: null,
    permissions: [],
    adminRole: null,
  });

  // =====================================================
  // AUTH STATE HELPERS
  // =====================================================
  
  const updateAuthState = useCallback((updates: Partial<AuthState>) => {
    setAuthState(prevState => ({ ...prevState, ...updates }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    updateAuthState({ loading });
  }, [updateAuthState]);

  const setError = useCallback((error: string | null) => {
    updateAuthState({ error });
  }, [updateAuthState]);

  const setUserAndSession = useCallback(async (user: AuthUser | null) => {
    if (!user) {
      updateAuthState({
        user: null,
        session: null,
        isAuthenticated: false,
        isAdmin: false,
        isTenantAdmin: false,
        currentTenant: null,
        permissions: [],
        adminRole: null,
        error: null
      });
      return;
    }

    // Get current session
    const session = await tenantAdminAuth.getSession();
    
    // Determine user permissions and roles
    const isAdmin = await tenantAdminAuth.hasRole(user, 'admin');
    const isTenantAdmin = await tenantAdminAuth.hasRole(user, 'tenant_admin');
    const adminRole = user.claims.admin_role;
    
    // Get all permissions for this user
    const allPermissions = Object.values(ADMIN_PERMISSIONS);
    const permissions: AdminPermission[] = [];
    
    for (const permission of allPermissions) {
      if (await tenantAdminAuth.hasPermission(user, permission)) {
        permissions.push(permission);
      }
    }

    // Get current tenant info
    let currentTenant: TenantInfo | null = null;
    if (user.claims.tenant_id) {
      const tenants = await tenantAdminAuth.getAccessibleTenants(user.id);
      currentTenant = tenants.find(t => t.id === user.claims.tenant_id) || null;
    }

    updateAuthState({
      user,
      session,
      isAuthenticated: true,
      isAdmin,
      isTenantAdmin,
      currentTenant,
      permissions,
      adminRole,
      error: null
    });
  }, [updateAuthState]);

  // =====================================================
  // AUTHENTICATION METHODS
  // =====================================================

  const login = useCallback(async (credentials: LoginCredentials): Promise<AuthResponse<AuthUser>> => {
    setLoading(true);
    setError(null);

    try {
      const result = await tenantAdminAuth.login(credentials);
      
      if (result.error) {
        setError(result.error);
        return result;
      }

      if (result.data) {
        await setUserAndSession(result.data);
        
        // Redirect to intended destination
        const intendedRoute = sessionStorage.getItem('intendedRoute') || redirectTo;
        sessionStorage.removeItem('intendedRoute');
        router.push(intendedRoute);
      }

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      setError(errorMessage);
      return { error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [router, redirectTo, setLoading, setError, setUserAndSession]);

  const logout = useCallback(async (): Promise<void> => {
    setLoading(true);
    
    try {
      await tenantAdminAuth.logout();
      await setUserAndSession(null);
      router.push('/admin/login');
    } catch (error) {
      console.error('Logout error:', error);
      // Force state reset even if logout fails
      await setUserAndSession(null);
      router.push('/admin/login');
    } finally {
      setLoading(false);
    }
  }, [router, setLoading, setUserAndSession]);

  const refreshSession = useCallback(async (): Promise<void> => {
    try {
      const result = await tenantAdminAuth.refreshSession();
      
      if (result.error) {
        setError(result.error);
        await setUserAndSession(null);
        return;
      }

      if (result.data) {
        await setUserAndSession(result.data);
      }
    } catch (error) {
      console.error('Session refresh error:', error);
      await setUserAndSession(null);
    }
  }, [setError, setUserAndSession]);

  const updateProfile = useCallback(async (updates: Partial<AuthUser>): Promise<AuthResponse<AuthUser>> => {
    if (!authState.user) {
      return { error: 'No authenticated user' };
    }

    try {
      // In production, this would call an API to update the user profile
      // For now, we'll just update the local state
      const updatedUser = { ...authState.user, ...updates };
      await setUserAndSession(updatedUser);
      
      return { data: updatedUser };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Profile update failed';
      return { error: errorMessage };
    }
  }, [authState.user, setUserAndSession]);

  // =====================================================
  // ROLE & PERMISSION METHODS
  // =====================================================

  const hasRole = useCallback((role: string): boolean => {
    if (!authState.user) return false;
    
    const { claims } = authState.user;
    
    if (role === 'admin' || role === 'tenant_admin') {
      return claims.admin_role === 'TENANT_ADMIN' || claims.admin_role === 'SUPER_ADMIN';
    }
    
    if (role === 'super_admin') {
      return claims.admin_role === 'SUPER_ADMIN';
    }
    
    return claims.admin_role === role || claims.staff_role === role || claims.role === role;
  }, [authState.user]);

  const hasPermission = useCallback((permission: AdminPermission): boolean => {
    return authState.permissions.includes(permission);
  }, [authState.permissions]);

  const hasAnyPermission = useCallback((permissions: AdminPermission[]): boolean => {
    return permissions.some(permission => authState.permissions.includes(permission));
  }, [authState.permissions]);

  const hasAllPermissions = useCallback((permissions: AdminPermission[]): boolean => {
    return permissions.every(permission => authState.permissions.includes(permission));
  }, [authState.permissions]);

  // =====================================================
  // TENANT MANAGEMENT
  // =====================================================

  const switchTenant = useCallback(async (tenantId: string): Promise<AuthResponse<boolean>> => {
    if (!authState.user) {
      return { error: 'No authenticated user' };
    }

    setLoading(true);
    
    try {
      const result = await tenantAdminAuth.switchTenantContext(authState.user.id, tenantId);
      
      if (result.error) {
        setError(result.error);
        return result;
      }

      // Refresh user state with new tenant context
      await refreshSession();
      
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Tenant switch failed';
      setError(errorMessage);
      return { error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [authState.user, setLoading, setError, refreshSession]);

  const getAccessibleTenants = useCallback(async (): Promise<TenantInfo[]> => {
    if (!authState.user) return [];
    
    try {
      return await tenantAdminAuth.getAccessibleTenants(authState.user.id);
    } catch (error) {
      console.error('Error fetching accessible tenants:', error);
      return [];
    }
  }, [authState.user]);

  // =====================================================
  // SECURITY METHODS
  // =====================================================

  const requireReauth = useCallback(async (): Promise<boolean> => {
    // This would implement re-authentication for sensitive actions
    // For now, we'll just check if the session is still valid
    if (!authState.session) return false;
    
    try {
      const result = await tenantAdminAuth.refreshSession();
      return !result.error;
    } catch {
      return false;
    }
  }, [authState.session]);

  const verifyPassword = useCallback(async (password: string): Promise<boolean> => {
    if (!authState.user) return false;
    
    try {
      // In production, this would verify the password against the current user
      // For now, we'll simulate a verification
      return password.length >= 8;
    } catch {
      return false;
    }
  }, [authState.user]);

  const extendSession = useCallback(async (): Promise<void> => {
    try {
      await tenantAdminAuth.refreshSession();
    } catch (error) {
      console.error('Session extension failed:', error);
    }
  }, []);

  const checkSessionExpiry = useCallback(async (): Promise<boolean> => {
    if (!authState.session) return true;
    
    try {
      const expiresAt = authState.session.expires_at;
      if (!expiresAt) return false;
      
      const now = Math.floor(Date.now() / 1000);
      const expiry = expiresAt;
      
      // Check if session expires within 5 minutes
      return (expiry - now) < 300;
    } catch {
      return true;
    }
  }, [authState.session]);

  // =====================================================
  // INITIALIZATION & AUTH STATE LISTENING
  // =====================================================

  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        const currentUser = await tenantAdminAuth.getCurrentUser();
        
        if (mounted) {
          if (currentUser) {
            await setUserAndSession(currentUser);
          } else {
            updateAuthState({
              user: null,
              session: null,
              isAuthenticated: false,
              isAdmin: false,
              isTenantAdmin: false,
              currentTenant: null,
              permissions: [],
              adminRole: null,
              loading: false
            });
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (mounted) {
          setError('Authentication initialization failed');
          setLoading(false);
        }
      }
    };

    initializeAuth();

    // Listen for auth state changes
    const { data: { subscription } } = tenantAdminAuth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return;

        console.log('Auth state change:', event, session);

        if (event === 'SIGNED_OUT' || !session) {
          await setUserAndSession(null);
        } else if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          const user = await tenantAdminAuth.getCurrentUser();
          if (user) {
            await setUserAndSession(user);
          }
        }
      }
    );

    return () => {
      mounted = false;
      subscription?.unsubscribe();
    };
  }, [setUserAndSession, updateAuthState, setError, setLoading]);

  // Session expiry checking
  useEffect(() => {
    if (!authState.isAuthenticated) return;

    const interval = setInterval(async () => {
      const isExpiring = await checkSessionExpiry();
      if (isExpiring) {
        // Show warning or auto-refresh
        await extendSession();
      }
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [authState.isAuthenticated, checkSessionExpiry, extendSession]);

  // Route protection
  useEffect(() => {
    if (authState.loading) return;

    const currentPath = window.location.pathname;
    const isPublicRoute = publicRoutes.some(route => currentPath.startsWith(route));

    if (!authState.isAuthenticated && !isPublicRoute) {
      // Store intended route for redirect after login
      sessionStorage.setItem('intendedRoute', currentPath);
      router.push('/admin/login');
    } else if (authState.isAuthenticated && currentPath === '/admin/login') {
      // Redirect logged-in users away from login page
      const intendedRoute = sessionStorage.getItem('intendedRoute') || redirectTo;
      sessionStorage.removeItem('intendedRoute');
      router.push(intendedRoute);
    }
  }, [authState.isAuthenticated, authState.loading, router, publicRoutes, redirectTo]);

  // =====================================================
  // CONTEXT VALUE
  // =====================================================

  const contextValue: AuthContextType = {
    authState,
    login,
    logout,
    refreshSession,
    updateProfile,
    hasRole,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    switchTenant,
    getAccessibleTenants,
    requireReauth,
    verifyPassword,
    extendSession,
    checkSessionExpiry,
  };

  return (
    <AdminAuthContext.Provider value={contextValue}>
      {children}
    </AdminAuthContext.Provider>
  );
}

// =====================================================
// HOOK FOR CONSUMING CONTEXT
// =====================================================

export function useAdminAuth(): AuthContextType {
  const context = useContext(AdminAuthContext);
  
  if (!context) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  
  return context;
}

// =====================================================
// CONVENIENCE HOOKS
// =====================================================

export function useAuthState() {
  const { authState } = useAdminAuth();
  return authState;
}

export function useAuthUser() {
  const { authState } = useAdminAuth();
  return authState.user;
}

export function useAuthLoading() {
  const { authState } = useAdminAuth();
  return authState.loading;
}

export function useAuthError() {
  const { authState } = useAdminAuth();
  return authState.error;
}

export function useIsAuthenticated() {
  const { authState } = useAdminAuth();
  return authState.isAuthenticated;
}

export function useIsAdmin() {
  const { authState } = useAdminAuth();
  return authState.isAdmin;
}

export function usePermissions() {
  const { authState } = useAdminAuth();
  return authState.permissions;
}

export function useCurrentTenant() {
  const { authState } = useAdminAuth();
  return authState.currentTenant;
}
// BHEEMDINE Menu Store - Zustand state management for menu and cart
// Handles menu items, cart operations, filtering, and user preferences

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// =====================================================
// TYPES & INTERFACES
// =====================================================

export interface MenuItem {
  id: string;
  name: string;
  description?: string;
  category: string;
  pricing: {
    basePrice: number;
    currentPrice: number;
    discountPercentage?: number;
    appliedRules: string[];
  };
  availability: {
    isAvailable: boolean;
    remainingQuantity?: number;
    estimatedWaitTime?: number;
    unavailableReason?: string;
  };
  imageUrl?: string;
  isVegetarian: boolean;
  isVegan: boolean;
  preparationTime: number;
  tags: string[];
  allergens: Array<{
    id: string;
    name: string;
    icon?: string;
    severity: 'CONTAINS' | 'MAY_CONTAIN' | 'TRACE';
  }>;
  sortOrder: number;
}

export interface CartItem {
  id: string; // Unique cart item ID
  menuItemId: string;
  menuItem: MenuItem;
  quantity: number;
  unitPrice: number; // Price at time of adding to cart
  totalPrice: number;
  customizations?: {
    notes?: string;
    modifications?: string[];
  };
  addedAt: Date;
}

export interface MenuFilters {
  categories: string[];
  dietary: {
    vegetarian: boolean;
    vegan: boolean;
    glutenFree: boolean;
  };
  priceRange: {
    min: number;
    max: number;
  };
  excludeAllergens: string[];
  searchQuery: string;
  availableOnly: boolean;
  tags: string[];
}

export interface MenuState {
  // Menu data
  menuItems: MenuItem[];
  categories: string[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  
  // Cart state
  cartItems: CartItem[];
  cartTotal: number;
  cartCount: number;
  
  // UI state
  selectedCategory: string | null;
  filters: MenuFilters;
  searchQuery: string;
  isFilterModalOpen: boolean;
  selectedItemId: string | null; // For item detail modal
  
  // User preferences (persisted)
  userPreferences: {
    favoriteItems: string[];
    dietaryRestrictions: string[];
    defaultFilters: Partial<MenuFilters>;
  };
}

export interface MenuActions {
  // Menu actions
  setMenuItems: (items: MenuItem[]) => void;
  setCategories: (categories: string[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  refreshMenu: () => Promise<void>;
  
  // Cart actions
  addToCart: (menuItem: MenuItem, quantity?: number, customizations?: any) => void;
  removeFromCart: (cartItemId: string) => void;
  updateCartItemQuantity: (cartItemId: string, quantity: number) => void;
  clearCart: () => void;
  getCartItemByMenuId: (menuItemId: string) => CartItem | undefined;
  
  // Filter actions
  setSelectedCategory: (category: string | null) => void;
  setSearchQuery: (query: string) => void;
  updateFilters: (filters: Partial<MenuFilters>) => void;
  resetFilters: () => void;
  toggleFilterModal: () => void;
  
  // Item detail actions
  setSelectedItem: (itemId: string | null) => void;
  
  // User preference actions
  toggleFavorite: (itemId: string) => void;
  updateDietaryRestrictions: (restrictions: string[]) => void;
  
  // Computed getters
  getFilteredMenuItems: () => MenuItem[];
  getItemsByCategory: (category: string) => MenuItem[];
  getFavoriteItems: () => MenuItem[];
  getCartSubtotal: () => number;
  getEstimatedWaitTime: () => number;
}

type MenuStore = MenuState & MenuActions;

// =====================================================
// DEFAULT STATE
// =====================================================

const defaultFilters: MenuFilters = {
  categories: [],
  dietary: {
    vegetarian: false,
    vegan: false,
    glutenFree: false,
  },
  priceRange: {
    min: 0,
    max: 1000,
  },
  excludeAllergens: [],
  searchQuery: '',
  availableOnly: true,
  tags: [],
};

const initialState: MenuState = {
  // Menu data
  menuItems: [],
  categories: [],
  isLoading: false,
  error: null,
  lastUpdated: null,
  
  // Cart state
  cartItems: [],
  cartTotal: 0,
  cartCount: 0,
  
  // UI state
  selectedCategory: null,
  filters: defaultFilters,
  searchQuery: '',
  isFilterModalOpen: false,
  selectedItemId: null,
  
  // User preferences
  userPreferences: {
    favoriteItems: [],
    dietaryRestrictions: [],
    defaultFilters: {},
  },
};

// =====================================================
// ZUSTAND STORE
// =====================================================

export const useMenuStore = create<MenuStore>()(
  // Persist cart and user preferences to localStorage
  persist(
    // Use immer for immutable state updates
    immer<MenuStore>((set, get) => ({
      ...initialState,
      
      // =====================================================
      // MENU ACTIONS
      // =====================================================
      
      setMenuItems: (items: MenuItem[]) => {
        set((state) => {
          state.menuItems = items;
          state.lastUpdated = new Date();
          state.error = null;
        });
      },
      
      setCategories: (categories: string[]) => {
        set((state) => {
          state.categories = categories;
        });
      },
      
      setLoading: (loading: boolean) => {
        set((state) => {
          state.isLoading = loading;
        });
      },
      
      setError: (error: string | null) => {
        set((state) => {
          state.error = error;
          state.isLoading = false;
        });
      },
      
      refreshMenu: async () => {
        const { setLoading, setError, setMenuItems } = get();
        
        try {
          setLoading(true);
          
          // In a real app, this would call the API
          // const response = await fetch('/api/menu');
          // const data = await response.json();
          // setMenuItems(data.items);
          
          // Mock implementation
          await new Promise(resolve => setTimeout(resolve, 1000));
          setError(null);
          
        } catch (error) {
          setError(error instanceof Error ? error.message : 'Failed to load menu');
        } finally {
          setLoading(false);
        }
      },
      
      // =====================================================
      // CART ACTIONS
      // =====================================================
      
      addToCart: (menuItem: MenuItem, quantity = 1, customizations = {}) => {
        set((state) => {
          // Check if item already exists in cart with same customizations
          const existingItemIndex = state.cartItems.findIndex(
            item => 
              item.menuItemId === menuItem.id && 
              JSON.stringify(item.customizations) === JSON.stringify(customizations)
          );
          
          if (existingItemIndex >= 0) {
            // Update existing item quantity
            const existingItem = state.cartItems[existingItemIndex];
            existingItem.quantity += quantity;
            existingItem.totalPrice = existingItem.quantity * existingItem.unitPrice;
          } else {
            // Add new cart item
            const cartItem: CartItem = {
              id: `cart_${Date.now()}_${Math.random()}`, // Unique cart item ID
              menuItemId: menuItem.id,
              menuItem,
              quantity,
              unitPrice: menuItem.pricing.currentPrice,
              totalPrice: menuItem.pricing.currentPrice * quantity,
              customizations,
              addedAt: new Date(),
            };
            
            state.cartItems.push(cartItem);
          }
          
          // Update cart totals
          state.cartCount = state.cartItems.reduce((sum, item) => sum + item.quantity, 0);
          state.cartTotal = state.cartItems.reduce((sum, item) => sum + item.totalPrice, 0);
        });
      },
      
      removeFromCart: (cartItemId: string) => {
        set((state) => {
          state.cartItems = state.cartItems.filter(item => item.id !== cartItemId);
          
          // Update cart totals
          state.cartCount = state.cartItems.reduce((sum, item) => sum + item.quantity, 0);
          state.cartTotal = state.cartItems.reduce((sum, item) => sum + item.totalPrice, 0);
        });
      },
      
      updateCartItemQuantity: (cartItemId: string, quantity: number) => {
        set((state) => {
          const item = state.cartItems.find(item => item.id === cartItemId);
          if (item) {
            if (quantity <= 0) {
              // Remove item if quantity is 0 or negative
              state.cartItems = state.cartItems.filter(item => item.id !== cartItemId);
            } else {
              item.quantity = quantity;
              item.totalPrice = item.unitPrice * quantity;
            }
            
            // Update cart totals
            state.cartCount = state.cartItems.reduce((sum, item) => sum + item.quantity, 0);
            state.cartTotal = state.cartItems.reduce((sum, item) => sum + item.totalPrice, 0);
          }
        });
      },
      
      clearCart: () => {
        set((state) => {
          state.cartItems = [];
          state.cartCount = 0;
          state.cartTotal = 0;
        });
      },
      
      getCartItemByMenuId: (menuItemId: string) => {
        const { cartItems } = get();
        return cartItems.find(item => item.menuItemId === menuItemId);
      },
      
      // =====================================================
      // FILTER ACTIONS
      // =====================================================
      
      setSelectedCategory: (category: string | null) => {
        set((state) => {
          state.selectedCategory = category;
        });
      },
      
      setSearchQuery: (query: string) => {
        set((state) => {
          state.searchQuery = query;
          state.filters.searchQuery = query;
        });
      },
      
      updateFilters: (newFilters: Partial<MenuFilters>) => {
        set((state) => {
          state.filters = { ...state.filters, ...newFilters };
        });
      },
      
      resetFilters: () => {
        set((state) => {
          state.filters = defaultFilters;
          state.searchQuery = '';
          state.selectedCategory = null;
        });
      },
      
      toggleFilterModal: () => {
        set((state) => {
          state.isFilterModalOpen = !state.isFilterModalOpen;
        });
      },
      
      // =====================================================
      // ITEM DETAIL ACTIONS
      // =====================================================
      
      setSelectedItem: (itemId: string | null) => {
        set((state) => {
          state.selectedItemId = itemId;
        });
      },
      
      // =====================================================
      // USER PREFERENCE ACTIONS
      // =====================================================
      
      toggleFavorite: (itemId: string) => {
        set((state) => {
          const favorites = state.userPreferences.favoriteItems;
          const index = favorites.indexOf(itemId);
          
          if (index >= 0) {
            favorites.splice(index, 1); // Remove from favorites
          } else {
            favorites.push(itemId); // Add to favorites
          }
        });
      },
      
      updateDietaryRestrictions: (restrictions: string[]) => {
        set((state) => {
          state.userPreferences.dietaryRestrictions = restrictions;
        });
      },
      
      // =====================================================
      // COMPUTED GETTERS
      // =====================================================
      
      getFilteredMenuItems: () => {
        const { menuItems, filters, selectedCategory } = get();
        
        return menuItems.filter(item => {
          // Category filter
          if (selectedCategory && item.category !== selectedCategory) {
            return false;
          }
          
          if (filters.categories.length > 0 && !filters.categories.includes(item.category)) {
            return false;
          }
          
          // Availability filter
          if (filters.availableOnly && !item.availability.isAvailable) {
            return false;
          }
          
          // Dietary filters
          if (filters.dietary.vegetarian && !item.isVegetarian) {
            return false;
          }
          
          if (filters.dietary.vegan && !item.isVegan) {
            return false;
          }
          
          // Price range filter
          if (item.pricing.currentPrice < filters.priceRange.min || 
              item.pricing.currentPrice > filters.priceRange.max) {
            return false;
          }
          
          // Allergen exclusion filter
          if (filters.excludeAllergens.length > 0) {
            const itemAllergens = item.allergens.map(a => a.name.toLowerCase());
            const hasExcludedAllergen = filters.excludeAllergens.some(allergen => 
              itemAllergens.includes(allergen.toLowerCase())
            );
            if (hasExcludedAllergen) {
              return false;
            }
          }
          
          // Search query filter
          if (filters.searchQuery) {
            const query = filters.searchQuery.toLowerCase();
            const matchesName = item.name.toLowerCase().includes(query);
            const matchesDescription = item.description?.toLowerCase().includes(query);
            const matchesTags = item.tags.some(tag => tag.toLowerCase().includes(query));
            
            if (!matchesName && !matchesDescription && !matchesTags) {
              return false;
            }
          }
          
          // Tags filter
          if (filters.tags.length > 0) {
            const hasRequiredTag = filters.tags.some(tag => 
              item.tags.includes(tag)
            );
            if (!hasRequiredTag) {
              return false;
            }
          }
          
          return true;
        }).sort((a, b) => {
          // Sort by sortOrder, then by name
          if (a.sortOrder !== b.sortOrder) {
            return a.sortOrder - b.sortOrder;
          }
          return a.name.localeCompare(b.name);
        });
      },
      
      getItemsByCategory: (category: string) => {
        const { menuItems } = get();
        return menuItems
          .filter(item => item.category === category && item.availability.isAvailable)
          .sort((a, b) => a.sortOrder - b.sortOrder);
      },
      
      getFavoriteItems: () => {
        const { menuItems, userPreferences } = get();
        return menuItems.filter(item => 
          userPreferences.favoriteItems.includes(item.id)
        );
      },
      
      getCartSubtotal: () => {
        const { cartItems } = get();
        return cartItems.reduce((sum, item) => sum + item.totalPrice, 0);
      },
      
      getEstimatedWaitTime: () => {
        const { cartItems } = get();
        if (cartItems.length === 0) return 0;
        
        // Calculate estimated wait time based on items in cart
        const maxPrepTime = Math.max(
          ...cartItems.map(item => item.menuItem.preparationTime)
        );
        
        // Add buffer time based on cart size
        const bufferTime = Math.min(cartItems.length * 2, 10);
        
        return maxPrepTime + bufferTime;
      },
    })),
    {
      name: 'bheemdine-menu-store', // localStorage key
      storage: createJSONStorage(() => localStorage),
      // Only persist cart and user preferences
      partialize: (state) => ({
        cartItems: state.cartItems,
        cartTotal: state.cartTotal,
        cartCount: state.cartCount,
        userPreferences: state.userPreferences,
      }),
    }
  )
);

// =====================================================
// SELECTORS (for performance optimization)
// =====================================================

// Memoized selectors to prevent unnecessary re-renders
export const useFilteredMenuItems = () => 
  useMenuStore(state => state.getFilteredMenuItems());

export const useCartItems = () => 
  useMenuStore(state => state.cartItems);

export const useCartTotal = () => 
  useMenuStore(state => state.cartTotal);

export const useCartCount = () => 
  useMenuStore(state => state.cartCount);

export const useMenuCategories = () => 
  useMenuStore(state => state.categories);

export const useMenuLoading = () => 
  useMenuStore(state => state.isLoading);

export const useSelectedCategory = () => 
  useMenuStore(state => state.selectedCategory);

export const useSearchQuery = () => 
  useMenuStore(state => state.searchQuery);
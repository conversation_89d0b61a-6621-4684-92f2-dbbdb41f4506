// Zustand store for real-time order dashboard state management
// This manages client-side state, UI preferences, and real-time updates

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { persist } from 'zustand/middleware';
import type { 
  RealtimeOrder,
  OrderEvent,
  OrderDashboardState,
  OrderDashboardFilters,
  OrderGroupBy,
  OrderSortBy,
  SortDirection,
  NotificationSettings,
  DashboardMetrics,
  StaffRole,
  OrderStatus,
  NotificationSound
} from '@/types/realtime-orders';

/**
 * Real-time order dashboard store
 * Manages orders, UI state, filters, and real-time synchronization
 */
interface OrderDashboardStore extends OrderDashboardState {
  // ==================== ACTIONS ====================
  
  // Order management
  setOrders: (orders: RealtimeOrder[]) => void;
  addOrder: (order: RealtimeOrder) => void;
  updateOrder: (orderId: string, updates: Partial<RealtimeOrder>) => void;
  removeOrder: (orderId: string) => void;
  
  // Real-time updates
  handleRealtimeOrderUpdate: (order: RealtimeOrder, eventType: 'INSERT' | 'UPDATE' | 'DELETE') => void;
  handleRealtimeOrderEvent: (event: OrderEvent) => void;
  
  // Selection and interaction
  selectOrder: (orderId: string | null) => void;
  toggleOrderSelection: (orderId: string) => void;
  clearSelection: () => void;
  
  // Filtering and sorting
  setFilters: (filters: Partial<OrderDashboardFilters>) => void;
  clearFilters: () => void;
  setGroupBy: (groupBy: OrderGroupBy) => void;
  setSorting: (sortBy: OrderSortBy, direction?: SortDirection) => void;
  
  // UI state
  setConnectionStatus: (isConnected: boolean, error?: string) => void;
  updateMetrics: (metrics: Partial<DashboardMetrics>) => void;
  
  // ==================== COMPUTED GETTERS ====================
  
  // Get filtered and sorted orders
  getFilteredOrders: () => RealtimeOrder[];
  getOrdersByStatus: () => Record<OrderStatus, RealtimeOrder[]>;
  getOrdersByStaff: () => Record<string, RealtimeOrder[]>;
  getOrdersByRoom: () => Record<string, RealtimeOrder[]>;
  getUrgentOrders: () => RealtimeOrder[];
  
  // Get selected orders
  getSelectedOrders: () => RealtimeOrder[];
  getSelectedOrder: () => RealtimeOrder | null;
  
  // Get dashboard metrics
  getDashboardMetrics: () => DashboardMetrics;
}

/**
 * Sound notification manager
 * Handles audio feedback for real-time events
 */
class SoundManager {
  private audioContext: AudioContext | null = null;
  private sounds: Map<NotificationSound, HTMLAudioElement> = new Map();
  private volume = 0.7;
  
  constructor() {
    this.initializeSounds();
  }
  
  private initializeSounds() {
    // Initialize Web Audio API for better sound control
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (error) {
      console.warn('Web Audio API not supported, falling back to HTML Audio');
    }
    
    // Define sound URLs (you would replace these with actual sound files)
    const soundUrls: Record<NotificationSound, string> = {
      NEW_ORDER: '/sounds/new-order.mp3',
      STATUS_CHANGE: '/sounds/status-change.mp3',
      URGENT_ORDER: '/sounds/urgent-alert.mp3',
      READY_FOR_PICKUP: '/sounds/ready-pickup.mp3',
      ERROR: '/sounds/error.mp3',
    };
    
    // Pre-load all sounds
    Object.entries(soundUrls).forEach(([sound, url]) => {
      const audio = new Audio(url);
      audio.preload = 'auto';
      audio.volume = this.volume;
      this.sounds.set(sound as NotificationSound, audio);
    });
  }
  
  setVolume(volume: number) {
    this.volume = Math.max(0, Math.min(1, volume));
    this.sounds.forEach(audio => {
      audio.volume = this.volume;
    });
  }
  
  play(sound: NotificationSound) {
    const audio = this.sounds.get(sound);
    if (audio) {
      // Reset playback position and play
      audio.currentTime = 0;
      audio.play().catch(error => {
        console.warn(`Failed to play sound ${sound}:`, error);
      });
    }
  }
}

// Global sound manager instance
const soundManager = new SoundManager();

/**
 * Default notification settings
 */
const defaultNotificationSettings: NotificationSettings = {
  enableSounds: true,
  enableBrowserNotifications: true,
  enableUrgentAlerts: true,
  soundVolume: 0.7,
  urgentThresholdMinutes: 30,
  roleSettings: {
    ADMIN: {
      notifyOnStatuses: ['PENDING', 'CONFIRMED', 'PREPARING', 'READY', 'DELIVERED'],
      notifyOnEvents: ['ORDER_CREATED', 'STATUS_CHANGED', 'ASSIGNED_TO_STAFF'],
    },
    MANAGER: {
      notifyOnStatuses: ['PENDING', 'CONFIRMED', 'PREPARING', 'READY'],
      notifyOnEvents: ['ORDER_CREATED', 'STATUS_CHANGED', 'ASSIGNED_TO_STAFF'],
    },
    CHEF: {
      notifyOnStatuses: ['CONFIRMED', 'PREPARING'],
      notifyOnEvents: ['ORDER_CREATED', 'STATUS_CHANGED'],
    },
    WAITER: {
      notifyOnStatuses: ['READY'],
      notifyOnEvents: ['STATUS_CHANGED'],
    },
    RECEPTIONIST: {
      notifyOnStatuses: ['PENDING', 'DELIVERED'],
      notifyOnEvents: ['ORDER_CREATED', 'STATUS_CHANGED'],
    },
  },
};

/**
 * Create the order dashboard store with persistence
 */
export const useOrderDashboardStore = create<OrderDashboardStore>()(
  subscribeWithSelector(
    persist(
      (set, get) => ({
        // ==================== INITIAL STATE ====================
        
        orders: new Map(),
        ordersById: {},
        ordersByStatus: {
          PENDING: [],
          CONFIRMED: [],
          PREPARING: [],
          READY: [],
          DELIVERED: [],
          CANCELLED: [],
        },
        
        // Connection state
        isConnected: false,
        connectionError: undefined,
        lastUpdated: undefined,
        
        // UI state
        filters: {},
        groupBy: 'status',
        sortBy: 'created_at',
        sortDirection: 'desc',
        selectedOrderId: undefined,
        
        // Performance tracking
        updateCount: 0,
        subscriptionChannels: [],
        
        // ==================== ACTIONS ====================
        
        setOrders: (orders: RealtimeOrder[]) => {
          set((state) => {
            const ordersMap = new Map(orders.map(order => [order.id, order]));
            const ordersById = Object.fromEntries(orders.map(order => [order.id, order]));
            
            // Group orders by status
            const ordersByStatus = orders.reduce((acc, order) => {
              if (!acc[order.status]) acc[order.status] = [];
              acc[order.status].push(order.id);
              return acc;
            }, {} as Record<OrderStatus, string[]>);
            
            return {
              ...state,
              orders: ordersMap,
              ordersById,
              ordersByStatus: {
                PENDING: [],
                CONFIRMED: [],
                PREPARING: [],
                READY: [],
                DELIVERED: [],
                CANCELLED: [],
                ...ordersByStatus,
              },
              lastUpdated: new Date().toISOString(),
            };
          });
        },
        
        addOrder: (order: RealtimeOrder) => {
          set((state) => {
            const newOrders = new Map(state.orders);
            newOrders.set(order.id, order);
            
            const newOrdersById = { ...state.ordersById, [order.id]: order };
            
            const newOrdersByStatus = { ...state.ordersByStatus };
            newOrdersByStatus[order.status] = [...newOrdersByStatus[order.status], order.id];
            
            console.log(`➕ Added order ${order.order_number} to store`);
            
            return {
              ...state,
              orders: newOrders,
              ordersById: newOrdersById,
              ordersByStatus: newOrdersByStatus,
              updateCount: state.updateCount + 1,
              lastUpdated: new Date().toISOString(),
            };
          });
        },
        
        updateOrder: (orderId: string, updates: Partial<RealtimeOrder>) => {
          set((state) => {
            const existingOrder = state.orders.get(orderId);
            if (!existingOrder) {
              console.warn(`Order ${orderId} not found for update`);
              return state;
            }
            
            const updatedOrder = { ...existingOrder, ...updates };
            const newOrders = new Map(state.orders);
            newOrders.set(orderId, updatedOrder);
            
            const newOrdersById = { ...state.ordersById, [orderId]: updatedOrder };
            
            // Update status grouping if status changed
            let newOrdersByStatus = { ...state.ordersByStatus };
            if (updates.status && updates.status !== existingOrder.status) {
              // Remove from old status group
              newOrdersByStatus[existingOrder.status] = newOrdersByStatus[existingOrder.status]
                .filter(id => id !== orderId);
              
              // Add to new status group
              newOrdersByStatus[updates.status] = [...newOrdersByStatus[updates.status], orderId];
              
              console.log(`🔄 Order ${existingOrder.order_number} status: ${existingOrder.status} → ${updates.status}`);
            }
            
            return {
              ...state,
              orders: newOrders,
              ordersById: newOrdersById,
              ordersByStatus: newOrdersByStatus,
              updateCount: state.updateCount + 1,
              lastUpdated: new Date().toISOString(),
            };
          });
        },
        
        removeOrder: (orderId: string) => {
          set((state) => {
            const order = state.orders.get(orderId);
            if (!order) return state;
            
            const newOrders = new Map(state.orders);
            newOrders.delete(orderId);
            
            const newOrdersById = { ...state.ordersById };
            delete newOrdersById[orderId];
            
            const newOrdersByStatus = { ...state.ordersByStatus };
            newOrdersByStatus[order.status] = newOrdersByStatus[order.status]
              .filter(id => id !== orderId);
            
            console.log(`🗑️ Removed order ${order.order_number} from store`);
            
            return {
              ...state,
              orders: newOrders,
              ordersById: newOrdersById,
              ordersByStatus: newOrdersByStatus,
              updateCount: state.updateCount + 1,
              lastUpdated: new Date().toISOString(),
            };
          });
        },
        
        handleRealtimeOrderUpdate: (order: RealtimeOrder, eventType: 'INSERT' | 'UPDATE' | 'DELETE') => {
          const { addOrder, updateOrder, removeOrder } = get();
          
          switch (eventType) {
            case 'INSERT':
              addOrder(order);
              
              // Play new order sound
              soundManager.play('NEW_ORDER');
              
              // Browser notification for new orders
              if ('Notification' in window && Notification.permission === 'granted') {
                new Notification(`New Order: ${order.order_number}`, {
                  body: `Order from ${order.customer_name || 'Guest'} - $${order.total_amount.toFixed(2)}`,
                  icon: '/icons/order-notification.png',
                  tag: `order-${order.id}`,
                });
              }
              break;
              
            case 'UPDATE':
              updateOrder(order.id, order);
              
              // Play status change sound
              soundManager.play('STATUS_CHANGE');
              break;
              
            case 'DELETE':
              removeOrder(order.id);
              break;
          }
        },
        
        handleRealtimeOrderEvent: (event: OrderEvent) => {
          console.log(`📋 Order event: ${event.event_type} for order ${event.order_id}`);
          
          // Update the last activity for the order
          const { updateOrder } = get();
          updateOrder(event.order_id, { 
            latest_event: event,
          });
          
          // Handle specific event types
          if (event.event_type === 'STATUS_CHANGED' && event.new_status === 'READY') {
            soundManager.play('READY_FOR_PICKUP');
          }
        },
        
        selectOrder: (orderId: string | null) => {
          set((state) => ({
            ...state,
            selectedOrderId: orderId,
          }));
        },
        
        toggleOrderSelection: (orderId: string) => {
          set((state) => ({
            ...state,
            selectedOrderId: state.selectedOrderId === orderId ? undefined : orderId,
          }));
        },
        
        clearSelection: () => {
          set((state) => ({
            ...state,
            selectedOrderId: undefined,
          }));
        },
        
        setFilters: (filters: Partial<OrderDashboardFilters>) => {
          set((state) => ({
            ...state,
            filters: { ...state.filters, ...filters },
          }));
        },
        
        clearFilters: () => {
          set((state) => ({
            ...state,
            filters: {},
          }));
        },
        
        setGroupBy: (groupBy: OrderGroupBy) => {
          set((state) => ({
            ...state,
            groupBy,
          }));
        },
        
        setSorting: (sortBy: OrderSortBy, direction?: SortDirection) => {
          set((state) => ({
            ...state,
            sortBy,
            sortDirection: direction || (state.sortBy === sortBy && state.sortDirection === 'asc' ? 'desc' : 'asc'),
          }));
        },
        
        setConnectionStatus: (isConnected: boolean, error?: string) => {
          set((state) => ({
            ...state,
            isConnected,
            connectionError: error,
            lastUpdated: new Date().toISOString(),
          }));
        },
        
        updateMetrics: (metrics: Partial<DashboardMetrics>) => {
          // Metrics would be managed separately or as part of state
          console.log('📊 Dashboard metrics updated:', metrics);
        },
        
        // ==================== COMPUTED GETTERS ====================
        
        getFilteredOrders: () => {
          const state = get();
          const orders = Array.from(state.orders.values());
          const { filters } = state;
          
          return orders.filter(order => {
            // Status filter
            if (filters.statuses?.length && !filters.statuses.includes(order.status)) {
              return false;
            }
            
            // Staff assignment filter
            if (filters.staff_assigned?.length && order.staff_assigned_id && 
                !filters.staff_assigned.includes(order.staff_assigned_id)) {
              return false;
            }
            
            // Room filter
            if (filters.room_numbers?.length && order.room_number && 
                !filters.room_numbers.includes(order.room_number)) {
              return false;
            }
            
            // Urgent orders only
            if (filters.is_urgent_only && !order.is_urgent) {
              return false;
            }
            
            // Search query
            if (filters.search_query) {
              const query = filters.search_query.toLowerCase();
              const searchFields = [
                order.order_number,
                order.customer_name,
                order.customer_email,
                order.room_number,
              ].filter(Boolean).join(' ').toLowerCase();
              
              if (!searchFields.includes(query)) {
                return false;
              }
            }
            
            // Date range filter
            if (filters.date_range) {
              const orderDate = new Date(order.created_at);
              const startDate = new Date(filters.date_range.start);
              const endDate = new Date(filters.date_range.end);
              
              if (orderDate < startDate || orderDate > endDate) {
                return false;
              }
            }
            
            return true;
          }).sort((a, b) => {
            const { sortBy, sortDirection } = state;
            const aValue = a[sortBy];
            const bValue = b[sortBy];
            
            let comparison = 0;
            if (aValue < bValue) comparison = -1;
            if (aValue > bValue) comparison = 1;
            
            return sortDirection === 'desc' ? -comparison : comparison;
          });
        },
        
        getOrdersByStatus: () => {
          const filteredOrders = get().getFilteredOrders();
          return filteredOrders.reduce((acc, order) => {
            if (!acc[order.status]) acc[order.status] = [];
            acc[order.status].push(order);
            return acc;
          }, {} as Record<OrderStatus, RealtimeOrder[]>);
        },
        
        getOrdersByStaff: () => {
          const filteredOrders = get().getFilteredOrders();
          return filteredOrders.reduce((acc, order) => {
            const staffId = order.staff_assigned_id || 'unassigned';
            if (!acc[staffId]) acc[staffId] = [];
            acc[staffId].push(order);
            return acc;
          }, {} as Record<string, RealtimeOrder[]>);
        },
        
        getOrdersByRoom: () => {
          const filteredOrders = get().getFilteredOrders();
          return filteredOrders.reduce((acc, order) => {
            const roomNumber = order.room_number || 'walk-in';
            if (!acc[roomNumber]) acc[roomNumber] = [];
            acc[roomNumber].push(order);
            return acc;
          }, {} as Record<string, RealtimeOrder[]>);
        },
        
        getUrgentOrders: () => {
          return get().getFilteredOrders().filter(order => order.is_urgent);
        },
        
        getSelectedOrders: () => {
          const state = get();
          return state.selectedOrderId ? [state.orders.get(state.selectedOrderId)!].filter(Boolean) : [];
        },
        
        getSelectedOrder: () => {
          const state = get();
          return state.selectedOrderId ? state.orders.get(state.selectedOrderId) || null : null;
        },
        
        getDashboardMetrics: () => {
          const orders = Array.from(get().orders.values());
          const now = new Date();
          
          // Calculate orders per minute (last hour)
          const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
          const recentOrders = orders.filter(order => new Date(order.created_at) > oneHourAgo);
          const ordersPerMinute = recentOrders.length / 60;
          
          // Calculate average order time
          const completedOrders = orders.filter(order => order.status === 'DELIVERED' && order.actual_completion_time);
          const averageOrderTime = completedOrders.reduce((acc, order) => {
            const createdTime = new Date(order.created_at).getTime();
            const completedTime = new Date(order.actual_completion_time!).getTime();
            return acc + (completedTime - createdTime);
          }, 0) / completedOrders.length / (1000 * 60); // Convert to minutes
          
          // Status distribution
          const statusDistribution = orders.reduce((acc, order) => {
            acc[order.status] = (acc[order.status] || 0) + 1;
            return acc;
          }, {} as Record<OrderStatus, number>);
          
          // Staff workload
          const staffWorkload = orders
            .filter(order => order.staff_assigned_id && order.status !== 'DELIVERED' && order.status !== 'CANCELLED')
            .reduce((acc, order) => {
              const staffId = order.staff_assigned_id!;
              acc[staffId] = (acc[staffId] || 0) + 1;
              return acc;
            }, {} as Record<string, number>);
          
          return {
            ordersPerMinute,
            averageOrderTime: isNaN(averageOrderTime) ? 0 : averageOrderTime,
            statusDistribution,
            staffWorkload,
            connectionLatency: 0, // Would be calculated from real-time pings
            updateFrequency: get().updateCount / 60, // Rough estimate
          };
        },
      }),
      {
        name: 'order-dashboard-store', // localStorage key
        partialize: (state) => ({
          // Only persist UI preferences, not real-time data
          filters: state.filters,
          groupBy: state.groupBy,
          sortBy: state.sortBy,
          sortDirection: state.sortDirection,
        }),
      }
    )
  )
);

/**
 * Notification settings store (separate from main store for better organization)
 */
export const useNotificationStore = create<{
  settings: NotificationSettings;
  updateSettings: (settings: Partial<NotificationSettings>) => void;
  checkPermissions: () => Promise<void>;
}>()(
  persist(
    (set, get) => ({
      settings: defaultNotificationSettings,
      
      updateSettings: (newSettings: Partial<NotificationSettings>) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
        }));
        
        // Update sound manager volume
        if (newSettings.soundVolume !== undefined) {
          soundManager.setVolume(newSettings.soundVolume);
        }
      },
      
      checkPermissions: async () => {
        if ('Notification' in window && Notification.permission === 'default') {
          const permission = await Notification.requestPermission();
          console.log('Notification permission:', permission);
        }
      },
    }),
    {
      name: 'notification-settings',
    }
  )
);

// Export sound manager for direct access
export { soundManager };
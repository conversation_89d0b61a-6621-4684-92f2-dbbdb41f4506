// Tenant admin dashboard layout with navigation and auth protection
'use client';

import React, { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { 
  LayoutDashboard,
  Menu as MenuIcon,
  ShoppingBag,
  Users,
  Settings,
  BarChart3,
  FileText,
  Bell,
  HelpCircle,
  Building,
  ChefHat,
  CreditCard,
  Shield,
  Zap,
  X
} from 'lucide-react';
import { AdminHeader } from '@/components/auth/AdminHeader';
import { ProtectedRoute, RequirePermission, IfHasPermission } from '@/components/auth/ProtectedRoute';
import { useAuthState } from '@/contexts/AdminAuthContext';
import { ADMIN_PERMISSIONS } from '@/types/auth';

interface AdminDashboardLayoutProps {
  children: React.ReactNode;
  title?: string;
  className?: string;
}

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ElementType;
  permission?: string;
  children?: NavigationItem[];
  badge?: string | number;
}

export function AdminDashboardLayout({ 
  children, 
  title, 
  className = '' 
}: AdminDashboardLayoutProps) {
  
  // =====================================================
  // HOOKS & STATE
  // =====================================================
  
  const router = useRouter();
  const pathname = usePathname();
  const authState = useAuthState();
  
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // =====================================================
  // NAVIGATION CONFIGURATION
  // =====================================================
  
  const navigation: NavigationItem[] = [
    {
      name: 'Dashboard',
      href: '/admin/dashboard',
      icon: LayoutDashboard,
      permission: ADMIN_PERMISSIONS.DASHBOARD_VIEW,
    },
    {
      name: 'Orders',
      href: '/admin/orders',
      icon: ShoppingBag,
      permission: ADMIN_PERMISSIONS.ORDERS_VIEW,
      badge: '3', // Would be dynamic from real data
      children: [
        {
          name: 'All Orders',
          href: '/admin/orders',
          icon: ShoppingBag,
          permission: ADMIN_PERMISSIONS.ORDERS_VIEW,
        },
        {
          name: 'Kitchen Queue',
          href: '/admin/orders/kitchen',
          icon: ChefHat,
          permission: ADMIN_PERMISSIONS.ORDERS_VIEW,
        },
        {
          name: 'Delivery Tracking',
          href: '/admin/orders/delivery',
          icon: Building,
          permission: ADMIN_PERMISSIONS.ORDERS_VIEW,
        },
      ],
    },
    {
      name: 'Menu Management',
      href: '/admin/menu',
      icon: MenuIcon,
      permission: ADMIN_PERMISSIONS.MENU_VIEW,
      children: [
        {
          name: 'Menu Items',
          href: '/admin/menu/items',
          icon: MenuIcon,
          permission: ADMIN_PERMISSIONS.MENU_VIEW,
        },
        {
          name: 'Categories',
          href: '/admin/menu/categories',
          icon: Building,
          permission: ADMIN_PERMISSIONS.MENU_EDIT,
        },
        {
          name: 'Pricing',
          href: '/admin/menu/pricing',
          icon: CreditCard,
          permission: ADMIN_PERMISSIONS.MENU_PRICING,
        },
        {
          name: 'Availability',
          href: '/admin/menu/availability',
          icon: Zap,
          permission: ADMIN_PERMISSIONS.MENU_EDIT,
        },
      ],
    },
    {
      name: 'Staff Management',
      href: '/admin/staff',
      icon: Users,
      permission: ADMIN_PERMISSIONS.STAFF_VIEW,
      children: [
        {
          name: 'All Staff',
          href: '/admin/staff',
          icon: Users,
          permission: ADMIN_PERMISSIONS.STAFF_VIEW,
        },
        {
          name: 'Roles & Permissions',
          href: '/admin/staff/permissions',
          icon: Shield,
          permission: ADMIN_PERMISSIONS.STAFF_PERMISSIONS,
        },
      ],
    },
    {
      name: 'Customers',
      href: '/admin/customers',
      icon: Users,
      permission: ADMIN_PERMISSIONS.CUSTOMERS_VIEW,
    },
    {
      name: 'Analytics',
      href: '/admin/analytics',
      icon: BarChart3,
      permission: ADMIN_PERMISSIONS.ANALYTICS_VIEW,
      children: [
        {
          name: 'Overview',
          href: '/admin/analytics',
          icon: BarChart3,
          permission: ADMIN_PERMISSIONS.ANALYTICS_VIEW,
        },
        {
          name: 'Sales Reports',
          href: '/admin/analytics/sales',
          icon: FileText,
          permission: ADMIN_PERMISSIONS.REPORTS_VIEW,
        },
        {
          name: 'Performance',
          href: '/admin/analytics/performance',
          icon: Zap,
          permission: ADMIN_PERMISSIONS.ANALYTICS_VIEW,
        },
      ],
    },
    {
      name: 'Settings',
      href: '/admin/settings',
      icon: Settings,
      permission: ADMIN_PERMISSIONS.TENANT_SETTINGS,
      children: [
        {
          name: 'General',
          href: '/admin/settings',
          icon: Settings,
          permission: ADMIN_PERMISSIONS.TENANT_SETTINGS,
        },
        {
          name: 'Billing',
          href: '/admin/settings/billing',
          icon: CreditCard,
          permission: ADMIN_PERMISSIONS.TENANT_BILLING,
        },
        {
          name: 'Integrations',
          href: '/admin/settings/integrations',
          icon: Zap,
          permission: ADMIN_PERMISSIONS.TENANT_INTEGRATIONS,
        },
      ],
    },
  ];

  // Add system admin items for super admins
  if (authState.user?.claims.admin_role === 'SUPER_ADMIN') {
    navigation.push({
      name: 'System Admin',
      href: '/admin/system',
      icon: Shield,
      permission: ADMIN_PERMISSIONS.SYSTEM_ADMIN,
      children: [
        {
          name: 'All Tenants',
          href: '/admin/system/tenants',
          icon: Building,
          permission: ADMIN_PERMISSIONS.TENANTS_MANAGE,
        },
        {
          name: 'System Settings',
          href: '/admin/system/settings',
          icon: Settings,
          permission: ADMIN_PERMISSIONS.SYSTEM_ADMIN,
        },
      ],
    });
  }

  // =====================================================
  // HELPER FUNCTIONS
  // =====================================================
  
  const isActiveRoute = (href: string): boolean => {
    if (href === '/admin/dashboard') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  const hasPermissionForItem = (item: NavigationItem): boolean => {
    if (!item.permission) return true;
    return authState.permissions.includes(item.permission as any);
  };

  const getVisibleNavigationItems = (): NavigationItem[] => {
    return navigation.filter(item => hasPermissionForItem(item));
  };

  // =====================================================
  // EVENT HANDLERS
  // =====================================================
  
  const handleNavigate = (href: string) => {
    router.push(href);
    setSidebarOpen(false); // Close mobile sidebar
  };

  const toggleSidebar = () => {
    setSidebarOpen(prev => !prev);
  };

  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderNavigationItem = (item: NavigationItem, isChild = false) => {
    if (!hasPermissionForItem(item)) return null;

    const Icon = item.icon;
    const isActive = isActiveRoute(item.href);
    
    return (
      <div key={item.href}>
        <button
          onClick={() => handleNavigate(item.href)}
          className={`
            group flex items-center w-full px-3 py-2 text-sm font-medium rounded-md transition-colors
            ${isChild ? 'ml-6 pl-8' : ''}
            ${isActive
              ? 'bg-orange-100 text-orange-700 border-r-2 border-orange-600'
              : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
            }
          `}
        >
          <Icon className={`
            mr-3 flex-shrink-0 h-5 w-5 transition-colors
            ${isActive ? 'text-orange-600' : 'text-gray-400 group-hover:text-gray-500'}
          `} />
          
          <span className="flex-1 text-left">{item.name}</span>
          
          {item.badge && (
            <span className={`
              ml-3 inline-block py-0.5 px-2 text-xs rounded-full
              ${isActive 
                ? 'bg-orange-200 text-orange-800' 
                : 'bg-gray-200 text-gray-600'
              }
            `}>
              {item.badge}
            </span>
          )}
        </button>

        {/* Render children if they exist */}
        {item.children && isActive && (
          <div className="mt-1 space-y-1">
            {item.children.map(child => renderNavigationItem(child, true))}
          </div>
        )}
      </div>
    );
  };

  const renderSidebar = () => (
    <div className="flex flex-col w-64 bg-white border-r border-gray-200">
      {/* Logo */}
      <div className="flex items-center h-16 px-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center">
            <Building className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-semibold text-gray-900">TapDine</h1>
            <p className="text-xs text-gray-500">Admin Portal</p>
          </div>
        </div>
      </div>

      {/* Current Tenant Info */}
      {authState.currentTenant && (
        <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Building className="w-4 h-4 text-gray-600" />
            <div>
              <p className="text-sm font-medium text-gray-900">
                {authState.currentTenant.name}
              </p>
              <p className="text-xs text-gray-500">
                {authState.user?.claims.admin_role}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
        {getVisibleNavigationItems().map(item => renderNavigationItem(item))}
      </nav>

      {/* Help & Support */}
      <div className="p-4 border-t border-gray-200">
        <button
          onClick={() => handleNavigate('/admin/help')}
          className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
        >
          <HelpCircle className="mr-3 h-5 w-5 text-gray-400" />
          Help & Support
        </button>
      </div>
    </div>
  );

  const renderMobileSidebar = () => (
    <>
      {/* Overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
      
      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white transform transition-transform duration-300 ease-in-out lg:hidden
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        {/* Close button */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center">
              <Building className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-lg font-semibold text-gray-900">TapDine Admin</h1>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Mobile navigation content */}
        <div className="flex flex-col h-full">
          {/* Current Tenant Info */}
          {authState.currentTenant && (
            <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
              <div className="flex items-center space-x-2">
                <Building className="w-4 h-4 text-gray-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {authState.currentTenant.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {authState.user?.claims.admin_role}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Navigation */}
          <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
            {getVisibleNavigationItems().map(item => renderNavigationItem(item))}
          </nav>

          {/* Help & Support */}
          <div className="p-4 border-t border-gray-200">
            <button
              onClick={() => handleNavigate('/admin/help')}
              className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
            >
              <HelpCircle className="mr-3 h-5 w-5 text-gray-400" />
              Help & Support
            </button>
          </div>
        </div>
      </div>
    </>
  );

  const renderPageHeader = () => {
    if (!title) return null;

    return (
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          </div>
        </div>
      </div>
    );
  };

  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  return (
    <ProtectedRoute
      requireAuth={true}
      roles={['SUPER_ADMIN', 'TENANT_ADMIN', 'MANAGER']}
      permissions={[ADMIN_PERMISSIONS.DASHBOARD_VIEW]}
    >
      <div className={`min-h-screen bg-gray-50 ${className}`}>
        <div className="flex h-screen">
          {/* Desktop Sidebar */}
          <div className="hidden lg:flex lg:flex-shrink-0">
            {renderSidebar()}
          </div>

          {/* Mobile Sidebar */}
          {renderMobileSidebar()}

          {/* Main Content */}
          <div className="flex flex-col flex-1 overflow-hidden">
            {/* Header */}
            <AdminHeader 
              onMenuToggle={toggleSidebar}
              showMenuButton={true}
            />

            {/* Page Header */}
            {renderPageHeader()}

            {/* Main Content Area */}
            <main className="flex-1 overflow-auto">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                {children}
              </div>
            </main>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
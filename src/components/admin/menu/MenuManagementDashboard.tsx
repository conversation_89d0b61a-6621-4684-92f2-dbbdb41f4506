// Complete menu management dashboard with all CRUD operations and bulk actions
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Plus,
  Download,
  Upload,
  Settings,
  BarChart3,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Zap,
  Building,
} from 'lucide-react';
import { useCrud } from '@/hooks/useCrud';
import { MenuItemForm } from './MenuItemForm';
import { CategoryManager } from './CategoryManager';
import { MenuItemsTable } from './MenuItemsTable';
import { AdminDashboardLayout } from '@/components/admin/AdminDashboardLayout';
import { ProtectedRoute, RequirePermission } from '@/components/auth/ProtectedRoute';
import { useAuthState } from '@/contexts/AdminAuthContext';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ADMIN_PERMISSIONS } from '@/types/auth';
import type { 
  MenuItemAdmin, 
  MenuCategoryAdmin, 
  MenuItemFormData, 
  MenuFilters 
} from '@/types/menu-admin';

interface MenuManagementDashboardProps {
  className?: string;
}

type ActiveTab = 'items' | 'categories' | 'analytics';

export function MenuManagementDashboard({ className = '' }: MenuManagementDashboardProps) {
  
  // =====================================================
  // HOOKS & STATE
  // =====================================================
  
  const authState = useAuthState();
  const tenantId = authState.currentTenant?.id || '';
  
  const [menuItemsState, menuItemsActions] = useCrud<MenuItemAdmin>({
    apiEndpoint: `/api/v1/admin/tenants/${tenantId}/menu-items`,
    optimisticUpdates: true,
    onSuccess: (action, item) => {
      if (action === 'create') {
        setSuccessMessage('Menu item created successfully');
      } else if (action === 'update') {
        setSuccessMessage('Menu item updated successfully');
      } else if (action === 'delete') {
        setSuccessMessage('Menu item deleted successfully');
      }
    },
    onError: (action, error) => {
      setErrorMessage(`Failed to ${action} menu item: ${error.message}`);
    },
  });
  
  const [categories, setCategories] = useState<MenuCategoryAdmin[]>([]);
  const [activeTab, setActiveTab] = useState<ActiveTab>('items');
  const [showItemForm, setShowItemForm] = useState(false);
  const [editingItem, setEditingItem] = useState<MenuItemAdmin | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // =====================================================
  // EFFECTS
  // =====================================================
  
  useEffect(() => {
    if (tenantId) {
      loadMenuItems();
      loadCategories();
    }
  }, [tenantId]);
  
  // Auto-hide messages after 5 seconds
  useEffect(() => {
    if (successMessage || errorMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage(null);
        setErrorMessage(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [successMessage, errorMessage]);
  
  // =====================================================
  // DATA LOADING
  // =====================================================
  
  const loadMenuItems = useCallback(async () => {
    try {
      await menuItemsActions.fetchItems();
    } catch (error) {
      setErrorMessage('Failed to load menu items');
    }
  }, [menuItemsActions]);
  
  const loadCategories = useCallback(async () => {
    try {
      const response = await fetch(`/api/v1/admin/tenants/${tenantId}/categories`);
      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories || data);
      }
    } catch (error) {
      console.error('Failed to load categories:', error);
    }
  }, [tenantId]);
  
  const refreshData = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([loadMenuItems(), loadCategories()]);
      setSuccessMessage('Data refreshed successfully');
    } catch (error) {
      setErrorMessage('Failed to refresh data');
    } finally {
      setIsRefreshing(false);
    }
  }, [loadMenuItems, loadCategories]);
  
  // =====================================================
  // MENU ITEM HANDLERS
  // =====================================================
  
  const handleCreateItem = async (data: MenuItemFormData) => {
    const itemData = {
      ...data,
      tenantId,
      sortOrder: menuItemsState.items.length + 1,
    };
    
    await menuItemsActions.createItem(itemData as any);
    setShowItemForm(false);
  };
  
  const handleUpdateItem = async (data: MenuItemFormData) => {
    if (!editingItem) return;
    
    await menuItemsActions.updateItem(editingItem.id, data);
    setEditingItem(null);
  };
  
  const handleDeleteItem = async (itemId: string) => {
    if (confirm('Are you sure you want to delete this menu item? This action cannot be undone.')) {
      await menuItemsActions.deleteItem(itemId);
    }
  };
  
  const handleCloneItem = async (item: MenuItemAdmin) => {
    const clonedData: MenuItemFormData = {
      name: `${item.name} (Copy)`,
      description: item.description,
      category: item.category,
      basePrice: item.basePrice,
      imageUrl: item.imageUrl,
      isAvailable: false, // Start as unavailable
      isVegetarian: item.isVegetarian,
      isVegan: item.isVegan,
      isGlutenFree: item.isGlutenFree,
      preparationTime: item.preparationTime,
      calories: item.calories,
      allergens: [...item.allergens],
      tags: [...item.tags],
      ingredients: [...item.ingredients],
    };
    
    await handleCreateItem(clonedData);
  };
  
  // =====================================================
  // BULK OPERATIONS
  // =====================================================
  
  const handleBulkAction = async (actionId: string, itemIds: string[]) => {
    switch (actionId) {
      case 'toggle-availability':
        // Toggle availability for selected items
        const updates = itemIds.map(async (id) => {
          const item = menuItemsState.items.find(i => i.id === id);
          if (item) {
            await menuItemsActions.updateItem(id, { isAvailable: !item.isAvailable });
          }
        });
        await Promise.all(updates);
        setSuccessMessage(`Updated availability for ${itemIds.length} items`);
        break;
        
      case 'update-category':
        // This would open a modal to select new category
        const newCategory = prompt('Enter new category name:');
        if (newCategory) {
          await menuItemsActions.bulkUpdate(itemIds, { category: newCategory });
          setSuccessMessage(`Updated category for ${itemIds.length} items`);
        }
        break;
        
      case 'clone-items':
        // Clone selected items
        const clonePromises = itemIds.map(async (id) => {
          const item = menuItemsState.items.find(i => i.id === id);
          if (item) {
            await handleCloneItem(item);
          }
        });
        await Promise.all(clonePromises);
        setSuccessMessage(`Cloned ${itemIds.length} items`);
        break;
        
      case 'delete-items':
        if (confirm(`Are you sure you want to delete ${itemIds.length} items? This action cannot be undone.`)) {
          await menuItemsActions.bulkDelete(itemIds);
          setSuccessMessage(`Deleted ${itemIds.length} items`);
        }
        break;
        
      default:
        setErrorMessage('Unknown bulk action');
    }
    
    menuItemsActions.clearSelection();
  };
  
  // =====================================================
  // ANALYTICS DATA
  // =====================================================
  
  const analyticsData = React.useMemo(() => {
    const items = menuItemsState.items;
    const totalItems = items.length;
    const availableItems = items.filter(item => item.isAvailable).length;
    const categoryCount = new Set(items.map(item => item.category)).size;
    const averagePrice = items.reduce((sum, item) => sum + item.basePrice, 0) / totalItems || 0;
    
    const categoryBreakdown = categories.map(category => ({
      name: category.name,
      count: items.filter(item => item.category === category.name).length,
      avgPrice: items
        .filter(item => item.category === category.name)
        .reduce((sum, item) => sum + item.basePrice, 0) / 
        items.filter(item => item.category === category.name).length || 0,
    }));
    
    return {
      totalItems,
      availableItems,
      categoryCount,
      averagePrice,
      categoryBreakdown,
    };
  }, [menuItemsState.items, categories]);
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderAnalytics = () => (
    <div className="space-y-6">
      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Building className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Items</p>
              <p className="text-2xl font-bold text-gray-900">{analyticsData.totalItems}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Zap className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Available</p>
              <p className="text-2xl font-bold text-gray-900">{analyticsData.availableItems}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Categories</p>
              <p className="text-2xl font-bold text-gray-900">{analyticsData.categoryCount}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <BarChart3 className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Avg. Price</p>
              <p className="text-2xl font-bold text-gray-900">${analyticsData.averagePrice.toFixed(2)}</p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Category Breakdown */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Category Breakdown</h3>
        <div className="space-y-4">
          {analyticsData.categoryBreakdown.map((category) => (
            <div key={category.name} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900">{category.name}</h4>
                <p className="text-sm text-gray-600">{category.count} items</p>
              </div>
              <div className="text-right">
                <p className="font-medium text-gray-900">${category.avgPrice.toFixed(2)}</p>
                <p className="text-sm text-gray-600">avg. price</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
  
  const renderTabContent = () => {
    switch (activeTab) {
      case 'items':
        return (
          <div className="space-y-6">
            {/* Form */}
            {(showItemForm || editingItem) && (
              <MenuItemForm
                item={editingItem || undefined}
                categories={categories}
                isLoading={menuItemsState.isSubmitting}
                onSubmit={editingItem ? handleUpdateItem : handleCreateItem}
                onCancel={() => {
                  setShowItemForm(false);
                  setEditingItem(null);
                }}
              />
            )}
            
            {/* Table */}
            <MenuItemsTable
              items={menuItemsState.items}
              isLoading={menuItemsState.loading}
              selectedItems={menuItemsState.selectedItems}
              onItemSelect={menuItemsActions.toggleSelection}
              onSelectAll={menuItemsActions.selectAll}
              onClearSelection={menuItemsActions.clearSelection}
              onItemEdit={setEditingItem}
              onItemDelete={handleDeleteItem}
              onItemClone={handleCloneItem}
              onBulkAction={handleBulkAction}
            />
          </div>
        );
        
      case 'categories':
        return (
          <CategoryManager
            tenantId={tenantId}
            onCategoryUpdate={setCategories}
          />
        );
        
      case 'analytics':
        return renderAnalytics();
        
      default:
        return null;
    }
  };
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  return (
    <ProtectedRoute
      requireAuth={true}
      permissions={[ADMIN_PERMISSIONS.MENU_VIEW]}
    >
      <AdminDashboardLayout title="Menu Management" className={className}>
        <div className="space-y-6">
          {/* Messages */}
          {successMessage && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                <div className="text-green-800">
                  <p>{successMessage}</p>
                </div>
                <button
                  onClick={() => setSuccessMessage(null)}
                  className="ml-auto text-green-600 hover:text-green-800"
                >
                  ×
                </button>
              </div>
            </div>
          )}
          
          {errorMessage && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
                <div className="text-red-800">
                  <p>{errorMessage}</p>
                </div>
                <button
                  onClick={() => setErrorMessage(null)}
                  className="ml-auto text-red-600 hover:text-red-800"
                >
                  ×
                </button>
              </div>
            </div>
          )}
          
          {/* Header Actions */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            {/* Tabs */}
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                {[
                  { id: 'items', label: 'Menu Items', permission: ADMIN_PERMISSIONS.MENU_VIEW },
                  { id: 'categories', label: 'Categories', permission: ADMIN_PERMISSIONS.MENU_EDIT },
                  { id: 'analytics', label: 'Analytics', permission: ADMIN_PERMISSIONS.ANALYTICS_VIEW },
                ].map((tab) => (
                  <RequirePermission key={tab.id} permissions={[tab.permission]}>
                    <button
                      onClick={() => setActiveTab(tab.id as ActiveTab)}
                      className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === tab.id
                          ? 'border-orange-500 text-orange-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      {tab.label}
                    </button>
                  </RequirePermission>
                ))}
              </nav>
            </div>
            
            {/* Actions */}
            <div className="flex items-center space-x-3">
              <button
                onClick={refreshData}
                disabled={isRefreshing}
                className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                <span>Refresh</span>
              </button>
              
              <RequirePermission permissions={[ADMIN_PERMISSIONS.MENU_EDIT]}>
                <button className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                  <Download className="w-4 h-4" />
                  <span>Export</span>
                </button>
              </RequirePermission>
              
              <RequirePermission permissions={[ADMIN_PERMISSIONS.MENU_CREATE]}>
                <button className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                  <Upload className="w-4 h-4" />
                  <span>Import</span>
                </button>
              </RequirePermission>
              
              {activeTab === 'items' && (
                <RequirePermission permissions={[ADMIN_PERMISSIONS.MENU_CREATE]}>
                  <button
                    onClick={() => setShowItemForm(true)}
                    className="flex items-center space-x-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Add Item</span>
                  </button>
                </RequirePermission>
              )}
            </div>
          </div>
          
          {/* Tab Content */}
          {renderTabContent()}
        </div>
      </AdminDashboardLayout>
    </ProtectedRoute>
  );
}
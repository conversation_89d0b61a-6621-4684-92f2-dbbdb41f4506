// Category management component with drag-and-drop reordering and CRUD operations
'use client';

import React, { useState, useCallback } from 'react';
import {
  Plus,
  Edit,
  Trash2,
  GripVertical,
  Eye,
  EyeOff,
  Save,
  X,
  Image as ImageIcon,
  Upload,
  Building,
  AlertCircle,
  CheckCircle,
} from 'lucide-react';
import { useCrud } from '@/hooks/useCrud';
import { useFormValidation } from '@/hooks/useFormValidation';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import type { MenuCategoryAdmin, MenuCategoryFormData } from '@/types/menu-admin';

interface CategoryManagerProps {
  tenantId: string;
  onCategoryUpdate?: (categories: MenuCategoryAdmin[]) => void;
  className?: string;
}

interface CategoryFormProps {
  category?: MenuCategoryAdmin;
  onSubmit: (data: MenuCategoryFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const CATEGORY_VALIDATION_SCHEMA = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 50,
    pattern: /^[a-zA-Z0-9\s\-'&]+$/,
    message: 'Name must be 2-50 characters with letters, numbers, and basic punctuation only'
  },
  description: {
    required: false,
    maxLength: 200,
    message: 'Description must be no more than 200 characters'
  },
};

// =====================================================
// CATEGORY FORM COMPONENT
// =====================================================

function CategoryForm({ category, onSubmit, onCancel, isLoading }: CategoryFormProps) {
  const [formData, setFormData] = useState<MenuCategoryFormData>({
    name: category?.name || '',
    description: category?.description || '',
    imageUrl: category?.imageUrl || '',
    isActive: category?.isActive ?? true,
  });
  
  const [validationState, validationActions] = useFormValidation(CATEGORY_VALIDATION_SCHEMA);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(category?.imageUrl || null);
  
  const updateFormData = useCallback((field: keyof MenuCategoryFormData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };
      
      // Auto-validate field
      const validator = validationState.createFieldValidator(field);
      validator.onChange(value, newData);
      
      return newData;
    });
  }, [validationState]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const errors = validationActions.validateForm(formData);
    if (Object.keys(errors).length > 0) {
      validationActions.markFormTouched();
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Category form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleImageUpload = (file: File) => {
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const dataUrl = e.target?.result as string;
        setImagePreview(dataUrl);
        updateFormData('imageUrl', dataUrl);
      };
      reader.readAsDataURL(file);
    }
  };
  
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          {category ? 'Edit Category' : 'Add New Category'}
        </h3>
        <button
          onClick={onCancel}
          className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Name Field */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Category Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => updateFormData('name', e.target.value)}
            placeholder="e.g., Appetizers, Main Courses, Desserts"
            className={`
              w-full px-4 py-3 rounded-lg border transition-all
              ${validationState.getFieldProps('name').showError
                ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                : 'border-gray-300 focus:border-orange-500 focus:ring-orange-200'
              }
              focus:outline-none focus:ring-2 focus:ring-opacity-50
            `}
          />
          {validationState.getFieldProps('name').showError && (
            <div className="flex items-center space-x-1 text-red-600 text-sm">
              <AlertCircle className="w-4 h-4" />
              <span>{validationState.getFieldProps('name').error}</span>
            </div>
          )}
        </div>
        
        {/* Description Field */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Description</label>
          <textarea
            value={formData.description}
            onChange={(e) => updateFormData('description', e.target.value)}
            placeholder="Brief description of this category (optional)"
            rows={3}
            className={`
              w-full px-4 py-3 rounded-lg border transition-all resize-none
              ${validationState.getFieldProps('description').showError
                ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                : 'border-gray-300 focus:border-orange-500 focus:ring-orange-200'
              }
              focus:outline-none focus:ring-2 focus:ring-opacity-50
            `}
          />
          <div className="flex justify-between items-center">
            <div>
              {validationState.getFieldProps('description').showError && (
                <div className="flex items-center space-x-1 text-red-600 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  <span>{validationState.getFieldProps('description').error}</span>
                </div>
              )}
            </div>
            <span className="text-sm text-gray-500">{formData.description.length}/200</span>
          </div>
        </div>
        
        {/* Image Upload */}
        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-700">Category Image</label>
          <div className="flex items-start space-x-4">
            {imagePreview ? (
              <div className="relative">
                <img
                  src={imagePreview}
                  alt="Preview"
                  className="w-20 h-20 object-cover rounded-lg border"
                />
                <button
                  type="button"
                  onClick={() => {
                    setImagePreview(null);
                    updateFormData('imageUrl', '');
                  }}
                  className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ) : (
              <div className="w-20 h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                <ImageIcon className="w-6 h-6 text-gray-400" />
              </div>
            )}
            
            <div className="flex-1">
              <input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) handleImageUpload(file);
                }}
                className="hidden"
                id={`category-image-${category?.id || 'new'}`}
              />
              <label
                htmlFor={`category-image-${category?.id || 'new'}`}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer transition-colors"
              >
                <Upload className="w-4 h-4 mr-2" />
                Upload
              </label>
              <p className="mt-1 text-xs text-gray-500">Optional category image</p>
            </div>
          </div>
        </div>
        
        {/* Active Toggle */}
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id={`active-${category?.id || 'new'}`}
            checked={formData.isActive}
            onChange={(e) => updateFormData('isActive', e.target.checked)}
            className="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
          />
          <label htmlFor={`active-${category?.id || 'new'}`} className="text-sm text-gray-700">
            Category is active and visible to customers
          </label>
        </div>
        
        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={isSubmitting || isLoading}
            className={`
              px-4 py-2 rounded-lg font-medium transition-all flex items-center space-x-2
              ${isSubmitting || isLoading
                ? 'bg-gray-400 text-white cursor-not-allowed'
                : 'bg-orange-600 text-white hover:bg-orange-700'
              }
            `}
          >
            {isSubmitting ? (
              <>
                <LoadingSpinner size="sm" />
                <span>Saving...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>{category ? 'Update' : 'Create'}</span>
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}

// =====================================================
// MAIN CATEGORY MANAGER COMPONENT
// =====================================================

export function CategoryManager({ 
  tenantId, 
  onCategoryUpdate, 
  className = '' 
}: CategoryManagerProps) {
  
  // =====================================================
  // HOOKS & STATE
  // =====================================================
  
  const [crudState, crudActions] = useCrud<MenuCategoryAdmin>({
    apiEndpoint: `/api/v1/admin/tenants/${tenantId}/categories`,
    optimisticUpdates: true,
    onSuccess: (action, item) => {
      if (action === 'create' || action === 'update' || action === 'delete') {
        onCategoryUpdate?.(crudState.items);
      }
    },
  });
  
  const [showForm, setShowForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<MenuCategoryAdmin | null>(null);
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  
  // =====================================================
  // DRAG AND DROP HANDLERS
  // =====================================================
  
  const handleDragStart = (e: React.DragEvent, categoryId: string) => {
    setDraggedItem(categoryId);
    e.dataTransfer.effectAllowed = 'move';
  };
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };
  
  const handleDrop = async (e: React.DragEvent, targetCategoryId: string) => {
    e.preventDefault();
    
    if (!draggedItem || draggedItem === targetCategoryId) {
      setDraggedItem(null);
      return;
    }
    
    const items = [...crudState.items];
    const draggedIndex = items.findIndex(item => item.id === draggedItem);
    const targetIndex = items.findIndex(item => item.id === targetCategoryId);
    
    if (draggedIndex === -1 || targetIndex === -1) return;
    
    // Reorder items
    const draggedCategory = items[draggedIndex];
    items.splice(draggedIndex, 1);
    items.splice(targetIndex, 0, draggedCategory);
    
    // Update sort orders
    const updatedItems = items.map((item, index) => ({
      ...item,
      sortOrder: index + 1,
    }));
    
    // Optimistically update UI
    crudActions.setItems(updatedItems);
    
    try {
      // Batch update sort orders
      await crudActions.bulkUpdate(
        updatedItems.map(item => item.id),
        { sortOrder: items.findIndex(i => i.id === updatedItems[0].id) + 1 }
      );
    } catch (error) {
      // Revert on error
      crudActions.refresh();
    }
    
    setDraggedItem(null);
  };
  
  // =====================================================
  // FORM HANDLERS
  // =====================================================
  
  const handleCreateCategory = async (data: MenuCategoryFormData) => {
    const categoryData = {
      ...data,
      tenantId,
      sortOrder: crudState.items.length + 1,
    };
    
    await crudActions.createItem(categoryData as any);
    setShowForm(false);
  };
  
  const handleUpdateCategory = async (data: MenuCategoryFormData) => {
    if (!editingCategory) return;
    
    await crudActions.updateItem(editingCategory.id, data);
    setEditingCategory(null);
  };
  
  const handleDeleteCategory = async (categoryId: string) => {
    if (confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      await crudActions.deleteItem(categoryId);
    }
  };
  
  const handleToggleActive = async (category: MenuCategoryAdmin) => {
    await crudActions.updateItem(category.id, { 
      isActive: !category.isActive 
    });
  };
  
  // =====================================================
  // EFFECTS
  // =====================================================
  
  React.useEffect(() => {
    crudActions.fetchItems();
  }, [tenantId]);
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderCategoryItem = (category: MenuCategoryAdmin) => (
    <div
      key={category.id}
      draggable
      onDragStart={(e) => handleDragStart(e, category.id)}
      onDragOver={handleDragOver}
      onDrop={(e) => handleDrop(e, category.id)}
      className={`
        bg-white border border-gray-200 rounded-lg p-4 transition-all
        ${draggedItem === category.id ? 'opacity-50 transform rotate-2' : ''}
        hover:shadow-md cursor-move
      `}
    >
      <div className="flex items-center space-x-4">
        {/* Drag Handle */}
        <div className="text-gray-400 hover:text-gray-600 cursor-grab active:cursor-grabbing">
          <GripVertical className="w-5 h-5" />
        </div>
        
        {/* Category Image */}
        <div className="flex-shrink-0">
          {category.imageUrl ? (
            <img
              src={category.imageUrl}
              alt={category.name}
              className="w-12 h-12 object-cover rounded-lg border"
            />
          ) : (
            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
              <Building className="w-6 h-6 text-gray-400" />
            </div>
          )}
        </div>
        
        {/* Category Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <h3 className="text-lg font-medium text-gray-900 truncate">
              {category.name}
            </h3>
            <span className={`
              inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
              ${category.isActive 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-800'
              }
            `}>
              {category.isActive ? 'Active' : 'Inactive'}
            </span>
          </div>
          
          {category.description && (
            <p className="text-sm text-gray-600 mt-1 truncate">
              {category.description}
            </p>
          )}
          
          <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
            <span>{category.itemCount || 0} items</span>
            <span>Order: {category.sortOrder}</span>
            <span>Updated: {new Date(category.updatedAt).toLocaleDateString()}</span>
          </div>
        </div>
        
        {/* Actions */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleToggleActive(category)}
            className={`
              p-2 rounded-lg transition-colors
              ${category.isActive
                ? 'text-green-600 hover:bg-green-100'
                : 'text-gray-400 hover:bg-gray-100'
              }
            `}
            title={category.isActive ? 'Hide category' : 'Show category'}
          >
            {category.isActive ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
          </button>
          
          <button
            onClick={() => setEditingCategory(category)}
            className="p-2 text-orange-600 hover:bg-orange-100 rounded-lg transition-colors"
            title="Edit category"
          >
            <Edit className="w-4 h-4" />
          </button>
          
          <button
            onClick={() => handleDeleteCategory(category.id)}
            className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
            title="Delete category"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  if (crudState.loading && crudState.items.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" text="Loading categories..." />
      </div>
    );
  }
  
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Category Management</h2>
          <p className="text-gray-600 mt-1">
            Organize your menu items into categories. Drag to reorder.
          </p>
        </div>
        
        <button
          onClick={() => setShowForm(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Add Category</span>
        </button>
      </div>
      
      {/* Error Display */}
      {crudState.error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
            <div className="text-red-800">
              <p className="font-medium">Error</p>
              <p className="mt-1">{crudState.error}</p>
            </div>
          </div>
        </div>
      )}
      
      {/* Category Form */}
      {(showForm || editingCategory) && (
        <CategoryForm
          category={editingCategory || undefined}
          onSubmit={editingCategory ? handleUpdateCategory : handleCreateCategory}
          onCancel={() => {
            setShowForm(false);
            setEditingCategory(null);
          }}
          isLoading={crudState.isSubmitting}
        />
      )}
      
      {/* Categories List */}
      <div className="space-y-3">
        {crudState.items.length > 0 ? (
          <>
            <div className="text-sm text-gray-600 mb-4">
              Showing {crudState.items.length} categories. Drag to reorder.
            </div>
            {crudState.items
              .sort((a, b) => a.sortOrder - b.sortOrder)
              .map(renderCategoryItem)}
          </>
        ) : (
          <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
            <Building className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No categories yet</h3>
            <p className="text-gray-600 mb-4">
              Create your first category to start organizing your menu items.
            </p>
            <button
              onClick={() => setShowForm(true)}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Add First Category</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
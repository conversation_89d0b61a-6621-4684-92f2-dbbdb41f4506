// Comprehensive menu item form with validation, image upload, and real-time feedback
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Save,
  X,
  Upload,
  Image as ImageIcon,
  DollarSign,
  Clock,
  Tag,
  AlertCircle,
  CheckCircle,
  Trash2,
  Eye,
  <PERSON>Off,
  Plus,
  Minus,
} from 'lucide-react';
import { useFormValidation } from '@/hooks/useFormValidation';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import type { 
  MenuItemAdmin, 
  MenuItemFormData, 
  MenuCategoryAdmin,
  DIETARY_OPTIONS,
  COMMON_ALLERGENS,
  COMMON_TAGS 
} from '@/types/menu-admin';

interface MenuItemFormProps {
  item?: MenuItemAdmin;
  categories: MenuCategoryAdmin[];
  isLoading?: boolean;
  onSubmit: (data: MenuItemFormData) => Promise<void>;
  onCancel: () => void;
  className?: string;
}

const FORM_VALIDATION_SCHEMA = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    pattern: /^[a-zA-Z0-9\s\-'.&]+$/,
    message: 'Name must be 2-100 characters with letters, numbers, and basic punctuation only'
  },
  description: {
    required: true,
    minLength: 10,
    maxLength: 500,
    message: 'Description must be 10-500 characters'
  },
  category: {
    required: true,
    message: 'Please select a category'
  },
  basePrice: {
    required: true,
    min: 0.01,
    max: 999.99,
    custom: (value: any) => {
      const numValue = parseFloat(value);
      if (isNaN(numValue)) return 'Please enter a valid price';
      return null;
    }
  },
  preparationTime: {
    required: true,
    min: 1,
    max: 180,
    custom: (value: any) => {
      const numValue = parseInt(value);
      if (isNaN(numValue)) return 'Please enter a valid preparation time';
      return null;
    }
  },
  calories: {
    required: false,
    min: 1,
    max: 5000,
    custom: (value: any) => {
      if (!value) return null;
      const numValue = parseInt(value);
      if (isNaN(numValue)) return 'Please enter a valid calorie count';
      return null;
    }
  },
};

export function MenuItemForm({
  item,
  categories,
  isLoading = false,
  onSubmit,
  onCancel,
  className = ''
}: MenuItemFormProps) {
  
  // =====================================================
  // STATE MANAGEMENT
  // =====================================================
  
  const [formData, setFormData] = useState<MenuItemFormData>({
    name: item?.name || '',
    description: item?.description || '',
    category: item?.category || '',
    basePrice: item?.basePrice || 0,
    imageUrl: item?.imageUrl || '',
    isAvailable: item?.isAvailable ?? true,
    isVegetarian: item?.isVegetarian || false,
    isVegan: item?.isVegan || false,
    isGlutenFree: item?.isGlutenFree || false,
    preparationTime: item?.preparationTime || 15,
    calories: item?.calories || undefined,
    allergens: item?.allergens || [],
    tags: item?.tags || [],
    ingredients: item?.ingredients || [],
  });
  
  const [validationState, validationActions] = useFormValidation(FORM_VALIDATION_SCHEMA, {
    validateOnChange: true,
    validateOnBlur: true,
    debounceMs: 300,
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(item?.imageUrl || null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [currentIngredient, setCurrentIngredient] = useState('');
  
  // =====================================================
  // FORM HANDLERS
  // =====================================================
  
  const updateFormData = useCallback((field: keyof MenuItemFormData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };
      
      // Auto-validate field
      const validator = validationState.createFieldValidator(field);
      validator.onChange(value, newData);
      
      return newData;
    });
  }, [validationState]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate entire form
    const errors = validationActions.validateForm(formData);
    if (Object.keys(errors).length > 0) {
      validationActions.markFormTouched();
      return;
    }
    
    setIsSubmitting(true);
    validationActions.setSubmitting(true);
    
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
      validationActions.setSubmitting(false);
    }
  };
  
  const handleImageUpload = (file: File) => {
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const dataUrl = e.target?.result as string;
        setImagePreview(dataUrl);
        updateFormData('imageUrl', dataUrl);
      };
      reader.readAsDataURL(file);
    }
  };
  
  const addIngredient = () => {
    if (currentIngredient.trim() && !formData.ingredients.includes(currentIngredient.trim())) {
      updateFormData('ingredients', [...formData.ingredients, currentIngredient.trim()]);
      setCurrentIngredient('');
    }
  };
  
  const removeIngredient = (ingredient: string) => {
    updateFormData('ingredients', formData.ingredients.filter(i => i !== ingredient));
  };
  
  const toggleArrayItem = (array: string[], item: string) => {
    const newArray = array.includes(item)
      ? array.filter(i => i !== item)
      : [...array, item];
    return newArray;
  };
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderField = ({
    label,
    name,
    type = 'text',
    placeholder,
    required = false,
    prefix,
    suffix,
    children,
    description,
  }: {
    label: string;
    name: keyof MenuItemFormData;
    type?: string;
    placeholder?: string;
    required?: boolean;
    prefix?: React.ReactNode;
    suffix?: React.ReactNode;
    children?: React.ReactNode;
    description?: string;
  }) => {
    const fieldProps = validationState.getFieldProps(name);
    const validator = validationState.createFieldValidator(name);
    
    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
        
        {children || (
          <div className="relative">
            {prefix && (
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                {prefix}
              </div>
            )}
            
            <input
              type={type}
              value={formData[name] as string | number}
              onChange={(e) => {
                const value = type === 'number' ? parseFloat(e.target.value) || 0 : e.target.value;
                updateFormData(name, value);
              }}
              onBlur={(e) => {
                const value = type === 'number' ? parseFloat(e.target.value) || 0 : e.target.value;
                validator.onBlur(value, formData);
              }}
              placeholder={placeholder}
              className={`
                w-full px-4 py-3 rounded-lg border transition-all
                ${prefix ? 'pl-10' : ''}
                ${suffix ? 'pr-10' : ''}
                ${fieldProps.showError
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                  : fieldProps.isTouched && !fieldProps.hasError
                    ? 'border-green-300 bg-green-50 focus:border-green-500 focus:ring-green-200'
                    : 'border-gray-300 focus:border-orange-500 focus:ring-orange-200'
                }
                focus:outline-none focus:ring-2 focus:ring-opacity-50
              `}
            />
            
            {suffix && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                {suffix}
              </div>
            )}
            
            {fieldProps.isTouched && !fieldProps.hasError && !suffix && (
              <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
            )}
          </div>
        )}
        
        {description && (
          <p className="text-sm text-gray-500">{description}</p>
        )}
        
        {fieldProps.showError && (
          <div className="flex items-center space-x-1 text-red-600 text-sm">
            <AlertCircle className="w-4 h-4" />
            <span>{fieldProps.error}</span>
          </div>
        )}
      </div>
    );
  };
  
  const renderTextArea = (name: keyof MenuItemFormData, label: string, placeholder: string, required = false) => {
    const fieldProps = validationState.getFieldProps(name);
    const validator = validationState.createFieldValidator(name);
    
    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
        
        <textarea
          value={formData[name] as string}
          onChange={(e) => updateFormData(name, e.target.value)}
          onBlur={(e) => validator.onBlur(e.target.value, formData)}
          placeholder={placeholder}
          rows={4}
          className={`
            w-full px-4 py-3 rounded-lg border transition-all resize-none
            ${fieldProps.showError
              ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
              : fieldProps.isTouched && !fieldProps.hasError
                ? 'border-green-300 bg-green-50 focus:border-green-500 focus:ring-green-200'
                : 'border-gray-300 focus:border-orange-500 focus:ring-orange-200'
            }
            focus:outline-none focus:ring-2 focus:ring-opacity-50
          `}
        />
        
        <div className="flex justify-between items-center">
          <div>
            {fieldProps.showError && (
              <div className="flex items-center space-x-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4" />
                <span>{fieldProps.error}</span>
              </div>
            )}
          </div>
          <span className="text-sm text-gray-500">
            {(formData[name] as string).length}/500
          </span>
        </div>
      </div>
    );
  };
  
  const renderCheckboxGroup = (title: string, options: readonly any[], field: keyof MenuItemFormData) => (
    <div className="space-y-3">
      <h4 className="text-sm font-medium text-gray-700">{title}</h4>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        {options.map((option) => {
          const isChecked = field === 'allergens' || field === 'tags' 
            ? (formData[field] as string[]).includes(option)
            : formData[field as keyof MenuItemFormData] as boolean;
            
          return (
            <label key={option.id || option} className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={isChecked}
                onChange={(e) => {
                  if (field === 'allergens' || field === 'tags') {
                    updateFormData(field, toggleArrayItem(formData[field] as string[], option));
                  } else {
                    updateFormData(field, e.target.checked);
                  }
                }}
                className="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
              />
              <span className="text-sm text-gray-700">
                {option.icon && option.icon} {option.label || option}
              </span>
            </label>
          );
        })}
      </div>
    </div>
  );
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            {item ? 'Edit Menu Item' : 'Add New Menu Item'}
          </h2>
          <button
            onClick={onCancel}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>
      
      {/* Form */}
      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            {renderField({
              label: 'Item Name',
              name: 'name',
              placeholder: 'e.g., Margherita Pizza',
              required: true,
            })}
          </div>
          
          <div className="md:col-span-2">
            {renderTextArea('description', 'Description', 'Detailed description of the menu item...', true)}
          </div>
          
          <div>
            {renderField({
              label: 'Category',
              name: 'category',
              required: true,
              children: (
                <select
                  value={formData.category}
                  onChange={(e) => updateFormData('category', e.target.value)}
                  className={`
                    w-full px-4 py-3 rounded-lg border transition-all
                    ${validationState.getFieldProps('category').showError
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                      : 'border-gray-300 focus:border-orange-500 focus:ring-orange-200'
                    }
                    focus:outline-none focus:ring-2 focus:ring-opacity-50
                  `}
                >
                  <option value="">Select a category</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.name}>
                      {category.name}
                    </option>
                  ))}
                </select>
              ),
            })}
          </div>
          
          <div>
            {renderField({
              label: 'Price',
              name: 'basePrice',
              type: 'number',
              placeholder: '0.00',
              required: true,
              prefix: <DollarSign className="w-4 h-4" />,
              description: 'Base price before any discounts or promotions',
            })}
          </div>
          
          <div>
            {renderField({
              label: 'Preparation Time',
              name: 'preparationTime',
              type: 'number',
              placeholder: '15',
              required: true,
              suffix: <Clock className="w-4 h-4" />,
              description: 'Estimated time in minutes',
            })}
          </div>
          
          <div>
            {renderField({
              label: 'Calories (Optional)',
              name: 'calories',
              type: 'number',
              placeholder: '350',
              description: 'Nutritional information',
            })}
          </div>
        </div>
        
        {/* Image Upload */}
        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-700">Item Image</label>
          <div className="flex items-start space-x-4">
            {imagePreview ? (
              <div className="relative">
                <img
                  src={imagePreview}
                  alt="Preview"
                  className="w-32 h-32 object-cover rounded-lg border"
                />
                <button
                  type="button"
                  onClick={() => {
                    setImagePreview(null);
                    updateFormData('imageUrl', '');
                  }}
                  className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ) : (
              <div className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                <ImageIcon className="w-8 h-8 text-gray-400" />
              </div>
            )}
            
            <div className="flex-1">
              <input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) handleImageUpload(file);
                }}
                className="hidden"
                id="image-upload"
              />
              <label
                htmlFor="image-upload"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer transition-colors"
              >
                <Upload className="w-4 h-4 mr-2" />
                Upload Image
              </label>
              <p className="mt-2 text-sm text-gray-500">
                PNG, JPG up to 5MB. Recommended size: 400x400px
              </p>
            </div>
          </div>
        </div>
        
        {/* Dietary Options */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Dietary Information</h3>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={formData.isAvailable}
                onChange={(e) => updateFormData('isAvailable', e.target.checked)}
                className="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
              />
              <span className="text-sm text-gray-700">Available to order</span>
            </label>
          </div>
          
          {renderCheckboxGroup('Dietary Restrictions', [
            { id: 'isVegetarian', label: 'Vegetarian', icon: '🌱' },
            { id: 'isVegan', label: 'Vegan', icon: '🌿' },
            { id: 'isGlutenFree', label: 'Gluten Free', icon: '🌾' },
          ], 'isVegetarian')}
        </div>
        
        {/* Advanced Options */}
        <div className="space-y-4">
          <button
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center space-x-2 text-orange-600 hover:text-orange-700 transition-colors"
          >
            {showAdvanced ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            <span>{showAdvanced ? 'Hide' : 'Show'} Advanced Options</span>
          </button>
          
          {showAdvanced && (
            <div className="space-y-6 p-4 bg-gray-50 rounded-lg">
              {/* Allergens */}
              {renderCheckboxGroup('Common Allergens', [
                'Milk', 'Eggs', 'Fish', 'Shellfish', 'Tree Nuts', 'Peanuts', 'Wheat', 'Soybeans', 'Sesame'
              ], 'allergens')}
              
              {/* Tags */}
              {renderCheckboxGroup('Tags', [
                'Popular', 'New', 'Spicy', 'Chef Special', 'Healthy', 'Comfort Food', 'Light', 'Hearty', 'Sweet', 'Savory'
              ], 'tags')}
              
              {/* Ingredients */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-gray-700">Ingredients</h4>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={currentIngredient}
                    onChange={(e) => setCurrentIngredient(e.target.value)}
                    placeholder="Add ingredient..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        addIngredient();
                      }
                    }}
                  />
                  <button
                    type="button"
                    onClick={addIngredient}
                    className="px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
                
                {formData.ingredients.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.ingredients.map((ingredient, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 bg-white border border-gray-300 rounded-full text-sm"
                      >
                        {ingredient}
                        <button
                          type="button"
                          onClick={() => removeIngredient(ingredient)}
                          className="ml-2 text-gray-400 hover:text-red-500 transition-colors"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        
        {/* Form Actions */}
        <div className="flex items-center justify-between pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          
          <div className="flex items-center space-x-3">
            {!validationState.isValid && validationState.submitCount > 0 && (
              <span className="text-red-600 text-sm">Please fix errors above</span>
            )}
            
            <button
              type="submit"
              disabled={isSubmitting || isLoading}
              className={`
                px-6 py-3 rounded-lg font-medium transition-all flex items-center space-x-2
                ${isSubmitting || isLoading
                  ? 'bg-gray-400 text-white cursor-not-allowed'
                  : 'bg-orange-600 text-white hover:bg-orange-700 shadow-lg hover:shadow-xl'
                }
              `}
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  <span>{item ? 'Update Item' : 'Create Item'}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
// Responsive menu items table with advanced filtering, sorting, and bulk operations
'use client';

import React, { useState, useMemo, useCallback } from 'react';
import {
  Search,
  Filter,
  Grid,
  List,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Eye,
  EyeOff,
  Edit,
  Trash2,
  Copy,
  MoreVertical,
  CheckSquare,
  Square,
  X,
  DollarSign,
  Clock,
  Image as ImageIcon,
  Tag,
  Utensils,
  Star,
  Plus,
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import type { 
  MenuItemAdmin, 
  MenuFilters, 
  MenuSortOption, 
  TableColumn, 
  BulkAction 
} from '@/types/menu-admin';

interface MenuItemsTableProps {
  items: MenuItemAdmin[];
  isLoading: boolean;
  selectedItems: Set<string>;
  onItemSelect: (id: string) => void;
  onSelectAll: () => void;
  onClearSelection: () => void;
  onItemEdit: (item: MenuItemAdmin) => void;
  onItemDelete: (id: string) => void;
  onItemClone: (item: MenuItemAdmin) => void;
  onBulkAction: (action: string, itemIds: string[]) => Promise<void>;
  onFiltersChange?: (filters: MenuFilters) => void;
  className?: string;
}

type ViewMode = 'table' | 'grid';

const SORT_OPTIONS: MenuSortOption[] = [
  { field: 'name', direction: 'asc', label: 'Name A-Z' },
  { field: 'name', direction: 'desc', label: 'Name Z-A' },
  { field: 'basePrice', direction: 'asc', label: 'Price Low-High' },
  { field: 'basePrice', direction: 'desc', label: 'Price High-Low' },
  { field: 'category', direction: 'asc', label: 'Category A-Z' },
  { field: 'preparationTime', direction: 'asc', label: 'Prep Time (Fastest)' },
  { field: 'createdAt', direction: 'desc', label: 'Newest First' },
  { field: 'updatedAt', direction: 'desc', label: 'Recently Updated' },
];

const TABLE_COLUMNS: TableColumn<MenuItemAdmin>[] = [
  {
    key: 'name',
    label: 'Item',
    sortable: true,
    width: '25%',
    render: (value, item) => (
      <div className="flex items-center space-x-3">
        {item.imageUrl ? (
          <img
            src={item.imageUrl}
            alt={item.name}
            className="w-12 h-12 object-cover rounded-lg border"
          />
        ) : (
          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
            <Utensils className="w-6 h-6 text-gray-400" />
          </div>
        )}
        <div className="min-w-0 flex-1">
          <div className="font-medium text-gray-900 truncate">{item.name}</div>
          <div className="text-sm text-gray-500 truncate">{item.description}</div>
        </div>
      </div>
    ),
  },
  {
    key: 'category',
    label: 'Category',
    sortable: true,
    width: '15%',
  },
  {
    key: 'basePrice',
    label: 'Price',
    sortable: true,
    width: '10%',
    align: 'right',
    render: (value) => (
      <span className="font-medium text-gray-900">${value.toFixed(2)}</span>
    ),
  },
  {
    key: 'preparationTime',
    label: 'Prep Time',
    sortable: true,
    width: '10%',
    align: 'center',
    render: (value) => (
      <span className="text-sm text-gray-600">{value}m</span>
    ),
  },
  {
    key: 'isAvailable',
    label: 'Status',
    sortable: true,
    width: '10%',
    align: 'center',
    render: (value) => (
      <span className={`
        inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
        ${value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
      `}>
        {value ? 'Available' : 'Unavailable'}
      </span>
    ),
  },
  {
    key: 'tags',
    label: 'Tags',
    width: '15%',
    render: (value: string[]) => (
      <div className="flex flex-wrap gap-1">
        {value.slice(0, 2).map((tag, index) => (
          <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800">
            {tag}
          </span>
        ))}
        {value.length > 2 && (
          <span className="text-xs text-gray-500">+{value.length - 2}</span>
        )}
      </div>
    ),
  },
];

const BULK_ACTIONS: BulkAction[] = [
  {
    id: 'toggle-availability',
    label: 'Toggle Availability',
    icon: Eye,
    action: async () => {},
  },
  {
    id: 'update-category',
    label: 'Update Category',
    icon: Tag,
    action: async () => {},
  },
  {
    id: 'clone-items',
    label: 'Clone Items',
    icon: Copy,
    action: async () => {},
  },
  {
    id: 'delete-items',
    label: 'Delete Items',
    icon: Trash2,
    action: async () => {},
    destructive: true,
    requiresConfirmation: true,
    confirmMessage: 'Are you sure you want to delete the selected items? This action cannot be undone.',
  },
];

export function MenuItemsTable({
  items,
  isLoading,
  selectedItems,
  onItemSelect,
  onSelectAll,
  onClearSelection,
  onItemEdit,
  onItemDelete,
  onItemClone,
  onBulkAction,
  onFiltersChange,
  className = ''
}: MenuItemsTableProps) {
  
  // =====================================================
  // STATE MANAGEMENT
  // =====================================================
  
  const [viewMode, setViewMode] = useState<ViewMode>('table');
  const [sortOption, setSortOption] = useState<MenuSortOption>(SORT_OPTIONS[0]);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<MenuFilters>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [showBulkActions, setShowBulkActions] = useState(false);
  
  // =====================================================
  // FILTERING AND SORTING LOGIC
  // =====================================================
  
  const filteredAndSortedItems = useMemo(() => {
    let filtered = [...items];
    
    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(query) ||
        item.description.toLowerCase().includes(query) ||
        item.category.toLowerCase().includes(query) ||
        item.tags.some(tag => tag.toLowerCase().includes(query)) ||
        item.ingredients.some(ing => ing.toLowerCase().includes(query))
      );
    }
    
    // Apply category filter
    if (filters.category) {
      filtered = filtered.filter(item => item.category === filters.category);
    }
    
    // Apply availability filter
    if (filters.isAvailable !== undefined) {
      filtered = filtered.filter(item => item.isAvailable === filters.isAvailable);
    }
    
    // Apply dietary filters
    if (filters.isVegetarian) {
      filtered = filtered.filter(item => item.isVegetarian);
    }
    if (filters.isVegan) {
      filtered = filtered.filter(item => item.isVegan);
    }
    if (filters.isGlutenFree) {
      filtered = filtered.filter(item => item.isGlutenFree);
    }
    
    // Apply price range filter
    if (filters.priceMin !== undefined) {
      filtered = filtered.filter(item => item.basePrice >= filters.priceMin!);
    }
    if (filters.priceMax !== undefined) {
      filtered = filtered.filter(item => item.basePrice <= filters.priceMax!);
    }
    
    // Apply tag filter
    if (filters.tags && filters.tags.length > 0) {
      filtered = filtered.filter(item =>
        filters.tags!.some(tag => item.tags.includes(tag))
      );
    }
    
    // Apply allergen filter
    if (filters.allergens && filters.allergens.length > 0) {
      filtered = filtered.filter(item =>
        filters.allergens!.some(allergen => item.allergens.includes(allergen))
      );
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortOption.field];
      const bValue = b[sortOption.field];
      
      if (aValue < bValue) return sortOption.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOption.direction === 'asc' ? 1 : -1;
      return 0;
    });
    
    return filtered;
  }, [items, searchQuery, filters, sortOption]);
  
  // =====================================================
  // EVENT HANDLERS
  // =====================================================
  
  const updateFilters = useCallback((newFilters: Partial<MenuFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFiltersChange?.(updatedFilters);
  }, [filters, onFiltersChange]);
  
  const handleSelectAll = () => {
    if (selectedItems.size === filteredAndSortedItems.length) {
      onClearSelection();
    } else {
      onSelectAll();
    }
  };
  
  const handleBulkAction = async (actionId: string) => {
    const selectedIds = Array.from(selectedItems);
    await onBulkAction(actionId, selectedIds);
    setShowBulkActions(false);
  };
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderFiltersPanel = () => (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-900">Filters</h3>
        <button
          onClick={() => {
            setFilters({});
            setSearchQuery('');
            onFiltersChange?.({});
          }}
          className="text-sm text-orange-600 hover:text-orange-700"
        >
          Clear All
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Category Filter */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Category</label>
          <select
            value={filters.category || ''}
            onChange={(e) => updateFilters({ category: e.target.value || undefined })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option value="">All Categories</option>
            {Array.from(new Set(items.map(item => item.category))).map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>
        
        {/* Availability Filter */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Availability</label>
          <select
            value={filters.isAvailable === undefined ? '' : filters.isAvailable.toString()}
            onChange={(e) => updateFilters({ 
              isAvailable: e.target.value === '' ? undefined : e.target.value === 'true'
            })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option value="">All Items</option>
            <option value="true">Available</option>
            <option value="false">Unavailable</option>
          </select>
        </div>
        
        {/* Price Range */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Price Range</label>
          <div className="flex space-x-2">
            <input
              type="number"
              placeholder="Min"
              value={filters.priceMin || ''}
              onChange={(e) => updateFilters({ 
                priceMin: e.target.value ? parseFloat(e.target.value) : undefined 
              })}
              className="w-full px-2 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
            <input
              type="number"
              placeholder="Max"
              value={filters.priceMax || ''}
              onChange={(e) => updateFilters({ 
                priceMax: e.target.value ? parseFloat(e.target.value) : undefined 
              })}
              className="w-full px-2 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
          </div>
        </div>
        
        {/* Dietary Filters */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Dietary</label>
          <div className="space-y-2">
            {[
              { key: 'isVegetarian', label: 'Vegetarian' },
              { key: 'isVegan', label: 'Vegan' },
              { key: 'isGlutenFree', label: 'Gluten Free' },
            ].map((option) => (
              <label key={option.key} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={!!filters[option.key as keyof MenuFilters]}
                  onChange={(e) => updateFilters({ 
                    [option.key]: e.target.checked || undefined 
                  })}
                  className="w-3 h-3 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
                />
                <span className="text-xs text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
  
  const renderTableView = () => (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="w-12 px-6 py-3">
                <input
                  type="checkbox"
                  checked={selectedItems.size === filteredAndSortedItems.length && filteredAndSortedItems.length > 0}
                  onChange={handleSelectAll}
                  className="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
                />
              </th>
              
              {TABLE_COLUMNS.map((column) => (
                <th
                  key={column.key as string}
                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${column.width ? `w-[${column.width}]` : ''}`}
                  style={{ textAlign: column.align || 'left' }}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.label}</span>
                    {column.sortable && (
                      <button
                        onClick={() => {
                          const newDirection = sortOption.field === column.key && sortOption.direction === 'asc' ? 'desc' : 'asc';
                          setSortOption({ field: column.key, direction: newDirection, label: '' });
                        }}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        {sortOption.field === column.key ? (
                          sortOption.direction === 'asc' ? <ArrowUp className="w-3 h-3" /> : <ArrowDown className="w-3 h-3" />
                        ) : (
                          <ArrowUpDown className="w-3 h-3" />
                        )}
                      </button>
                    )}
                  </div>
                </th>
              ))}
              
              <th className="w-16 px-6 py-3"></th>
            </tr>
          </thead>
          
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredAndSortedItems.map((item) => (
              <tr
                key={item.id}
                className={`hover:bg-gray-50 ${selectedItems.has(item.id) ? 'bg-orange-50' : ''}`}
              >
                <td className="px-6 py-4">
                  <input
                    type="checkbox"
                    checked={selectedItems.has(item.id)}
                    onChange={() => onItemSelect(item.id)}
                    className="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
                  />
                </td>
                
                {TABLE_COLUMNS.map((column) => (
                  <td
                    key={column.key as string}
                    className="px-6 py-4 whitespace-nowrap"
                    style={{ textAlign: column.align || 'left' }}
                  >
                    {column.render 
                      ? column.render(item[column.key], item)
                      : String(item[column.key])
                    }
                  </td>
                ))}
                
                <td className="px-6 py-4 whitespace-nowrap text-right">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => onItemEdit(item)}
                      className="p-1 text-orange-600 hover:bg-orange-100 rounded transition-colors"
                      title="Edit item"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={() => onItemClone(item)}
                      className="p-1 text-blue-600 hover:bg-blue-100 rounded transition-colors"
                      title="Clone item"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={() => onItemDelete(item.id)}
                      className="p-1 text-red-600 hover:bg-red-100 rounded transition-colors"
                      title="Delete item"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {filteredAndSortedItems.length === 0 && (
        <div className="text-center py-12">
          <Utensils className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No items found</h3>
          <p className="text-gray-600">
            {searchQuery || Object.keys(filters).length > 0
              ? 'Try adjusting your search or filters'
              : 'Add your first menu item to get started'
            }
          </p>
        </div>
      )}
    </div>
  );
  
  const renderGridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {filteredAndSortedItems.map((item) => (
        <div
          key={item.id}
          className={`bg-white rounded-lg border-2 transition-all hover:shadow-md ${
            selectedItems.has(item.id) ? 'border-orange-500 bg-orange-50' : 'border-gray-200'
          }`}
        >
          {/* Card Header */}
          <div className="p-4 pb-3">
            <div className="flex items-center justify-between mb-3">
              <input
                type="checkbox"
                checked={selectedItems.has(item.id)}
                onChange={() => onItemSelect(item.id)}
                className="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
              />
              
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => onItemEdit(item)}
                  className="p-1 text-orange-600 hover:bg-orange-100 rounded transition-colors"
                >
                  <Edit className="w-4 h-4" />
                </button>
                
                <button
                  onClick={() => onItemClone(item)}
                  className="p-1 text-blue-600 hover:bg-blue-100 rounded transition-colors"
                >
                  <Copy className="w-4 h-4" />
                </button>
                
                <button
                  onClick={() => onItemDelete(item.id)}
                  className="p-1 text-red-600 hover:bg-red-100 rounded transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
            
            {/* Item Image */}
            <div className="aspect-w-16 aspect-h-9 mb-3">
              {item.imageUrl ? (
                <img
                  src={item.imageUrl}
                  alt={item.name}
                  className="w-full h-32 object-cover rounded-lg"
                />
              ) : (
                <div className="w-full h-32 bg-gray-100 rounded-lg flex items-center justify-center">
                  <Utensils className="w-8 h-8 text-gray-400" />
                </div>
              )}
            </div>
            
            {/* Item Info */}
            <div className="space-y-2">
              <div className="flex items-start justify-between">
                <h3 className="font-medium text-gray-900 truncate flex-1 mr-2">
                  {item.name}
                </h3>
                <span className="font-semibold text-orange-600">
                  ${item.basePrice.toFixed(2)}
                </span>
              </div>
              
              <p className="text-sm text-gray-600 line-clamp-2">
                {item.description}
              </p>
              
              <div className="flex items-center justify-between text-sm text-gray-500">
                <span>{item.category}</span>
                <span className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>{item.preparationTime}m</span>
                </span>
              </div>
              
              {/* Status and Tags */}
              <div className="flex items-center justify-between">
                <span className={`
                  inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                  ${item.isAvailable ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
                `}>
                  {item.isAvailable ? 'Available' : 'Unavailable'}
                </span>
                
                {item.tags.length > 0 && (
                  <div className="flex space-x-1">
                    {item.tags.slice(0, 2).map((tag, index) => (
                      <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800">
                        {tag}
                      </span>
                    ))}
                    {item.tags.length > 2 && (
                      <span className="text-xs text-gray-500">+{item.tags.length - 2}</span>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      ))}
      
      {filteredAndSortedItems.length === 0 && (
        <div className="col-span-full text-center py-12">
          <Utensils className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No items found</h3>
          <p className="text-gray-600">
            {searchQuery || Object.keys(filters).length > 0
              ? 'Try adjusting your search or filters'
              : 'Add your first menu item to get started'
            }
          </p>
        </div>
      )}
    </div>
  );
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Toolbar */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        {/* Search and View Controls */}
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search menu items..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
          </div>
          
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center space-x-2 px-3 py-2 border rounded-lg transition-colors ${
              showFilters || Object.keys(filters).length > 0
                ? 'bg-orange-100 border-orange-300 text-orange-700'
                : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <Filter className="w-4 h-4" />
            <span>Filters</span>
            {Object.keys(filters).length > 0 && (
              <span className="bg-orange-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {Object.keys(filters).length}
              </span>
            )}
          </button>
          
          <div className="flex items-center border border-gray-300 rounded-lg overflow-hidden">
            <button
              onClick={() => setViewMode('table')}
              className={`p-2 transition-colors ${
                viewMode === 'table' ? 'bg-orange-100 text-orange-700' : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 transition-colors ${
                viewMode === 'grid' ? 'bg-orange-100 text-orange-700' : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <Grid className="w-4 h-4" />
            </button>
          </div>
        </div>
        
        {/* Sort and Bulk Actions */}
        <div className="flex items-center space-x-4">
          <select
            value={`${sortOption.field}-${sortOption.direction}`}
            onChange={(e) => {
              const [field, direction] = e.target.value.split('-');
              const option = SORT_OPTIONS.find(opt => opt.field === field && opt.direction === direction);
              if (option) setSortOption(option);
            }}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            {SORT_OPTIONS.map((option) => (
              <option key={`${option.field}-${option.direction}`} value={`${option.field}-${option.direction}`}>
                {option.label}
              </option>
            ))}
          </select>
          
          {selectedItems.size > 0 && (
            <div className="relative">
              <button
                onClick={() => setShowBulkActions(!showBulkActions)}
                className="flex items-center space-x-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
              >
                <span>{selectedItems.size} selected</span>
                <MoreVertical className="w-4 h-4" />
              </button>
              
              {showBulkActions && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10">
                  {BULK_ACTIONS.map((action) => (
                    <button
                      key={action.id}
                      onClick={() => handleBulkAction(action.id)}
                      className={`flex items-center space-x-2 w-full px-4 py-2 text-sm text-left transition-colors ${
                        action.destructive
                          ? 'text-red-700 hover:bg-red-50'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <action.icon className="w-4 h-4" />
                      <span>{action.label}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      
      {/* Filters Panel */}
      {showFilters && renderFiltersPanel()}
      
      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-gray-600">
        <span>
          Showing {filteredAndSortedItems.length} of {items.length} items
          {searchQuery && ` for "${searchQuery}"`}
        </span>
        
        {selectedItems.size > 0 && (
          <button
            onClick={onClearSelection}
            className="text-orange-600 hover:text-orange-700"
          >
            Clear selection
          </button>
        )}
      </div>
      
      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner size="lg" text="Loading menu items..." />
        </div>
      )}
      
      {/* Table/Grid View */}
      {!isLoading && (
        <>
          {viewMode === 'table' ? renderTableView() : renderGridView()}
        </>
      )}
    </div>
  );
}
// Order filters component providing advanced filtering and search capabilities
// Allows filtering by status, staff, room, date range, and other criteria

'use client';

import React, { useState, useCallback } from 'react';
import {
  X,
  Search,
  Calendar,
  User,
  MapPin,
  Filter,
  RotateCcw,
  Check,
  AlertTriangle,
  Clock,
  DollarSign,
} from 'lucide-react';

import type { OrderDashboardFilters, OrderStatus, StaffRole } from '@/types/realtime-orders';

interface OrderFiltersProps {
  filters: OrderDashboardFilters;
  onFiltersChange: (filters: Partial<OrderDashboardFilters>) => void;
  onClose: () => void;
  className?: string;
}

// Available filter options
const STATUS_OPTIONS: { value: OrderStatus; label: string; color: string }[] = [
  { value: 'PENDING', label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'CONFIRMED', label: 'Confirmed', color: 'bg-blue-100 text-blue-800' },
  { value: 'PREPARING', label: 'Preparing', color: 'bg-orange-100 text-orange-800' },
  { value: 'READY', label: 'Ready', color: 'bg-green-100 text-green-800' },
  { value: 'DELIVERED', label: 'Delivered', color: 'bg-gray-100 text-gray-800' },
  { value: 'CANCELLED', label: 'Cancelled', color: 'bg-red-100 text-red-800' },
];

const ORDER_TYPE_OPTIONS = [
  { value: 'DINE_IN', label: 'Dine In' },
  { value: 'ROOM_SERVICE', label: 'Room Service' },
  { value: 'TAKEOUT', label: 'Takeout' },
];

// Quick date range presets
const DATE_PRESETS = [
  { label: 'Today', getValue: () => {
    const today = new Date();
    const start = new Date(today.setHours(0, 0, 0, 0));
    const end = new Date(today.setHours(23, 59, 59, 999));
    return { start: start.toISOString(), end: end.toISOString() };
  }},
  { label: 'Yesterday', getValue: () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const start = new Date(yesterday.setHours(0, 0, 0, 0));
    const end = new Date(yesterday.setHours(23, 59, 59, 999));
    return { start: start.toISOString(), end: end.toISOString() };
  }},
  { label: 'Last 7 days', getValue: () => {
    const end = new Date();
    const start = new Date();
    start.setDate(start.getDate() - 7);
    return { start: start.toISOString(), end: end.toISOString() };
  }},
  { label: 'This month', getValue: () => {
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth(), 1);
    const end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
    return { start: start.toISOString(), end: end.toISOString() };
  }},
];

/**
 * Multi-select Checkbox Component
 * Reusable component for selecting multiple values
 */
interface MultiSelectProps<T> {
  options: { value: T; label: string; color?: string }[];
  selectedValues: T[];
  onChange: (values: T[]) => void;
  title: string;
  icon?: React.ElementType;
}

function MultiSelect<T extends string>({
  options,
  selectedValues,
  onChange,
  title,
  icon: Icon,
}: MultiSelectProps<T>) {
  const handleToggle = (value: T) => {
    const newValues = selectedValues.includes(value)
      ? selectedValues.filter(v => v !== value)
      : [...selectedValues, value];
    onChange(newValues);
  };
  
  const handleSelectAll = () => {
    if (selectedValues.length === options.length) {
      onChange([]);
    } else {
      onChange(options.map(option => option.value));
    }
  };
  
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {Icon && <Icon className="w-4 h-4 text-gray-500" />}
          <h4 className="text-sm font-medium text-gray-900">{title}</h4>
        </div>
        
        <button
          onClick={handleSelectAll}
          className="text-xs text-orange-600 hover:text-orange-700 font-medium"
        >
          {selectedValues.length === options.length ? 'Clear All' : 'Select All'}
        </button>
      </div>
      
      <div className="space-y-2 max-h-48 overflow-y-auto">
        {options.map((option) => {
          const isSelected = selectedValues.includes(option.value);
          
          return (
            <label
              key={option.value}
              className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded-lg"
            >
              <div className="relative">
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={() => handleToggle(option.value)}
                  className="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
                />
                {isSelected && (
                  <Check className="absolute inset-0 w-4 h-4 text-orange-600 pointer-events-none" />
                )}
              </div>
              
              <span className={`flex-1 text-sm ${
                option.color 
                  ? `px-2 py-1 rounded-full ${option.color}`
                  : 'text-gray-700'
              }`}>
                {option.label}
              </span>
            </label>
          );
        })}
      </div>
    </div>
  );
}

/**
 * Date Range Picker Component
 */
interface DateRangePickerProps {
  dateRange?: { start: string; end: string };
  onChange: (range: { start: string; end: string } | undefined) => void;
}

function DateRangePicker({ dateRange, onChange }: DateRangePickerProps) {
  const [customStart, setCustomStart] = useState(
    dateRange?.start ? new Date(dateRange.start).toISOString().slice(0, 16) : ''
  );
  const [customEnd, setCustomEnd] = useState(
    dateRange?.end ? new Date(dateRange.end).toISOString().slice(0, 16) : ''
  );
  
  const handlePresetClick = (preset: typeof DATE_PRESETS[0]) => {
    const range = preset.getValue();
    onChange(range);
    setCustomStart(new Date(range.start).toISOString().slice(0, 16));
    setCustomEnd(new Date(range.end).toISOString().slice(0, 16));
  };
  
  const handleCustomChange = () => {
    if (customStart && customEnd) {
      onChange({
        start: new Date(customStart).toISOString(),
        end: new Date(customEnd).toISOString(),
      });
    }
  };
  
  const handleClear = () => {
    onChange(undefined);
    setCustomStart('');
    setCustomEnd('');
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Calendar className="w-4 h-4 text-gray-500" />
          <h4 className="text-sm font-medium text-gray-900">Date Range</h4>
        </div>
        
        {dateRange && (
          <button
            onClick={handleClear}
            className="text-xs text-red-600 hover:text-red-700 font-medium"
          >
            Clear
          </button>
        )}
      </div>
      
      {/* Quick Presets */}
      <div className="grid grid-cols-2 gap-2">
        {DATE_PRESETS.map((preset) => (
          <button
            key={preset.label}
            onClick={() => handlePresetClick(preset)}
            className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {preset.label}
          </button>
        ))}
      </div>
      
      {/* Custom Date Range */}
      <div className="space-y-2">
        <label className="block text-xs font-medium text-gray-700">Custom Range</label>
        
        <div className="grid grid-cols-1 gap-2">
          <div>
            <label className="block text-xs text-gray-600 mb-1">From</label>
            <input
              type="datetime-local"
              value={customStart}
              onChange={(e) => setCustomStart(e.target.value)}
              onBlur={handleCustomChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
          </div>
          
          <div>
            <label className="block text-xs text-gray-600 mb-1">To</label>
            <input
              type="datetime-local"
              value={customEnd}
              onChange={(e) => setCustomEnd(e.target.value)}
              onBlur={handleCustomChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Main Order Filters Component
 */
export function OrderFilters({
  filters,
  onFiltersChange,
  onClose,
  className = ''
}: OrderFiltersProps) {
  
  // ==================== LOCAL STATE ====================
  
  const [localFilters, setLocalFilters] = useState<OrderDashboardFilters>(filters);
  
  // ==================== HANDLERS ====================
  
  const handleFilterChange = useCallback((key: keyof OrderDashboardFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange({ [key]: value });
  }, [localFilters, onFiltersChange]);
  
  const handleClearAll = useCallback(() => {
    const clearedFilters: OrderDashboardFilters = {};
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  }, [onFiltersChange]);
  
  const handleApplyFilters = useCallback(() => {
    onFiltersChange(localFilters);
    onClose();
  }, [localFilters, onFiltersChange, onClose]);
  
  // ==================== COMPUTED VALUES ====================
  
  // Count active filters
  const activeFilterCount = Object.keys(filters).reduce((count, key) => {
    const value = filters[key as keyof OrderDashboardFilters];
    if (Array.isArray(value) && value.length > 0) return count + 1;
    if (typeof value === 'boolean' || typeof value === 'string' || typeof value === 'number') {
      return count + 1;
    }
    if (value && typeof value === 'object' && Object.keys(value).length > 0) {
      return count + 1;
    }
    return count;
  }, 0);
  
  // Get sample room numbers and staff (in real app, fetch from API)
  const sampleRooms = ['101', '102', '201', '202', '301', '302'];
  const sampleStaff = ['John Doe', 'Jane Smith', 'Mike Johnson', 'Sarah Wilson'];
  
  // ==================== RENDER ====================
  
  return (
    <div className={`bg-white rounded-lg shadow-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Filter className="w-5 h-5 text-gray-500" />
          <h3 className="text-lg font-semibold text-gray-900">Filter Orders</h3>
          {activeFilterCount > 0 && (
            <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full font-medium">
              {activeFilterCount} active
            </span>
          )}
        </div>
        
        <button
          onClick={onClose}
          className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X className="w-5 h-5" />
        </button>
      </div>
      
      {/* Filter Content */}
      <div className="p-4 space-y-6 max-h-96 overflow-y-auto">
        {/* Search */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Search className="w-4 h-4 text-gray-500" />
            <label className="text-sm font-medium text-gray-900">Search</label>
          </div>
          <input
            type="text"
            placeholder="Order number, customer name, room..."
            value={filters.search_query || ''}
            onChange={(e) => handleFilterChange('search_query', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
          />
        </div>
        
        {/* Order Status */}
        <MultiSelect
          options={STATUS_OPTIONS}
          selectedValues={filters.statuses || []}
          onChange={(values) => handleFilterChange('statuses', values)}
          title="Order Status"
          icon={AlertTriangle}
        />
        
        {/* Order Types */}
        <MultiSelect
          options={ORDER_TYPE_OPTIONS}
          selectedValues={filters.order_types || []}
          onChange={(values) => handleFilterChange('order_types', values)}
          title="Order Type"
          icon={Clock}
        />
        
        {/* Date Range */}
        <DateRangePicker
          dateRange={filters.date_range}
          onChange={(range) => handleFilterChange('date_range', range)}
        />
        
        {/* Room Numbers */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <MapPin className="w-4 h-4 text-gray-500" />
            <h4 className="text-sm font-medium text-gray-900">Room Numbers</h4>
          </div>
          
          <div className="grid grid-cols-3 gap-2">
            {sampleRooms.map((room) => {
              const isSelected = filters.room_numbers?.includes(room) || false;
              
              return (
                <button
                  key={room}
                  onClick={() => {
                    const current = filters.room_numbers || [];
                    const newRooms = isSelected
                      ? current.filter(r => r !== room)
                      : [...current, room];
                    handleFilterChange('room_numbers', newRooms);
                  }}
                  className={`px-3 py-2 text-sm border rounded-lg transition-colors ${
                    isSelected
                      ? 'bg-orange-100 border-orange-300 text-orange-800'
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {room}
                </button>
              );
            })}
          </div>
        </div>
        
        {/* Assigned Staff */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <User className="w-4 h-4 text-gray-500" />
            <h4 className="text-sm font-medium text-gray-900">Assigned Staff</h4>
          </div>
          
          <div className="space-y-2">
            {sampleStaff.map((staff) => {
              const isSelected = filters.staff_assigned?.includes(staff) || false;
              
              return (
                <label
                  key={staff}
                  className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded-lg"
                >
                  <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={() => {
                      const current = filters.staff_assigned || [];
                      const newStaff = isSelected
                        ? current.filter(s => s !== staff)
                        : [...current, staff];
                      handleFilterChange('staff_assigned', newStaff);
                    }}
                    className="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
                  />
                  <span className="text-sm text-gray-700">{staff}</span>
                </label>
              );
            })}
          </div>
        </div>
        
        {/* Special Filters */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-900">Special Filters</h4>
          
          <div className="space-y-2">
            <label className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded-lg">
              <input
                type="checkbox"
                checked={filters.is_urgent_only || false}
                onChange={(e) => handleFilterChange('is_urgent_only', e.target.checked)}
                className="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
              />
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-4 h-4 text-red-500" />
                <span className="text-sm text-gray-700">Urgent orders only</span>
              </div>
            </label>
          </div>
        </div>
      </div>
      
      {/* Footer Actions */}
      <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
        <button
          onClick={handleClearAll}
          className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <RotateCcw className="w-4 h-4" />
          <span>Clear All</span>
        </button>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          
          <button
            onClick={handleApplyFilters}
            className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  );
}
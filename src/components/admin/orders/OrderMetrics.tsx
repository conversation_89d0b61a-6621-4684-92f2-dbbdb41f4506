// Order metrics component displaying real-time dashboard KPIs and performance indicators
// Shows key metrics like orders per hour, average completion time, and status distribution

'use client';

import React, { useMemo } from 'react';
import {
  TrendingUp,
  TrendingDown,
  Clock,
  DollarSign,
  Users,
  AlertTriangle,
  CheckCircle,
  Activity,
  Target,
  Zap,
} from 'lucide-react';

import type { DashboardMetrics, OrderStatus } from '@/types/realtime-orders';

interface OrderMetricsProps {
  metrics: DashboardMetrics;
  className?: string;
}

/**
 * Individual Metric Card Component
 * Displays a single KPI with trend indicators and visual styling
 */
interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ElementType;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string;
  color?: 'blue' | 'green' | 'orange' | 'red' | 'purple' | 'gray';
  onClick?: () => void;
}

function MetricCard({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  trendValue,
  color = 'blue',
  onClick,
}: MetricCardProps) {
  
  // Color mappings for consistent theming
  const colorClasses = {
    blue: {
      bg: 'bg-blue-50',
      text: 'text-blue-600',
      icon: 'text-blue-500',
      border: 'border-blue-200',
    },
    green: {
      bg: 'bg-green-50',
      text: 'text-green-600',
      icon: 'text-green-500',
      border: 'border-green-200',
    },
    orange: {
      bg: 'bg-orange-50',
      text: 'text-orange-600',
      icon: 'text-orange-500',
      border: 'border-orange-200',
    },
    red: {
      bg: 'bg-red-50',
      text: 'text-red-600',
      icon: 'text-red-500',
      border: 'border-red-200',
    },
    purple: {
      bg: 'bg-purple-50',
      text: 'text-purple-600',
      icon: 'text-purple-500',
      border: 'border-purple-200',
    },
    gray: {
      bg: 'bg-gray-50',
      text: 'text-gray-600',
      icon: 'text-gray-500',
      border: 'border-gray-200',
    },
  };
  
  const colors = colorClasses[color];
  
  return (
    <div
      className={`
        relative bg-white rounded-lg border p-6 transition-all duration-200
        ${colors.border}
        ${onClick ? 'cursor-pointer hover:shadow-md hover:border-opacity-80' : ''}
      `}
      onClick={onClick}
    >
      {/* Icon */}
      <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4 ${colors.bg}`}>
        <Icon className={`w-6 h-6 ${colors.icon}`} />
      </div>
      
      {/* Content */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          
          {/* Trend Indicator */}
          {trend && trendValue && (
            <div className={`flex items-center space-x-1 text-xs ${
              trend === 'up' ? 'text-green-600' :
              trend === 'down' ? 'text-red-600' :
              'text-gray-600'
            }`}>
              {trend === 'up' && <TrendingUp className="w-3 h-3" />}
              {trend === 'down' && <TrendingDown className="w-3 h-3" />}
              {trend === 'stable' && <Activity className="w-3 h-3" />}
              <span>{trendValue}</span>
            </div>
          )}
        </div>
        
        <div>
          <div className={`text-2xl font-bold ${colors.text}`}>
            {value}
          </div>
          {subtitle && (
            <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * Status Distribution Chart Component
 * Shows the breakdown of orders by status as a simple bar chart
 */
interface StatusDistributionProps {
  distribution: Record<OrderStatus, number>;
}

function StatusDistribution({ distribution }: StatusDistributionProps) {
  const total = Object.values(distribution).reduce((sum, count) => sum + count, 0);
  
  // Status configuration with colors
  const statusConfig = {
    PENDING: { label: 'Pending', color: 'bg-yellow-500' },
    CONFIRMED: { label: 'Confirmed', color: 'bg-blue-500' },
    PREPARING: { label: 'Preparing', color: 'bg-orange-500' },
    READY: { label: 'Ready', color: 'bg-green-500' },
    DELIVERED: { label: 'Delivered', color: 'bg-gray-500' },
    CANCELLED: { label: 'Cancelled', color: 'bg-red-500' },
  };
  
  if (total === 0) {
    return (
      <div className="text-center py-4 text-gray-500">
        <Activity className="w-6 h-6 mx-auto mb-2 opacity-50" />
        <p className="text-sm">No orders to display</p>
      </div>
    );
  }
  
  return (
    <div className="space-y-3">
      <h4 className="text-sm font-medium text-gray-900">Status Distribution</h4>
      
      <div className="space-y-2">
        {Object.entries(statusConfig).map(([status, config]) => {
          const count = distribution[status as OrderStatus] || 0;
          const percentage = total > 0 ? (count / total) * 100 : 0;
          
          if (count === 0) return null;
          
          return (
            <div key={status} className="flex items-center space-x-3">
              <div className="flex-1">
                <div className="flex items-center justify-between text-sm mb-1">
                  <span className="text-gray-700">{config.label}</span>
                  <span className="text-gray-900 font-medium">{count}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${config.color}`}
                    style={{ width: `${percentage}%` }}
                  />
                </div>
              </div>
              <div className="text-xs text-gray-500 w-10 text-right">
                {percentage.toFixed(0)}%
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

/**
 * Staff Workload Component
 * Shows current workload distribution among staff members
 */
interface StaffWorkloadProps {
  workload: Record<string, number>;
}

function StaffWorkload({ workload }: StaffWorkloadProps) {
  const staffEntries = Object.entries(workload);
  const totalActiveOrders = Object.values(workload).reduce((sum, count) => sum + count, 0);
  
  if (staffEntries.length === 0 || totalActiveOrders === 0) {
    return (
      <div className="text-center py-4 text-gray-500">
        <Users className="w-6 h-6 mx-auto mb-2 opacity-50" />
        <p className="text-sm">No active assignments</p>
      </div>
    );
  }
  
  // Sort by workload (highest first)
  const sortedStaff = staffEntries
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5); // Show top 5 staff members
  
  return (
    <div className="space-y-3">
      <h4 className="text-sm font-medium text-gray-900">Staff Workload</h4>
      
      <div className="space-y-2">
        {sortedStaff.map(([staffId, orderCount]) => {
          // In a real app, you'd fetch staff names from a lookup
          const staffName = `Staff ${staffId.slice(-4)}`;
          const percentage = totalActiveOrders > 0 ? (orderCount / totalActiveOrders) * 100 : 0;
          
          return (
            <div key={staffId} className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-orange-600" />
              </div>
              
              <div className="flex-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-700 truncate">{staffName}</span>
                  <span className="text-gray-900 font-medium">
                    {orderCount} order{orderCount !== 1 ? 's' : ''}
                  </span>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                  <div
                    className="h-1.5 rounded-full bg-orange-500 transition-all duration-300"
                    style={{ width: `${percentage}%` }}
                  />
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      {staffEntries.length > 5 && (
        <p className="text-xs text-gray-500">
          +{staffEntries.length - 5} more staff members
        </p>
      )}
    </div>
  );
}

/**
 * Main Order Metrics Component
 */
export function OrderMetrics({ metrics, className = '' }: OrderMetricsProps) {
  
  // ==================== COMPUTED VALUES ====================
  
  // Calculate derived metrics
  const derivedMetrics = useMemo(() => {
    const {
      ordersPerMinute,
      averageOrderTime,
      statusDistribution,
      staffWorkload,
      connectionLatency,
      updateFrequency,
    } = metrics;
    
    // Convert orders per minute to orders per hour
    const ordersPerHour = ordersPerMinute * 60;
    
    // Calculate total active orders (not delivered or cancelled)
    const activeOrders = Object.entries(statusDistribution)
      .filter(([status]) => status !== 'DELIVERED' && status !== 'CANCELLED')
      .reduce((sum, [, count]) => sum + count, 0);
    
    // Calculate total orders
    const totalOrders = Object.values(statusDistribution).reduce((sum, count) => sum + count, 0);
    
    // Calculate completion rate
    const completedOrders = statusDistribution.DELIVERED || 0;
    const completionRate = totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0;
    
    // Determine performance status
    const isHighVolume = ordersPerHour > 20; // More than 20 orders per hour
    const isSlowService = averageOrderTime > 45; // More than 45 minutes average
    const hasConnectionIssues = connectionLatency > 1000; // More than 1 second latency
    
    return {
      ordersPerHour,
      activeOrders,
      totalOrders,
      completionRate,
      isHighVolume,
      isSlowService,
      hasConnectionIssues,
    };
  }, [metrics]);
  
  // ==================== TREND CALCULATIONS ====================
  
  // In a real application, you would compare with previous periods
  // For demo purposes, we'll simulate some trends
  const trendData = useMemo(() => {
    const { ordersPerHour, averageOrderTime, completionRate } = derivedMetrics;
    
    // Simulate trend calculations (in real app, compare with historical data)
    return {
      ordersTrend: ordersPerHour > 15 ? { trend: 'up' as const, value: '+12%' } : 
                   ordersPerHour < 5 ? { trend: 'down' as const, value: '-8%' } :
                   { trend: 'stable' as const, value: '±2%' },
      
      timeTrend: averageOrderTime > 40 ? { trend: 'up' as const, value: '+15%' } :
                 averageOrderTime < 25 ? { trend: 'down' as const, value: '-10%' } :
                 { trend: 'stable' as const, value: '±5%' },
      
      completionTrend: completionRate > 80 ? { trend: 'up' as const, value: '+5%' } :
                       completionRate < 60 ? { trend: 'down' as const, value: '-12%' } :
                       { trend: 'stable' as const, value: '±3%' },
    };
  }, [derivedMetrics]);
  
  // ==================== RENDER ====================
  
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Orders per Hour */}
        <MetricCard
          title="Orders per Hour"
          value={derivedMetrics.ordersPerHour.toFixed(1)}
          subtitle={derivedMetrics.isHighVolume ? "High volume period" : "Normal volume"}
          icon={TrendingUp}
          trend={trendData.ordersTrend.trend}
          trendValue={trendData.ordersTrend.value}
          color={derivedMetrics.isHighVolume ? "orange" : "blue"}
        />
        
        {/* Average Order Time */}
        <MetricCard
          title="Avg. Order Time"
          value={`${Math.round(metrics.averageOrderTime)}m`}
          subtitle={derivedMetrics.isSlowService ? "Above target" : "Within target"}
          icon={Clock}
          trend={trendData.timeTrend.trend}
          trendValue={trendData.timeTrend.value}
          color={derivedMetrics.isSlowService ? "red" : "green"}
        />
        
        {/* Active Orders */}
        <MetricCard
          title="Active Orders"
          value={derivedMetrics.activeOrders}
          subtitle={`${derivedMetrics.totalOrders} total today`}
          icon={Activity}
          color="orange"
        />
        
        {/* Completion Rate */}
        <MetricCard
          title="Completion Rate"
          value={`${derivedMetrics.completionRate.toFixed(1)}%`}
          subtitle="Orders delivered successfully"
          icon={Target}
          trend={trendData.completionTrend.trend}
          trendValue={trendData.completionTrend.value}
          color={derivedMetrics.completionRate > 80 ? "green" : "red"}
        />
      </div>
      
      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Status Distribution */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <StatusDistribution distribution={metrics.statusDistribution} />
        </div>
        
        {/* Staff Workload */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <StaffWorkload workload={metrics.staffWorkload} />
        </div>
      </div>
      
      {/* System Performance */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h4 className="text-sm font-medium text-gray-900 mb-4">System Performance</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Connection Latency */}
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full mb-2 ${
              derivedMetrics.hasConnectionIssues ? 'bg-red-100' : 'bg-green-100'
            }`}>
              <Zap className={`w-4 h-4 ${
                derivedMetrics.hasConnectionIssues ? 'text-red-600' : 'text-green-600'
              }`} />
            </div>
            <div className="text-sm text-gray-600">Connection</div>
            <div className={`text-lg font-semibold ${
              derivedMetrics.hasConnectionIssues ? 'text-red-600' : 'text-green-600'
            }`}>
              {metrics.connectionLatency}ms
            </div>
          </div>
          
          {/* Update Frequency */}
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="inline-flex items-center justify-center w-8 h-8 rounded-full mb-2 bg-blue-100">
              <Activity className="w-4 h-4 text-blue-600" />
            </div>
            <div className="text-sm text-gray-600">Updates</div>
            <div className="text-lg font-semibold text-blue-600">
              {metrics.updateFrequency.toFixed(1)}/s
            </div>
          </div>
          
          {/* System Status */}
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="inline-flex items-center justify-center w-8 h-8 rounded-full mb-2 bg-green-100">
              <CheckCircle className="w-4 h-4 text-green-600" />
            </div>
            <div className="text-sm text-gray-600">Status</div>
            <div className="text-lg font-semibold text-green-600">
              Healthy
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
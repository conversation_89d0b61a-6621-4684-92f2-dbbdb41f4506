// Individual QR Code Preview and Editing Component
// Provides detailed view and customization options for a single QR code
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  QrCode,
  Download,
  RefreshCw,
  Settings,
  Eye,
  Edit3,
  Copy,
  Share2,
  Printer,
  Save,
  X,
  <PERSON><PERSON>,
  Image as ImageIcon,
  Maximize2,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';

import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import type {
  QRCodeData,
  QRCodeOptions,
  QRCustomizationFormData,
  ExportOptions,
  Room,
  DEFAULT_QR_OPTIONS,
} from '@/types/qr-management';
import { QRCodeGenerationService } from '@/services/qr-generation';

interface QRCodePreviewProps {
  room: Room;
  qrCodeData?: QRCodeData;
  isOpen: boolean;
  onClose: () => void;
  onSave?: (qrCode: QRCodeData) => void;
  onExport?: (format: 'png' | 'svg' | 'pdf') => void;
  className?: string;
}

export function QRCodePreview({
  room,
  qrCodeData,
  isOpen,
  onClose,
  onSave,
  onExport,
  className = ''
}: QRCodePreviewProps) {
  
  // =====================================================
  // STATE MANAGEMENT
  // =====================================================
  
  const [activeTab, setActiveTab] = useState<'preview' | 'customize' | 'export'>('preview');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [currentQRCode, setCurrentQRCode] = useState<QRCodeData | null>(qrCodeData || null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  // Customization form state
  const [customization, setCustomization] = useState<QRCustomizationFormData>({
    size: qrCodeData?.options.size || 256,
    foregroundColor: qrCodeData?.options.foregroundColor || '#000000',
    backgroundColor: qrCodeData?.options.backgroundColor || '#FFFFFF',
    errorCorrectionLevel: qrCodeData?.options.errorCorrectionLevel || 'M',
    includelogo: qrCodeData?.options.includelogo || false,
    logoSize: qrCodeData?.options.logoSize || 20,
    margin: qrCodeData?.options.margin || 4,
    border: qrCodeData?.options.border || false,
    borderWidth: qrCodeData?.options.borderWidth || 1,
    borderColor: qrCodeData?.options.borderColor || '#000000',
  });
  
  const qrService = new QRCodeGenerationService();
  
  // =====================================================
  // HANDLERS
  // =====================================================
  
  const handleCustomizationChange = useCallback((
    field: keyof QRCustomizationFormData,
    value: any
  ) => {
    setCustomization(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
  }, []);
  
  const generatePreview = useCallback(async () => {
    setIsGenerating(true);
    
    try {
      const options: QRCodeOptions = {
        ...DEFAULT_QR_OPTIONS,
        ...customization,
        baseUrl: DEFAULT_QR_OPTIONS.baseUrl,
        qrCode: currentQRCode?.qrCode || `preview_${room.id}`,
      };
      
      const result = await qrService.generateQRCode(room, options);
      
      if (result.success) {
        setCurrentQRCode(result.qrCode);
        setPreviewUrl(result.dataUrl || null);
        setHasUnsavedChanges(false);
      }
    } catch (error) {
      console.error('Preview generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  }, [room, customization, currentQRCode, qrService]);
  
  const handleSave = useCallback(async () => {
    if (!currentQRCode) return;
    
    try {
      onSave?.(currentQRCode);
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Save failed:', error);
    }
  }, [currentQRCode, onSave]);
  
  const handleExport = useCallback(async (format: 'png' | 'svg' | 'pdf') => {
    if (!currentQRCode) return;
    
    setIsExporting(true);
    
    try {
      onExport?.(format);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  }, [currentQRCode, onExport]);
  
  const copyToClipboard = useCallback(async () => {
    if (!currentQRCode?.url) return;
    
    try {
      await navigator.clipboard.writeText(currentQRCode.url);
      // Show success message
    } catch (error) {
      console.error('Copy failed:', error);
    }
  }, [currentQRCode]);
  
  // =====================================================
  // EFFECTS
  // =====================================================
  
  useEffect(() => {
    if (isOpen && !currentQRCode) {
      generatePreview();
    }
  }, [isOpen, currentQRCode, generatePreview]);
  
  useEffect(() => {
    if (currentQRCode?.dataUrl) {
      setPreviewUrl(currentQRCode.dataUrl);
    }
  }, [currentQRCode]);
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderPreviewTab = () => (
    <div className=\"space-y-6\">
      {/* QR Code Display */}
      <div className=\"flex justify-center\">
        <div className=\"relative bg-white p-8 rounded-lg border-2 border-gray-200 shadow-sm\">
          {previewUrl ? (
            <img
              src={previewUrl}
              alt={`QR Code for Room ${room.roomNumber}`}
              className=\"w-64 h-64 object-contain\"
            />
          ) : (
            <div className=\"w-64 h-64 bg-gray-100 rounded flex items-center justify-center\">
              {isGenerating ? (
                <LoadingSpinner size=\"lg\" />
              ) : (
                <QrCode className=\"w-16 h-16 text-gray-400\" />
              )}
            </div>
          )}
          
          {/* Zoom button */}
          <button className=\"absolute top-2 right-2 p-2 bg-white rounded-full shadow-md hover:shadow-lg transition-shadow\">
            <Maximize2 className=\"w-4 h-4 text-gray-600\" />
          </button>
        </div>
      </div>
      
      {/* Room Information */}
      <div className=\"bg-gray-50 rounded-lg p-4\">
        <h3 className=\"font-semibold text-gray-900 mb-3\">Room Details</h3>
        <div className=\"grid grid-cols-2 gap-4 text-sm\">
          <div>
            <span className=\"text-gray-600\">Room Number:</span>
            <span className=\"ml-2 font-medium\">{room.roomNumber}</span>
          </div>
          {room.floor && (
            <div>
              <span className=\"text-gray-600\">Floor:</span>
              <span className=\"ml-2 font-medium\">{room.floor}</span>
            </div>
          )}
          <div>
            <span className=\"text-gray-600\">Capacity:</span>
            <span className=\"ml-2 font-medium\">{room.capacity} guests</span>
          </div>
          <div>
            <span className=\"text-gray-600\">Status:</span>
            <span className=\"ml-2 font-medium\">{room.status}</span>
          </div>
        </div>
      </div>
      
      {/* QR Code Information */}
      {currentQRCode && (
        <div className=\"bg-blue-50 rounded-lg p-4\">
          <h3 className=\"font-semibold text-gray-900 mb-3\">QR Code Information</h3>
          <div className=\"space-y-2 text-sm\">
            <div className=\"flex items-center justify-between\">
              <span className=\"text-gray-600\">URL:</span>
              <div className=\"flex items-center space-x-2\">
                <code className=\"px-2 py-1 bg-white rounded text-xs font-mono truncate max-w-xs\">
                  {currentQRCode.url}
                </code>
                <button
                  onClick={copyToClipboard}
                  className=\"p-1 text-gray-600 hover:text-blue-600 transition-colors\"
                  title=\"Copy URL\"
                >
                  <Copy className=\"w-4 h-4\" />
                </button>
              </div>
            </div>
            <div className=\"flex justify-between\">
              <span className=\"text-gray-600\">Generated:</span>
              <span className=\"font-medium\">
                {new Date(currentQRCode.generatedAt).toLocaleString()}
              </span>
            </div>
            <div className=\"flex justify-between\">
              <span className=\"text-gray-600\">Size:</span>
              <span className=\"font-medium\">{customization.size}x{customization.size}px</span>
            </div>
          </div>
        </div>
      )}
      
      {/* Quick Actions */}
      <div className=\"flex justify-center space-x-3\">
        <button
          onClick={() => handleExport('png')}
          disabled={!currentQRCode || isExporting}
          className=\"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50\"
        >
          <Download className=\"w-4 h-4\" />
          <span>Download PNG</span>
        </button>
        
        <button
          onClick={() => handleExport('svg')}
          disabled={!currentQRCode || isExporting}
          className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\"
        >
          <ImageIcon className=\"w-4 h-4\" />
          <span>Download SVG</span>
        </button>
        
        <button
          onClick={() => handleExport('pdf')}
          disabled={!currentQRCode || isExporting}
          className=\"flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50\"
        >
          <Printer className=\"w-4 h-4\" />
          <span>Download PDF</span>
        </button>
      </div>
    </div>
  );
  
  const renderCustomizeTab = () => (
    <div className=\"space-y-6\">
      {/* Size and Colors */}
      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">
        <div className=\"space-y-4\">
          <h3 className=\"font-semibold text-gray-900\">Size & Colors</h3>
          
          {/* Size */}
          <div>
            <label className=\"block text-sm font-medium text-gray-700 mb-1\">
              Size (pixels)
            </label>
            <input
              type=\"range\"
              min=\"128\"
              max=\"512\"
              step=\"32\"
              value={customization.size}
              onChange={(e) => handleCustomizationChange('size', parseInt(e.target.value))}
              className=\"w-full\"
            />
            <div className=\"flex justify-between text-xs text-gray-500 mt-1\">
              <span>128px</span>
              <span className=\"font-medium\">{customization.size}px</span>
              <span>512px</span>
            </div>
          </div>
          
          {/* Foreground Color */}
          <div>
            <label className=\"block text-sm font-medium text-gray-700 mb-1\">
              Foreground Color
            </label>
            <div className=\"flex items-center space-x-2\">
              <input
                type=\"color\"
                value={customization.foregroundColor}
                onChange={(e) => handleCustomizationChange('foregroundColor', e.target.value)}
                className=\"w-12 h-10 border border-gray-300 rounded cursor-pointer\"
              />
              <input
                type=\"text\"
                value={customization.foregroundColor}
                onChange={(e) => handleCustomizationChange('foregroundColor', e.target.value)}
                className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
                placeholder=\"#000000\"
              />
            </div>
          </div>
          
          {/* Background Color */}
          <div>
            <label className=\"block text-sm font-medium text-gray-700 mb-1\">
              Background Color
            </label>
            <div className=\"flex items-center space-x-2\">
              <input
                type=\"color\"
                value={customization.backgroundColor}
                onChange={(e) => handleCustomizationChange('backgroundColor', e.target.value)}
                className=\"w-12 h-10 border border-gray-300 rounded cursor-pointer\"
              />
              <input
                type=\"text\"
                value={customization.backgroundColor}
                onChange={(e) => handleCustomizationChange('backgroundColor', e.target.value)}
                className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
                placeholder=\"#FFFFFF\"
              />
            </div>
          </div>
        </div>
        
        <div className=\"space-y-4\">
          <h3 className=\"font-semibold text-gray-900\">Options</h3>
          
          {/* Error Correction Level */}
          <div>
            <label className=\"block text-sm font-medium text-gray-700 mb-1\">
              Error Correction Level
            </label>
            <select
              value={customization.errorCorrectionLevel}
              onChange={(e) => handleCustomizationChange('errorCorrectionLevel', e.target.value as 'L' | 'M' | 'Q' | 'H')}
              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
            >
              <option value=\"L\">Low (7%)</option>
              <option value=\"M\">Medium (15%)</option>
              <option value=\"Q\">Quartile (25%)</option>
              <option value=\"H\">High (30%)</option>
            </select>
          </div>
          
          {/* Margin */}
          <div>
            <label className=\"block text-sm font-medium text-gray-700 mb-1\">
              Margin (quiet zone)
            </label>
            <input
              type=\"range\"
              min=\"0\"
              max=\"10\"
              step=\"1\"
              value={customization.margin}
              onChange={(e) => handleCustomizationChange('margin', parseInt(e.target.value))}
              className=\"w-full\"
            />
            <div className=\"flex justify-between text-xs text-gray-500 mt-1\">
              <span>0</span>
              <span className=\"font-medium\">{customization.margin}</span>
              <span>10</span>
            </div>
          </div>
          
          {/* Logo Options */}
          <div>
            <label className=\"flex items-center space-x-2\">
              <input
                type=\"checkbox\"
                checked={customization.includelogo}
                onChange={(e) => handleCustomizationChange('includelogo', e.target.checked)}
                className=\"w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500\"
              />
              <span className=\"text-sm font-medium text-gray-700\">Include Logo</span>
            </label>
            
            {customization.includelogo && (
              <div className=\"mt-2\">
                <label className=\"block text-sm font-medium text-gray-700 mb-1\">
                  Logo Size (% of QR code)
                </label>
                <input
                  type=\"range\"
                  min=\"10\"
                  max=\"30\"
                  step=\"5\"
                  value={customization.logoSize}
                  onChange={(e) => handleCustomizationChange('logoSize', parseInt(e.target.value))}
                  className=\"w-full\"
                />
                <div className=\"flex justify-between text-xs text-gray-500 mt-1\">
                  <span>10%</span>
                  <span className=\"font-medium\">{customization.logoSize}%</span>
                  <span>30%</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Border Options */}
      <div className=\"border-t border-gray-200 pt-6\">
        <h3 className=\"font-semibold text-gray-900 mb-4\">Border</h3>
        
        <div className=\"space-y-4\">
          <label className=\"flex items-center space-x-2\">
            <input
              type=\"checkbox\"
              checked={customization.border}
              onChange={(e) => handleCustomizationChange('border', e.target.checked)}
              className=\"w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500\"
            />
            <span className=\"text-sm font-medium text-gray-700\">Add Border</span>
          </label>
          
          {customization.border && (
            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 ml-6\">
              <div>
                <label className=\"block text-sm font-medium text-gray-700 mb-1\">
                  Border Width
                </label>
                <input
                  type=\"number\"
                  min=\"1\"
                  max=\"10\"
                  value={customization.borderWidth}
                  onChange={(e) => handleCustomizationChange('borderWidth', parseInt(e.target.value))}
                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
                />
              </div>
              
              <div>
                <label className=\"block text-sm font-medium text-gray-700 mb-1\">
                  Border Color
                </label>
                <div className=\"flex items-center space-x-2\">
                  <input
                    type=\"color\"
                    value={customization.borderColor}
                    onChange={(e) => handleCustomizationChange('borderColor', e.target.value)}
                    className=\"w-12 h-10 border border-gray-300 rounded cursor-pointer\"
                  />
                  <input
                    type=\"text\"
                    value={customization.borderColor}
                    onChange={(e) => handleCustomizationChange('borderColor', e.target.value)}
                    className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
                    placeholder=\"#000000\"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Preview and Generate Button */}
      <div className=\"flex justify-center pt-6 border-t border-gray-200\">
        <button
          onClick={generatePreview}
          disabled={isGenerating}
          className=\"flex items-center space-x-2 px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50\"
        >
          {isGenerating ? (
            <LoadingSpinner size=\"sm\" />
          ) : (
            <RefreshCw className=\"w-4 h-4\" />
          )}
          <span>Generate Preview</span>
        </button>
      </div>
    </div>
  );
  
  const renderExportTab = () => (
    <div className=\"space-y-6\">
      <h3 className=\"font-semibold text-gray-900\">Export Options</h3>
      
      {/* Export Formats */}
      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">
        <div className=\"border border-gray-200 rounded-lg p-4 hover:border-orange-300 transition-colors\">
          <div className=\"flex items-center justify-between mb-3\">
            <h4 className=\"font-medium text-gray-900\">PNG Image</h4>
            <ImageIcon className=\"w-5 h-5 text-green-600\" />
          </div>
          <p className=\"text-sm text-gray-600 mb-4\">
            High-quality raster image. Best for web use and printing.
          </p>
          <button
            onClick={() => handleExport('png')}
            disabled={!currentQRCode || isExporting}
            className=\"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50\"
          >
            Export PNG
          </button>
        </div>
        
        <div className=\"border border-gray-200 rounded-lg p-4 hover:border-orange-300 transition-colors\">
          <div className=\"flex items-center justify-between mb-3\">
            <h4 className=\"font-medium text-gray-900\">SVG Vector</h4>
            <ImageIcon className=\"w-5 h-5 text-blue-600\" />
          </div>
          <p className=\"text-sm text-gray-600 mb-4\">
            Scalable vector format. Perfect for print materials and large displays.
          </p>
          <button
            onClick={() => handleExport('svg')}
            disabled={!currentQRCode || isExporting}
            className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\"
          >
            Export SVG
          </button>
        </div>
        
        <div className=\"border border-gray-200 rounded-lg p-4 hover:border-orange-300 transition-colors\">
          <div className=\"flex items-center justify-between mb-3\">
            <h4 className=\"font-medium text-gray-900\">PDF Document</h4>
            <Printer className=\"w-5 h-5 text-red-600\" />
          </div>
          <p className=\"text-sm text-gray-600 mb-4\">
            Print-ready document with room information and QR code.
          </p>
          <button
            onClick={() => handleExport('pdf')}
            disabled={!currentQRCode || isExporting}
            className=\"w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50\"
          >
            Export PDF
          </button>
        </div>
      </div>
      
      {/* Share Options */}
      <div className=\"border-t border-gray-200 pt-6\">
        <h3 className=\"font-semibold text-gray-900 mb-4\">Share & Print</h3>
        
        <div className=\"flex flex-wrap gap-3\">
          <button
            onClick={copyToClipboard}
            className=\"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"
          >
            <Copy className=\"w-4 h-4\" />
            <span>Copy URL</span>
          </button>
          
          <button className=\"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\">
            <Share2 className=\"w-4 h-4\" />
            <span>Share Link</span>
          </button>
          
          <button className=\"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\">
            <Printer className=\"w-4 h-4\" />
            <span>Print</span>
          </button>
        </div>
      </div>
    </div>
  );
  
  if (!isOpen) return null;
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  return (
    <div className=\"fixed inset-0 z-50 overflow-y-auto\" aria-labelledby=\"modal-title\" role=\"dialog\" aria-modal=\"true\">
      <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">
        {/* Background overlay */}
        <div 
          className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" 
          aria-hidden=\"true\"
          onClick={onClose}
        />
        
        {/* Modal panel */}
        <div className=\"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full\">
          {/* Header */}
          <div className=\"bg-white px-6 py-4 border-b border-gray-200\">
            <div className=\"flex items-center justify-between\">
              <div>
                <h1 className=\"text-xl font-semibold text-gray-900\">
                  QR Code for Room {room.roomNumber}
                </h1>
                {room.floor && (
                  <p className=\"text-sm text-gray-600\">Floor {room.floor}</p>
                )}
              </div>
              
              <div className=\"flex items-center space-x-2\">
                {hasUnsavedChanges && (
                  <div className=\"flex items-center space-x-2 text-amber-600\">
                    <AlertCircle className=\"w-4 h-4\" />
                    <span className=\"text-sm\">Unsaved changes</span>
                  </div>
                )}
                
                <button
                  onClick={onClose}
                  className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\"
                >
                  <X className=\"w-5 h-5\" />
                </button>
              </div>
            </div>
            
            {/* Tabs */}
            <div className=\"flex space-x-1 mt-4 bg-gray-100 rounded-lg p-1\">
              <button
                onClick={() => setActiveTab('preview')}
                className={`flex-1 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === 'preview'
                    ? 'bg-white text-orange-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Eye className=\"w-4 h-4 inline mr-2\" />
                Preview
              </button>
              <button
                onClick={() => setActiveTab('customize')}
                className={`flex-1 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === 'customize'
                    ? 'bg-white text-orange-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Palette className=\"w-4 h-4 inline mr-2\" />
                Customize
              </button>
              <button
                onClick={() => setActiveTab('export')}
                className={`flex-1 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === 'export'
                    ? 'bg-white text-orange-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Download className=\"w-4 h-4 inline mr-2\" />
                Export
              </button>
            </div>
          </div>
          
          {/* Content */}
          <div className=\"bg-white px-6 py-6 min-h-[400px]\">
            {activeTab === 'preview' && renderPreviewTab()}
            {activeTab === 'customize' && renderCustomizeTab()}
            {activeTab === 'export' && renderExportTab()}
          </div>
          
          {/* Footer */}
          <div className=\"bg-gray-50 px-6 py-4 flex items-center justify-between\">
            <div className=\"flex items-center space-x-2 text-sm text-gray-600\">
              {currentQRCode && (
                <>
                  <CheckCircle className=\"w-4 h-4 text-green-500\" />
                  <span>QR code generated successfully</span>
                </>
              )}
            </div>
            
            <div className=\"flex items-center space-x-3\">
              <button
                onClick={onClose}
                className=\"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\"
              >
                Close
              </button>
              
              {hasUnsavedChanges && (
                <button
                  onClick={handleSave}
                  className=\"flex items-center space-x-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors\"
                >
                  <Save className=\"w-4 h-4\" />
                  <span>Save Changes</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
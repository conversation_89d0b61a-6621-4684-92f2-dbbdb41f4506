// Bulk QR Code Generation Component
// Provides interface for generating multiple QR codes with batch operations and progress tracking
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Zap,
  Download,
  Settings,
  FileText,
  CheckCircle,
  AlertCircle,
  Clock,
  Users,
  Grid,
  List,
  Filter,
  Search,
  X,
  Play,
  Pause,
  RotateCcw,
  Eye,
  Building,
} from 'lucide-react';

import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import type {
  Room,
  QRCodeData,
  BulkQROperation,
  BulkQRGenerationResult,
  QRCodeOptions,
  BulkLayoutOptions,
  ExportOptions,
  RoomFilters,
  DEFAULT_QR_OPTIONS,
  DEFAULT_LAYOUT_OPTIONS,
  ROOM_STATUSES,
} from '@/types/qr-management';
import { QRCodeGenerationService } from '@/services/qr-generation';

interface BulkQRGeneratorProps {
  rooms: Room[];
  selectedRoomIds: string[];
  isOpen: boolean;
  onClose: () => void;
  onComplete: (results: BulkQRGenerationResult) => void;
  className?: string;
}

interface GenerationProgress {
  current: number;
  total: number;
  isActive: boolean;
  isPaused: boolean;
  completed: QRCodeData[];
  failed: Array<{ roomId: string; error: string }>;
  startTime?: number;
  estimatedTimeRemaining?: number;
}

export function BulkQRGenerator({
  rooms,
  selectedRoomIds,
  isOpen,
  onClose,
  onComplete,
  className = ''
}: BulkQRGeneratorProps) {
  
  // =====================================================
  // STATE MANAGEMENT
  // =====================================================
  
  const [activeTab, setActiveTab] = useState<'selection' | 'options' | 'layout' | 'progress'>('selection');
  const [selectedRooms, setSelectedRooms] = useState<Set<string>>(new Set(selectedRoomIds));
  const [filters, setFilters] = useState<RoomFilters>({});
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  
  // Generation options
  const [qrOptions, setQROptions] = useState<QRCodeOptions>(DEFAULT_QR_OPTIONS);
  const [layoutOptions, setLayoutOptions] = useState<BulkLayoutOptions>(DEFAULT_LAYOUT_OPTIONS);
  const [exportFormat, setExportFormat] = useState<'pdf' | 'zip'>('pdf');
  
  // Progress tracking
  const [progress, setProgress] = useState<GenerationProgress>({
    current: 0,
    total: 0,
    isActive: false,
    isPaused: false,
    completed: [],
    failed: [],
  });
  
  const [generationResults, setGenerationResults] = useState<BulkQRGenerationResult | null>(null);
  
  const qrService = new QRCodeGenerationService();
  
  // =====================================================
  // COMPUTED VALUES
  // =====================================================
  
  const filteredRooms = rooms.filter(room => {
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      if (!room.roomNumber.toLowerCase().includes(searchLower) && 
          !room.floor?.toLowerCase().includes(searchLower)) {
        return false;
      }
    }
    
    if (filters.floor && room.floor !== filters.floor) return false;
    if (filters.status && !filters.status.includes(room.status)) return false;
    if (filters.capacity) {
      if (filters.capacity.min && room.capacity < filters.capacity.min) return false;
      if (filters.capacity.max && room.capacity > filters.capacity.max) return false;
    }
    
    return true;
  });
  
  const roomsToGenerate = rooms.filter(room => selectedRooms.has(room.id));
  
  // =====================================================
  // HANDLERS
  // =====================================================
  
  const toggleRoomSelection = useCallback((roomId: string) => {
    const newSelection = new Set(selectedRooms);
    if (newSelection.has(roomId)) {
      newSelection.delete(roomId);
    } else {
      newSelection.add(roomId);
    }
    setSelectedRooms(newSelection);
  }, [selectedRooms]);
  
  const selectAllFiltered = useCallback(() => {
    const allIds = filteredRooms.map(room => room.id);
    setSelectedRooms(new Set(allIds));
  }, [filteredRooms]);
  
  const clearSelection = useCallback(() => {
    setSelectedRooms(new Set());
  }, []);
  
  const updateQROptions = useCallback((updates: Partial<QRCodeOptions>) => {
    setQROptions(prev => ({ ...prev, ...updates }));
  }, []);
  
  const updateLayoutOptions = useCallback((updates: Partial<BulkLayoutOptions>) => {
    setLayoutOptions(prev => ({ ...prev, ...updates }));
  }, []);
  
  const startGeneration = useCallback(async () => {
    if (roomsToGenerate.length === 0) return;
    
    setActiveTab('progress');
    setProgress({
      current: 0,
      total: roomsToGenerate.length,
      isActive: true,
      isPaused: false,
      completed: [],
      failed: [],
      startTime: Date.now(),
    });
    
    try {
      const result = await qrService.bulkGenerateQRCodes(
        roomsToGenerate,
        qrOptions,
        (current, total) => {
          const elapsed = Date.now() - (progress.startTime || Date.now());
          const rate = current / elapsed * 1000; // items per second
          const remaining = total - current;
          const estimatedTimeRemaining = remaining / rate * 1000; // milliseconds
          
          setProgress(prev => ({
            ...prev,
            current,
            total,
            estimatedTimeRemaining: isFinite(estimatedTimeRemaining) ? estimatedTimeRemaining : undefined,
          }));
        }
      );
      
      setGenerationResults(result);
      setProgress(prev => ({
        ...prev,
        isActive: false,
        completed: result.results.filter(r => r.success).map(r => r.qrCode),
        failed: result.failed,
      }));
      
      onComplete(result);
      
    } catch (error) {
      setProgress(prev => ({
        ...prev,
        isActive: false,
        failed: roomsToGenerate.map(room => ({
          roomId: room.id,
          error: error instanceof Error ? error.message : 'Generation failed',
        })),
      }));
    }
  }, [roomsToGenerate, qrOptions, qrService, onComplete, progress.startTime]);
  
  const pauseGeneration = useCallback(() => {
    setProgress(prev => ({ ...prev, isPaused: true }));
  }, []);
  
  const resumeGeneration = useCallback(() => {
    setProgress(prev => ({ ...prev, isPaused: false }));
  }, []);
  
  const resetGeneration = useCallback(() => {
    setProgress({
      current: 0,
      total: 0,
      isActive: false,
      isPaused: false,
      completed: [],
      failed: [],
    });
    setGenerationResults(null);
    setActiveTab('selection');
  }, []);
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderSelectionTab = () => (
    <div className=\"space-y-6\">
      {/* Filters and controls */}
      <div className=\"flex items-center justify-between\">
        <div className=\"flex items-center space-x-4\">
          {/* Search */}
          <div className=\"relative\">
            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />
            <input
              type=\"text\"
              placeholder=\"Search rooms...\"
              value={filters.search || ''}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
            />
          </div>
          
          {/* Floor filter */}
          <select
            value={filters.floor || ''}
            onChange={(e) => setFilters(prev => ({ ...prev, floor: e.target.value || undefined }))}
            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
          >
            <option value=\"\">All Floors</option>
            {Array.from(new Set(rooms.map(r => r.floor).filter(Boolean))).map(floor => (
              <option key={floor} value={floor}>{floor}</option>
            ))}
          </select>
          
          {/* Status filter */}
          <select
            value={filters.status?.[0] || ''}
            onChange={(e) => setFilters(prev => ({ 
              ...prev, 
              status: e.target.value ? [e.target.value as any] : undefined 
            }))}
            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
          >
            <option value=\"\">All Statuses</option>
            {ROOM_STATUSES.map(status => (
              <option key={status.value} value={status.value}>{status.label}</option>
            ))}
          </select>
        </div>
        
        <div className=\"flex items-center space-x-2\">
          <button
            onClick={selectAllFiltered}
            className=\"px-3 py-2 text-sm text-orange-600 hover:text-orange-700 transition-colors\"
          >
            Select All ({filteredRooms.length})
          </button>
          
          <button
            onClick={clearSelection}
            className=\"px-3 py-2 text-sm text-gray-600 hover:text-gray-700 transition-colors\"
          >
            Clear Selection
          </button>
          
          {/* View mode toggle */}
          <div className=\"flex items-center space-x-1 bg-gray-100 rounded-lg p-1\">
            <button
              onClick={() => setViewMode('list')}
              className={`p-1 rounded transition-colors ${
                viewMode === 'list' ? 'bg-white text-orange-600' : 'text-gray-600'
              }`}
            >
              <List className=\"w-4 h-4\" />
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`p-1 rounded transition-colors ${
                viewMode === 'grid' ? 'bg-white text-orange-600' : 'text-gray-600'
              }`}
            >
              <Grid className=\"w-4 h-4\" />
            </button>
          </div>
        </div>
      </div>
      
      {/* Selection summary */}
      {selectedRooms.size > 0 && (
        <div className=\"bg-orange-50 border border-orange-200 rounded-lg p-4\">
          <div className=\"flex items-center justify-between\">
            <div className=\"flex items-center space-x-2\">
              <CheckCircle className=\"w-5 h-5 text-orange-600\" />
              <span className=\"font-medium text-orange-700\">
                {selectedRooms.size} room{selectedRooms.size !== 1 ? 's' : ''} selected for QR generation
              </span>
            </div>
            <button
              onClick={() => setActiveTab('options')}
              disabled={selectedRooms.size === 0}
              className=\"px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50\"
            >
              Configure Options →
            </button>
          </div>
        </div>
      )}
      
      {/* Room list */}
      <div className={`
        ${viewMode === 'grid' 
          ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4' 
          : 'space-y-2'
        }
      `}>
        {filteredRooms.length === 0 ? (
          <div className=\"text-center py-8 text-gray-500\">
            <Building className=\"w-12 h-12 mx-auto mb-4 text-gray-400\" />
            <p>No rooms match the current filters</p>
          </div>
        ) : (
          filteredRooms.map(room => {
            const isSelected = selectedRooms.has(room.id);
            const statusInfo = ROOM_STATUSES.find(s => s.value === room.status);
            
            return viewMode === 'grid' ? (
              <div
                key={room.id}
                className={`
                  p-4 rounded-lg border-2 cursor-pointer transition-all
                  ${isSelected 
                    ? 'border-orange-500 bg-orange-50' 
                    : 'border-gray-200 bg-white hover:border-gray-300'
                  }
                `}
                onClick={() => toggleRoomSelection(room.id)}
              >
                <div className=\"flex items-center justify-between mb-3\">
                  <input
                    type=\"checkbox\"
                    checked={isSelected}
                    onChange={() => toggleRoomSelection(room.id)}
                    className=\"w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500\"
                  />
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusInfo?.color}`}>
                    {statusInfo?.label}
                  </span>
                </div>
                
                <h3 className=\"font-semibold text-gray-900 mb-1\">
                  Room {room.roomNumber}
                </h3>
                {room.floor && (
                  <p className=\"text-sm text-gray-600 mb-2\">Floor {room.floor}</p>
                )}
                <div className=\"flex items-center text-sm text-gray-600\">
                  <Users className=\"w-4 h-4 mr-1\" />
                  <span>{room.capacity} guests</span>
                </div>
              </div>
            ) : (
              <div
                key={room.id}
                className={`
                  flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-all
                  ${isSelected 
                    ? 'border-orange-500 bg-orange-50' 
                    : 'border-gray-200 bg-white hover:border-gray-300'
                  }
                `}
                onClick={() => toggleRoomSelection(room.id)}
              >
                <div className=\"flex items-center space-x-3\">
                  <input
                    type=\"checkbox\"
                    checked={isSelected}
                    onChange={() => toggleRoomSelection(room.id)}
                    className=\"w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500\"
                  />
                  
                  <div>
                    <h3 className=\"font-medium text-gray-900\">
                      Room {room.roomNumber}
                    </h3>
                    <div className=\"flex items-center space-x-4 text-sm text-gray-600\">
                      {room.floor && <span>Floor {room.floor}</span>}
                      <span>{room.capacity} guests</span>
                    </div>
                  </div>
                </div>
                
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusInfo?.color}`}>
                  {statusInfo?.label}
                </span>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
  
  const renderOptionsTab = () => (
    <div className=\"space-y-6\">
      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">
        {/* QR Code Options */}
        <div className=\"space-y-4\">
          <h3 className=\"text-lg font-semibold text-gray-900\">QR Code Settings</h3>
          
          {/* Size */}
          <div>
            <label className=\"block text-sm font-medium text-gray-700 mb-2\">
              Size (pixels)
            </label>
            <input
              type=\"range\"
              min=\"128\"
              max=\"512\"
              step=\"32\"
              value={qrOptions.size}
              onChange={(e) => updateQROptions({ size: parseInt(e.target.value) })}
              className=\"w-full\"
            />
            <div className=\"flex justify-between text-xs text-gray-500 mt-1\">
              <span>128px</span>
              <span className=\"font-medium\">{qrOptions.size}px</span>
              <span>512px</span>
            </div>
          </div>
          
          {/* Colors */}
          <div className=\"grid grid-cols-2 gap-4\">
            <div>
              <label className=\"block text-sm font-medium text-gray-700 mb-2\">
                Foreground Color
              </label>
              <div className=\"flex items-center space-x-2\">
                <input
                  type=\"color\"
                  value={qrOptions.foregroundColor}
                  onChange={(e) => updateQROptions({ foregroundColor: e.target.value })}
                  className=\"w-12 h-10 border border-gray-300 rounded cursor-pointer\"
                />
                <input
                  type=\"text\"
                  value={qrOptions.foregroundColor}
                  onChange={(e) => updateQROptions({ foregroundColor: e.target.value })}
                  className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
                />
              </div>
            </div>
            
            <div>
              <label className=\"block text-sm font-medium text-gray-700 mb-2\">
                Background Color
              </label>
              <div className=\"flex items-center space-x-2\">
                <input
                  type=\"color\"
                  value={qrOptions.backgroundColor}
                  onChange={(e) => updateQROptions({ backgroundColor: e.target.value })}
                  className=\"w-12 h-10 border border-gray-300 rounded cursor-pointer\"
                />
                <input
                  type=\"text\"
                  value={qrOptions.backgroundColor}
                  onChange={(e) => updateQROptions({ backgroundColor: e.target.value })}
                  className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
                />
              </div>
            </div>
          </div>
          
          {/* Error correction */}
          <div>
            <label className=\"block text-sm font-medium text-gray-700 mb-2\">
              Error Correction Level
            </label>
            <select
              value={qrOptions.errorCorrectionLevel}
              onChange={(e) => updateQROptions({ errorCorrectionLevel: e.target.value as any })}
              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
            >
              <option value=\"L\">Low (7%)</option>
              <option value=\"M\">Medium (15%)</option>
              <option value=\"Q\">Quartile (25%)</option>
              <option value=\"H\">High (30%)</option>
            </select>
          </div>
          
          {/* Logo options */}
          <div>
            <label className=\"flex items-center space-x-2 mb-2\">
              <input
                type=\"checkbox\"
                checked={qrOptions.includelogo}
                onChange={(e) => updateQROptions({ includelogo: e.target.checked })}
                className=\"w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500\"
              />
              <span className=\"text-sm font-medium text-gray-700\">Include Logo</span>
            </label>
            
            {qrOptions.includelogo && (
              <div>
                <label className=\"block text-sm font-medium text-gray-700 mb-2\">
                  Logo Size (% of QR code)
                </label>
                <input
                  type=\"range\"
                  min=\"10\"
                  max=\"30\"
                  step=\"5\"
                  value={qrOptions.logoSize}
                  onChange={(e) => updateQROptions({ logoSize: parseInt(e.target.value) })}
                  className=\"w-full\"
                />
                <div className=\"flex justify-between text-xs text-gray-500 mt-1\">
                  <span>10%</span>
                  <span className=\"font-medium\">{qrOptions.logoSize}%</span>
                  <span>30%</span>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Export Options */}
        <div className=\"space-y-4\">
          <h3 className=\"text-lg font-semibold text-gray-900\">Export Settings</h3>
          
          {/* Format */}
          <div>
            <label className=\"block text-sm font-medium text-gray-700 mb-2\">
              Export Format
            </label>
            <div className=\"space-y-2\">
              <label className=\"flex items-center space-x-2\">
                <input
                  type=\"radio\"
                  value=\"pdf\"
                  checked={exportFormat === 'pdf'}
                  onChange={(e) => setExportFormat(e.target.value as 'pdf')}
                  className=\"w-4 h-4 text-orange-600 border-gray-300 focus:ring-orange-500\"
                />
                <span className=\"text-sm text-gray-700\">PDF Document (Recommended)</span>
              </label>
              <label className=\"flex items-center space-x-2\">
                <input
                  type=\"radio\"
                  value=\"zip\"
                  checked={exportFormat === 'zip'}
                  onChange={(e) => setExportFormat(e.target.value as 'zip')}
                  className=\"w-4 h-4 text-orange-600 border-gray-300 focus:ring-orange-500\"
                />
                <span className=\"text-sm text-gray-700\">ZIP Archive (Individual files)</span>
              </label>
            </div>
          </div>
          
          {exportFormat === 'pdf' && (
            <div className=\"bg-blue-50 rounded-lg p-4\">
              <h4 className=\"font-medium text-blue-900 mb-2\">PDF Layout Options</h4>
              <button
                onClick={() => setActiveTab('layout')}
                className=\"text-sm text-blue-700 hover:text-blue-800 transition-colors\"
              >
                Configure PDF layout →
              </button>
            </div>
          )}
          
          {/* Preview */}
          <div className=\"bg-gray-50 rounded-lg p-4\">
            <h4 className=\"font-medium text-gray-900 mb-2\">Generation Summary</h4>
            <div className=\"space-y-1 text-sm text-gray-600\">
              <div>Rooms to generate: <span className=\"font-medium\">{selectedRooms.size}</span></div>
              <div>QR code size: <span className=\"font-medium\">{qrOptions.size}x{qrOptions.size}px</span></div>
              <div>Export format: <span className=\"font-medium\">{exportFormat.toUpperCase()}</span></div>
              <div>Estimated time: <span className=\"font-medium\">~{Math.ceil(selectedRooms.size * 0.5)}s</span></div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Action buttons */}
      <div className=\"flex justify-between pt-6 border-t border-gray-200\">
        <button
          onClick={() => setActiveTab('selection')}
          className=\"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\"
        >
          ← Back to Selection
        </button>
        
        <div className=\"flex space-x-3\">
          {exportFormat === 'pdf' && (
            <button
              onClick={() => setActiveTab('layout')}
              className=\"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\"
            >
              Configure Layout
            </button>
          )}
          
          <button
            onClick={startGeneration}
            disabled={selectedRooms.size === 0}
            className=\"flex items-center space-x-2 px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50\"
          >
            <Zap className=\"w-4 h-4\" />
            <span>Start Generation</span>
          </button>
        </div>
      </div>
    </div>
  );
  
  const renderLayoutTab = () => (
    <div className=\"space-y-6\">
      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">
        {/* Page settings */}
        <div className=\"space-y-4\">
          <h3 className=\"text-lg font-semibold text-gray-900\">Page Settings</h3>
          
          {/* Page size */}
          <div>
            <label className=\"block text-sm font-medium text-gray-700 mb-2\">
              Page Size
            </label>
            <select
              value={layoutOptions.pageSize}
              onChange={(e) => updateLayoutOptions({ pageSize: e.target.value as any })}
              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
            >
              <option value=\"A4\">A4 (210 × 297 mm)</option>
              <option value=\"A3\">A3 (297 × 420 mm)</option>
              <option value=\"Letter\">Letter (216 × 279 mm)</option>
              <option value=\"Legal\">Legal (216 × 356 mm)</option>
            </select>
          </div>
          
          {/* Orientation */}
          <div>
            <label className=\"block text-sm font-medium text-gray-700 mb-2\">
              Orientation
            </label>
            <div className=\"flex space-x-4\">
              <label className=\"flex items-center space-x-2\">
                <input
                  type=\"radio\"
                  value=\"portrait\"
                  checked={layoutOptions.orientation === 'portrait'}
                  onChange={(e) => updateLayoutOptions({ orientation: e.target.value as any })}
                  className=\"w-4 h-4 text-orange-600 border-gray-300 focus:ring-orange-500\"
                />
                <span className=\"text-sm text-gray-700\">Portrait</span>
              </label>
              <label className=\"flex items-center space-x-2\">
                <input
                  type=\"radio\"
                  value=\"landscape\"
                  checked={layoutOptions.orientation === 'landscape'}
                  onChange={(e) => updateLayoutOptions({ orientation: e.target.value as any })}
                  className=\"w-4 h-4 text-orange-600 border-gray-300 focus:ring-orange-500\"
                />
                <span className=\"text-sm text-gray-700\">Landscape</span>
              </label>
            </div>
          </div>
          
          {/* Grid layout */}
          <div className=\"grid grid-cols-2 gap-4\">
            <div>
              <label className=\"block text-sm font-medium text-gray-700 mb-2\">
                Columns
              </label>
              <input
                type=\"number\"
                min=\"1\"
                max=\"6\"
                value={layoutOptions.columns}
                onChange={(e) => updateLayoutOptions({ columns: parseInt(e.target.value) })}
                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
              />
            </div>
            
            <div>
              <label className=\"block text-sm font-medium text-gray-700 mb-2\">
                Rows
              </label>
              <input
                type=\"number\"
                min=\"1\"
                max=\"8\"
                value={layoutOptions.rows}
                onChange={(e) => updateLayoutOptions({ rows: parseInt(e.target.value) })}
                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
              />
            </div>
          </div>
          
          {/* Margins and spacing */}
          <div className=\"grid grid-cols-2 gap-4\">
            <div>
              <label className=\"block text-sm font-medium text-gray-700 mb-2\">
                Page Margin (mm)
              </label>
              <input
                type=\"number\"
                min=\"5\"
                max=\"50\"
                value={layoutOptions.margin}
                onChange={(e) => updateLayoutOptions({ margin: parseInt(e.target.value) })}
                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
              />
            </div>
            
            <div>
              <label className=\"block text-sm font-medium text-gray-700 mb-2\">
                Item Spacing (mm)
              </label>
              <input
                type=\"number\"
                min=\"0\"
                max=\"20\"
                value={layoutOptions.spacing}
                onChange={(e) => updateLayoutOptions({ spacing: parseInt(e.target.value) })}
                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
              />
            </div>
          </div>
        </div>
        
        {/* Content options */}
        <div className=\"space-y-4\">
          <h3 className=\"text-lg font-semibold text-gray-900\">Content Options</h3>
          
          {/* Include options */}
          <div className=\"space-y-2\">
            <label className=\"flex items-center space-x-2\">
              <input
                type=\"checkbox\"
                checked={layoutOptions.includeRoomNumber}
                onChange={(e) => updateLayoutOptions({ includeRoomNumber: e.target.checked })}
                className=\"w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500\"
              />
              <span className=\"text-sm text-gray-700\">Include room number</span>
            </label>
            
            <label className=\"flex items-center space-x-2\">
              <input
                type=\"checkbox\"
                checked={layoutOptions.includeFloor}
                onChange={(e) => updateLayoutOptions({ includeFloor: e.target.checked })}
                className=\"w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500\"
              />
              <span className=\"text-sm text-gray-700\">Include floor information</span>
            </label>
            
            <label className=\"flex items-center space-x-2\">
              <input
                type=\"checkbox\"
                checked={layoutOptions.includeQRCodeText}
                onChange={(e) => updateLayoutOptions({ includeQRCodeText: e.target.checked })}
                className=\"w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500\"
              />
              <span className=\"text-sm text-gray-700\">Include QR code text</span>
            </label>
            
            <label className=\"flex items-center space-x-2\">
              <input
                type=\"checkbox\"
                checked={layoutOptions.includeInstructions}
                onChange={(e) => updateLayoutOptions({ includeInstructions: e.target.checked })}
                className=\"w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500\"
              />
              <span className=\"text-sm text-gray-700\">Include scanning instructions</span>
            </label>
          </div>
          
          {/* Headers */}
          <div>
            <label className=\"block text-sm font-medium text-gray-700 mb-2\">
              Document Title (Optional)
            </label>
            <input
              type=\"text\"
              value={layoutOptions.title || ''}
              onChange={(e) => updateLayoutOptions({ title: e.target.value || undefined })}
              placeholder=\"e.g., Room QR Codes - TapDine Hotel\"
              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
            />
          </div>
          
          {/* Typography */}
          <div className=\"grid grid-cols-2 gap-4\">
            <div>
              <label className=\"block text-sm font-medium text-gray-700 mb-2\">
                Font Size
              </label>
              <input
                type=\"number\"
                min=\"8\"
                max=\"16\"
                value={layoutOptions.fontSize}
                onChange={(e) => updateLayoutOptions({ fontSize: parseInt(e.target.value) })}
                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
              />
            </div>
            
            <div>
              <label className=\"block text-sm font-medium text-gray-700 mb-2\">
                Title Font Size
              </label>
              <input
                type=\"number\"
                min=\"12\"
                max=\"24\"
                value={layoutOptions.titleFontSize}
                onChange={(e) => updateLayoutOptions({ titleFontSize: parseInt(e.target.value) })}
                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"
              />
            </div>
          </div>
          
          {/* Preview calculation */}
          <div className=\"bg-gray-50 rounded-lg p-4\">
            <h4 className=\"font-medium text-gray-900 mb-2\">Layout Preview</h4>
            <div className=\"space-y-1 text-sm text-gray-600\">
              <div>QR codes per page: <span className=\"font-medium\">{layoutOptions.columns * layoutOptions.rows}</span></div>
              <div>Estimated pages: <span className=\"font-medium\">{Math.ceil(selectedRooms.size / (layoutOptions.columns * layoutOptions.rows))}</span></div>
              <div>Page size: <span className=\"font-medium\">{layoutOptions.pageSize} {layoutOptions.orientation}</span></div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Action buttons */}
      <div className=\"flex justify-between pt-6 border-t border-gray-200\">
        <button
          onClick={() => setActiveTab('options')}
          className=\"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\"
        >
          ← Back to Options
        </button>
        
        <button
          onClick={startGeneration}
          disabled={selectedRooms.size === 0}
          className=\"flex items-center space-x-2 px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50\"
        >
          <Zap className=\"w-4 h-4\" />
          <span>Start Generation</span>
        </button>
      </div>
    </div>
  );
  
  const renderProgressTab = () => (
    <div className=\"space-y-6\">
      {/* Progress header */}
      <div className=\"text-center\">
        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">
          {progress.isActive ? 'Generating QR Codes...' : 
           generationResults ? 'Generation Complete!' : 'Ready to Generate'}
        </h2>
        <p className=\"text-gray-600\">
          {progress.isActive 
            ? `Processing ${progress.current} of ${progress.total} QR codes`
            : generationResults
              ? `Generated ${generationResults.successCount} of ${generationResults.totalProcessed} QR codes`
              : `Ready to generate ${selectedRooms.size} QR codes`
          }
        </p>
      </div>
      
      {/* Progress bar */}
      <div className=\"w-full bg-gray-200 rounded-full h-4\">
        <div 
          className={`h-4 rounded-full transition-all duration-300 ${
            progress.isActive ? 'bg-orange-600' : 
            generationResults?.success ? 'bg-green-600' : 'bg-gray-400'
          }`}
          style={{ 
            width: `${progress.total > 0 ? (progress.current / progress.total) * 100 : 0}%` 
          }}
        />
      </div>
      
      {/* Time estimates */}
      {progress.isActive && progress.estimatedTimeRemaining && (
        <div className=\"text-center text-sm text-gray-600\">
          <Clock className=\"w-4 h-4 inline mr-1\" />
          Estimated time remaining: {Math.ceil(progress.estimatedTimeRemaining / 1000)}s
        </div>
      )}
      
      {/* Generation controls */}
      {progress.isActive && (
        <div className=\"flex justify-center space-x-3\">
          {progress.isPaused ? (
            <button
              onClick={resumeGeneration}
              className=\"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"
            >
              <Play className=\"w-4 h-4\" />
              <span>Resume</span>
            </button>
          ) : (
            <button
              onClick={pauseGeneration}
              className=\"flex items-center space-x-2 px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors\"
            >
              <Pause className=\"w-4 h-4\" />
              <span>Pause</span>
            </button>
          )}
          
          <button
            onClick={resetGeneration}
            className=\"flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"
          >
            <RotateCcw className=\"w-4 h-4\" />
            <span>Cancel</span>
          </button>
        </div>
      )}
      
      {/* Results summary */}
      {generationResults && (
        <div className=\"bg-gray-50 rounded-lg p-6\">
          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Generation Results</h3>
          
          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">
            <div className=\"text-center p-4 bg-green-100 rounded-lg\">
              <CheckCircle className=\"w-8 h-8 text-green-600 mx-auto mb-2\" />
              <div className=\"text-2xl font-bold text-green-700\">{generationResults.successCount}</div>
              <div className=\"text-sm text-green-600\">Successful</div>
            </div>
            
            <div className=\"text-center p-4 bg-red-100 rounded-lg\">
              <AlertCircle className=\"w-8 h-8 text-red-600 mx-auto mb-2\" />
              <div className=\"text-2xl font-bold text-red-700\">{generationResults.failureCount}</div>
              <div className=\"text-sm text-red-600\">Failed</div>
            </div>
            
            <div className=\"text-center p-4 bg-blue-100 rounded-lg\">
              <Clock className=\"w-8 h-8 text-blue-600 mx-auto mb-2\" />
              <div className=\"text-2xl font-bold text-blue-700\">
                {(generationResults.processingTime / 1000).toFixed(1)}s
              </div>
              <div className=\"text-sm text-blue-600\">Processing Time</div>
            </div>
          </div>
          
          {/* Failed items */}
          {generationResults.failed.length > 0 && (
            <div className=\"mb-6\">
              <h4 className=\"font-medium text-red-700 mb-2\">Failed Generations:</h4>
              <div className=\"space-y-1 text-sm\">
                {generationResults.failed.map((failure, index) => (
                  <div key={index} className=\"text-red-600\">
                    Room {failure.roomId}: {failure.error}
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {/* Action buttons */}
          <div className=\"flex justify-center space-x-3\">
            <button
              onClick={() => {
                // Trigger export/download
                console.log('Export results:', generationResults);
              }}
              className=\"flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"
            >
              <Download className=\"w-4 h-4\" />
              <span>Download {exportFormat.toUpperCase()}</span>
            </button>
            
            <button
              onClick={resetGeneration}
              className=\"flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"
            >
              <RotateCcw className=\"w-4 h-4\" />
              <span>Generate More</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
  
  if (!isOpen) return null;
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  return (
    <div className=\"fixed inset-0 z-50 overflow-y-auto\" aria-labelledby=\"modal-title\" role=\"dialog\" aria-modal=\"true\">
      <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">
        {/* Background overlay */}
        <div 
          className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" 
          aria-hidden=\"true\"
          onClick={onClose}
        />
        
        {/* Modal panel */}
        <div className=\"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full\">
          {/* Header */}
          <div className=\"bg-white px-6 py-4 border-b border-gray-200\">
            <div className=\"flex items-center justify-between\">
              <div>
                <h1 className=\"text-xl font-semibold text-gray-900 flex items-center space-x-2\">
                  <Zap className=\"w-6 h-6 text-orange-600\" />
                  <span>Bulk QR Code Generator</span>
                </h1>
                <p className=\"text-sm text-gray-600 mt-1\">
                  Generate multiple QR codes with customizable options and layouts
                </p>
              </div>
              
              <button
                onClick={onClose}
                disabled={progress.isActive}
                className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50\"
              >
                <X className=\"w-5 h-5\" />
              </button>
            </div>
            
            {/* Progress indicator tabs */}
            <div className=\"flex items-center mt-4 space-x-1\">
              {['selection', 'options', 'layout', 'progress'].map((tab, index) => (
                <div key={tab} className=\"flex items-center\">
                  <div
                    className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-colors ${
                      activeTab === tab
                        ? 'bg-orange-600 text-white'
                        : index < ['selection', 'options', 'layout', 'progress'].indexOf(activeTab)
                          ? 'bg-green-600 text-white'
                          : 'bg-gray-200 text-gray-600'
                    }`}
                  >
                    {index + 1}
                  </div>
                  <span className={`ml-2 text-sm capitalize ${
                    activeTab === tab ? 'text-orange-600 font-medium' : 'text-gray-600'
                  }`}>
                    {tab}
                  </span>
                  {index < 3 && (
                    <div className={`w-8 h-0.5 mx-2 ${
                      index < ['selection', 'options', 'layout', 'progress'].indexOf(activeTab)
                        ? 'bg-green-600'
                        : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>
          
          {/* Content */}
          <div className=\"bg-white px-6 py-6 min-h-[500px]\">
            {activeTab === 'selection' && renderSelectionTab()}
            {activeTab === 'options' && renderOptionsTab()}
            {activeTab === 'layout' && renderLayoutTab()}
            {activeTab === 'progress' && renderProgressTab()}
          </div>
        </div>
      </div>
    </div>
  );
}
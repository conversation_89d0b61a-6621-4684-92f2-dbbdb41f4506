// Main QR Management Dashboard - Comprehensive room and QR code management interface
// Provides intuitive controls for generating, previewing, and downloading QR codes
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  QrCode,
  Download,
  Upload,
  Search,
  Filter,
  Grid,
  List,
  Eye,
  Settings,
  Plus,
  Trash2,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Users,
  Building,
  Printer,
  FileText,
  Image as ImageIcon,
  Zap,
  Palette,
} from 'lucide-react';

import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useCrud } from '@/hooks/useCrud';
import type {
  Room,
  QRCodeData,
  QRManagementState,
  QRManagementActions,
  RoomFilters,
  RoomSortOptions,
  QRCodeOptions,
  BulkQROperation,
  QRNotification,
  DEFAULT_QR_OPTIONS,
  ROOM_STATUSES,
} from '@/types/qr-management';
import { QRCodeGenerationService } from '@/services/qr-generation';

interface QRManagementDashboardProps {
  tenantId: string;
  className?: string;
}

export function QRManagementDashboard({ 
  tenantId, 
  className = '' 
}: QRManagementDashboardProps) {
  
  // =====================================================
  // STATE MANAGEMENT
  // =====================================================
  
  const [state, setState] = useState<QRManagementState>({
    rooms: [],
    qrCodes: new Map(),
    selectedRooms: new Set(),
    
    isLoading: false,
    isGenerating: false,
    isExporting: false,
    error: null,
    
    filters: {},
    sortOptions: { field: 'roomNumber', direction: 'asc' },
    viewMode: 'grid',
    
    bulkOperation: null,
    
    defaultQROptions: DEFAULT_QR_OPTIONS,
    customizationOptions: {
      size: 256,
      foregroundColor: '#000000',
      backgroundColor: '#FFFFFF',
      errorCorrectionLevel: 'M',
      includelogo: true,
      logoSize: 20,
      margin: 4,
      border: false,
      borderWidth: 1,
      borderColor: '#000000',
    },
    
    exportProgress: {
      current: 0,
      total: 0,
      isActive: false,
    },
    
    notifications: [],
  });
  
  // Initialize CRUD operations for rooms
  const [roomsCrudState, roomsCrudActions] = useCrud<Room>({
    apiEndpoint: `/api/tenants/${tenantId}/rooms`,
    optimisticUpdates: true,
    onSuccess: (action, room) => {
      addNotification({
        type: 'success',
        title: 'Success',
        message: `Room ${action === 'create' ? 'created' : action === 'update' ? 'updated' : 'deleted'} successfully`,
        autoClose: true,
        duration: 3000,
      });
    },
    onError: (action, error) => {
      addNotification({
        type: 'error',
        title: 'Error',
        message: `Failed to ${action} room: ${error.message}`,
        autoClose: true,
        duration: 5000,
      });
    },
  });
  
  // Initialize QR generation service
  const qrService = new QRCodeGenerationService();
  
  // =====================================================
  // UTILITY FUNCTIONS
  // =====================================================
  
  const updateState = useCallback((updates: Partial<QRManagementState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);
  
  const addNotification = useCallback((notification: Omit<QRNotification, 'id' | 'timestamp'>) => {
    const newNotification: QRNotification = {
      ...notification,
      id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
    };
    
    updateState({
      notifications: [...state.notifications, newNotification],
    });
    
    // Auto-remove notification if specified
    if (notification.autoClose && notification.duration) {
      setTimeout(() => {
        removeNotification(newNotification.id);
      }, notification.duration);
    }
  }, [state.notifications, updateState]);
  
  const removeNotification = useCallback((notificationId: string) => {
    updateState({
      notifications: state.notifications.filter(n => n.id !== notificationId),
    });
  }, [state.notifications, updateState]);
  
  // =====================================================
  // DATA OPERATIONS
  // =====================================================
  
  const loadRooms = useCallback(async (filters?: RoomFilters) => {
    updateState({ isLoading: true, error: null });
    
    try {
      const rooms = await roomsCrudActions.fetchItems(filters);
      updateState({ 
        rooms,
        isLoading: false,
      });
    } catch (error) {
      updateState({ 
        error: error instanceof Error ? error.message : 'Failed to load rooms',
        isLoading: false,
      });
    }
  }, [roomsCrudActions, updateState]);
  
  const generateQRCode = useCallback(async (
    roomId: string, 
    options?: QRCodeOptions
  ) => {
    const room = state.rooms.find(r => r.id === roomId);
    if (!room) {
      throw new Error('Room not found');
    }
    
    updateState({ isGenerating: true });
    
    try {
      const qrOptions = { ...state.defaultQROptions, ...options };
      const result = await qrService.generateQRCode(room, qrOptions);
      
      if (result.success) {
        // Update QR codes map
        const newQRCodes = new Map(state.qrCodes);
        newQRCodes.set(roomId, result.qrCode);
        
        updateState({ 
          qrCodes: newQRCodes,
          isGenerating: false,
        });
        
        addNotification({
          type: 'success',
          title: 'QR Code Generated',
          message: `QR code for room ${room.roomNumber} generated successfully`,
          autoClose: true,
          duration: 3000,
        });
      } else {
        throw new Error(result.error || 'QR generation failed');
      }
      
      return result;
    } catch (error) {
      updateState({ isGenerating: false });
      addNotification({
        type: 'error',
        title: 'Generation Failed',
        message: error instanceof Error ? error.message : 'QR code generation failed',
        autoClose: true,
        duration: 5000,
      });
      throw error;
    }
  }, [state.rooms, state.defaultQROptions, state.qrCodes, qrService, updateState, addNotification]);
  
  const bulkGenerateQRCodes = useCallback(async (
    roomIds: string[], 
    options?: QRCodeOptions
  ) => {
    const rooms = state.rooms.filter(r => roomIds.includes(r.id));
    if (rooms.length === 0) {
      throw new Error('No valid rooms selected');
    }
    
    updateState({ 
      isGenerating: true,
      exportProgress: { current: 0, total: rooms.length, isActive: true },
    });
    
    try {
      const qrOptions = { ...state.defaultQROptions, ...options };
      
      const result = await qrService.bulkGenerateQRCodes(
        rooms,
        qrOptions,
        (current, total) => {
          updateState({
            exportProgress: { current, total, isActive: true },
          });
        }
      );
      
      // Update QR codes map with successful generations
      const newQRCodes = new Map(state.qrCodes);
      result.results.forEach((qrResult, index) => {
        if (qrResult.success) {
          newQRCodes.set(rooms[index].id, qrResult.qrCode);
        }
      });
      
      updateState({ 
        qrCodes: newQRCodes,
        isGenerating: false,
        exportProgress: { current: 0, total: 0, isActive: false },
      });
      
      addNotification({
        type: result.success ? 'success' : 'warning',
        title: 'Bulk Generation Complete',
        message: `Generated ${result.successCount}/${result.totalProcessed} QR codes successfully`,
        autoClose: true,
        duration: 5000,
      });
      
      return result;
    } catch (error) {
      updateState({ 
        isGenerating: false,
        exportProgress: { current: 0, total: 0, isActive: false },
      });
      addNotification({
        type: 'error',
        title: 'Bulk Generation Failed',
        message: error instanceof Error ? error.message : 'Bulk QR generation failed',
        autoClose: true,
        duration: 5000,
      });
      throw error;
    }
  }, [state.rooms, state.defaultQROptions, state.qrCodes, qrService, updateState, addNotification]);
  
  const exportQRCode = useCallback(async (roomId: string, format: 'png' | 'svg' | 'pdf') => {
    const qrCode = state.qrCodes.get(roomId);
    if (!qrCode) {
      throw new Error('QR code not found. Please generate it first.');
    }
    
    updateState({ isExporting: true });
    
    try {
      const exportOptions = {
        format,
        quality: 95,
        size: state.customizationOptions.size,
      };
      
      const result = await qrService.exportQRCode(qrCode, exportOptions);
      
      if (result.success && result.downloadUrl) {
        // Trigger download
        const link = document.createElement('a');
        link.href = result.downloadUrl;
        link.download = result.filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up URL
        URL.revokeObjectURL(result.downloadUrl);
        
        addNotification({
          type: 'success',
          title: 'Export Complete',
          message: `QR code exported as ${format.toUpperCase()}`,
          autoClose: true,
          duration: 3000,
        });
      } else {
        throw new Error(result.error || 'Export failed');
      }
      
      updateState({ isExporting: false });
      return result;
    } catch (error) {
      updateState({ isExporting: false });
      addNotification({
        type: 'error',
        title: 'Export Failed',
        message: error instanceof Error ? error.message : 'QR code export failed',
        autoClose: true,
        duration: 5000,
      });
      throw error;
    }
  }, [state.qrCodes, state.customizationOptions, qrService, updateState, addNotification]);
  
  // =====================================================
  // SELECTION MANAGEMENT
  // =====================================================
  
  const toggleRoomSelection = useCallback((roomId: string) => {
    const newSelection = new Set(state.selectedRooms);
    if (newSelection.has(roomId)) {
      newSelection.delete(roomId);
    } else {
      newSelection.add(roomId);
    }
    updateState({ selectedRooms: newSelection });
  }, [state.selectedRooms, updateState]);
  
  const selectAllRooms = useCallback(() => {
    const allRoomIds = state.rooms.map(room => room.id);
    updateState({ selectedRooms: new Set(allRoomIds) });
  }, [state.rooms, updateState]);
  
  const clearSelection = useCallback(() => {
    updateState({ selectedRooms: new Set() });
  }, [updateState]);
  
  // =====================================================
  // FILTERS AND SORTING
  // =====================================================
  
  const applyFilters = useCallback((newFilters: Partial<RoomFilters>) => {
    updateState({ 
      filters: { ...state.filters, ...newFilters },
    });
    loadRooms({ ...state.filters, ...newFilters });
  }, [state.filters, updateState, loadRooms]);
  
  const applySorting = useCallback((sortOptions: RoomSortOptions) => {
    updateState({ sortOptions });
    // Sort rooms locally or reload with new sort
    const sortedRooms = [...state.rooms].sort((a, b) => {
      const aValue = a[sortOptions.field];
      const bValue = b[sortOptions.field];
      
      if (sortOptions.direction === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
    
    updateState({ rooms: sortedRooms });
  }, [state.rooms, updateState]);
  
  // =====================================================
  // EFFECTS
  // =====================================================
  
  useEffect(() => {
    loadRooms();
  }, [loadRooms]);
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderRoom = (room: Room) => {
    const isSelected = state.selectedRooms.has(room.id);
    const hasQRCode = state.qrCodes.has(room.id);
    const statusInfo = ROOM_STATUSES.find(s => s.value === room.status);
    
    return (
      <div
        key={room.id}
        className={`
          relative p-4 rounded-lg border-2 transition-all cursor-pointer hover:shadow-md
          ${isSelected 
            ? 'border-orange-500 bg-orange-50' 
            : 'border-gray-200 bg-white hover:border-gray-300'
          }
        `}
        onClick={() => toggleRoomSelection(room.id)}
      >
        {/* Selection indicator */}
        <div className=\"absolute top-2 left-2\">
          <input
            type=\"checkbox\"
            checked={isSelected}
            onChange={() => toggleRoomSelection(room.id)}
            className=\"w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500\"
          />
        </div>
        
        {/* QR Code status */}
        <div className=\"absolute top-2 right-2\">
          {hasQRCode ? (
            <CheckCircle className=\"w-5 h-5 text-green-500\" title=\"QR Code Generated\" />
          ) : (
            <Clock className=\"w-5 h-5 text-gray-400\" title=\"QR Code Pending\" />
          )}
        </div>
        
        <div className=\"mt-6 space-y-3\">
          {/* Room info */}
          <div className=\"text-center\">
            <h3 className=\"text-lg font-semibold text-gray-900\">
              Room {room.roomNumber}
            </h3>
            {room.floor && (
              <p className=\"text-sm text-gray-600\">Floor {room.floor}</p>
            )}
          </div>
          
          {/* Status badge */}
          <div className=\"flex justify-center\">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusInfo?.color || 'bg-gray-100 text-gray-800'}`}>
              {statusInfo?.label || room.status}
            </span>
          </div>
          
          {/* Capacity */}
          <div className=\"flex items-center justify-center space-x-1 text-sm text-gray-600\">
            <Users className=\"w-4 h-4\" />
            <span>{room.capacity} guests</span>
          </div>
          
          {/* QR Code preview */}
          {hasQRCode && (
            <div className=\"flex justify-center\">
              <div className=\"w-16 h-16 bg-gray-100 rounded border flex items-center justify-center\">
                <QrCode className=\"w-8 h-8 text-gray-600\" />
              </div>
            </div>
          )}
          
          {/* Action buttons */}
          <div className=\"flex justify-center space-x-2\">
            {hasQRCode ? (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    exportQRCode(room.id, 'png');
                  }}
                  className=\"p-2 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded transition-colors\"
                  title=\"Download PNG\"
                >
                  <Download className=\"w-4 h-4\" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    generateQRCode(room.id);
                  }}
                  className=\"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors\"
                  title=\"Regenerate QR\"
                >
                  <RefreshCw className=\"w-4 h-4\" />
                </button>
              </>
            ) : (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  generateQRCode(room.id);
                }}
                className=\"p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded transition-colors\"
                title=\"Generate QR Code\"
              >
                <QrCode className=\"w-4 h-4\" />
              </button>
            )}
          </div>
        </div>
      </div>
    );
  };
  
  const renderNotifications = () => {
    if (state.notifications.length === 0) return null;
    
    return (
      <div className=\"fixed top-4 right-4 z-50 space-y-2\">
        {state.notifications.map((notification) => (
          <div
            key={notification.id}
            className={`
              p-4 rounded-lg shadow-lg max-w-sm transition-all transform
              ${notification.type === 'success' ? 'bg-green-100 border border-green-200' :
                notification.type === 'error' ? 'bg-red-100 border border-red-200' :
                notification.type === 'warning' ? 'bg-yellow-100 border border-yellow-200' :
                'bg-blue-100 border border-blue-200'
              }
            `}
          >
            <div className=\"flex items-start justify-between\">
              <div className=\"flex items-start space-x-2\">
                {notification.type === 'success' && <CheckCircle className=\"w-5 h-5 text-green-600 mt-0.5\" />}
                {notification.type === 'error' && <AlertCircle className=\"w-5 h-5 text-red-600 mt-0.5\" />}
                {notification.type === 'warning' && <AlertCircle className=\"w-5 h-5 text-yellow-600 mt-0.5\" />}
                {notification.type === 'info' && <AlertCircle className=\"w-5 h-5 text-blue-600 mt-0.5\" />}
                
                <div>
                  <h4 className=\"font-medium text-gray-900\">{notification.title}</h4>
                  <p className=\"text-sm text-gray-700\">{notification.message}</p>
                </div>
              </div>
              
              <button
                onClick={() => removeNotification(notification.id)}
                className=\"text-gray-400 hover:text-gray-600 transition-colors\"
              >
                <X className=\"w-4 h-4\" />
              </button>
            </div>
            
            {notification.action && (
              <div className=\"mt-3\">
                <button
                  onClick={notification.action.onClick}
                  className=\"text-sm font-medium text-orange-600 hover:text-orange-700 transition-colors\"
                >
                  {notification.action.label}
                </button>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      {/* Header */}
      <div className=\"bg-white shadow-sm border-b border-gray-200\">
        <div className=\"px-6 py-4\">
          <div className=\"flex items-center justify-between\">
            <div>
              <h1 className=\"text-2xl font-bold text-gray-900 flex items-center space-x-2\">
                <QrCode className=\"w-8 h-8 text-orange-600\" />
                <span>QR Code Management</span>
              </h1>
              <p className=\"text-gray-600 mt-1\">
                Generate, preview, and download QR codes for room access
              </p>
            </div>
            
            <div className=\"flex items-center space-x-3\">
              {/* Bulk actions */}
              {state.selectedRooms.size > 0 && (
                <div className=\"flex items-center space-x-2 px-3 py-2 bg-orange-50 rounded-lg border border-orange-200\">
                  <span className=\"text-sm font-medium text-orange-700\">
                    {state.selectedRooms.size} selected
                  </span>
                  
                  <button
                    onClick={() => bulkGenerateQRCodes(Array.from(state.selectedRooms))}
                    disabled={state.isGenerating}
                    className=\"px-3 py-1 bg-orange-600 text-white rounded text-sm hover:bg-orange-700 transition-colors disabled:opacity-50\"
                  >
                    {state.isGenerating ? <LoadingSpinner size=\"sm\" /> : <Zap className=\"w-4 h-4\" />}
                    Generate QRs
                  </button>
                  
                  <button
                    onClick={clearSelection}
                    className=\"px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors\"
                  >
                    Clear
                  </button>
                </div>
              )}
              
              {/* View mode toggle */}
              <div className=\"flex items-center space-x-1 bg-gray-100 rounded-lg p-1\">
                <button
                  onClick={() => updateState({ viewMode: 'grid' })}
                  className={`p-2 rounded transition-colors ${
                    state.viewMode === 'grid' 
                      ? 'bg-white text-orange-600 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Grid className=\"w-4 h-4\" />
                </button>
                <button
                  onClick={() => updateState({ viewMode: 'list' })}
                  className={`p-2 rounded transition-colors ${
                    state.viewMode === 'list' 
                      ? 'bg-white text-orange-600 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <List className=\"w-4 h-4\" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Filters and search */}
      <div className=\"px-6 py-4 bg-white border-b border-gray-200\">
        <div className=\"flex items-center justify-between\">
          <div className=\"flex items-center space-x-4\">
            {/* Search */}
            <div className=\"relative\">
              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />
              <input
                type=\"text\"
                placeholder=\"Search rooms...\"
                className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500\"
                onChange={(e) => applyFilters({ search: e.target.value })}
              />
            </div>
            
            {/* Filter buttons */}
            <button className=\"flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\">
              <Filter className=\"w-4 h-4\" />
              <span>Filters</span>
            </button>
            
            <button className=\"flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\">
              <Settings className=\"w-4 h-4\" />
              <span>QR Options</span>
            </button>
          </div>
          
          <div className=\"flex items-center space-x-2\">
            <button
              onClick={selectAllRooms}
              className=\"px-3 py-2 text-sm text-gray-600 hover:text-gray-900 transition-colors\"
            >
              Select All
            </button>
            
            <button
              onClick={() => loadRooms()}
              disabled={state.isLoading}
              className=\"flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50\"
            >
              {state.isLoading ? <LoadingSpinner size=\"sm\" /> : <RefreshCw className=\"w-4 h-4\" />}
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>
      
      {/* Progress indicator */}
      {state.exportProgress.isActive && (
        <div className=\"px-6 py-3 bg-blue-50 border-b border-blue-200\">
          <div className=\"flex items-center justify-between\">
            <div className=\"flex items-center space-x-3\">
              <LoadingSpinner size=\"sm\" />
              <span className=\"text-sm font-medium text-blue-700\">
                Processing QR codes... {state.exportProgress.current}/{state.exportProgress.total}
              </span>
            </div>
            
            <div className=\"flex-1 mx-4\">
              <div className=\"w-full bg-blue-200 rounded-full h-2\">
                <div 
                  className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"
                  style={{ 
                    width: `${(state.exportProgress.current / state.exportProgress.total) * 100}%` 
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Main content */}
      <div className=\"px-6 py-6\">
        {state.isLoading ? (
          <div className=\"flex items-center justify-center py-12\">
            <LoadingSpinner size=\"lg\" />
            <span className=\"ml-3 text-gray-600\">Loading rooms...</span>
          </div>
        ) : state.error ? (
          <div className=\"text-center py-12\">
            <AlertCircle className=\"w-12 h-12 text-red-500 mx-auto mb-4\" />
            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Error Loading Rooms</h3>
            <p className=\"text-gray-600 mb-4\">{state.error}</p>
            <button
              onClick={() => loadRooms()}
              className=\"px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors\"
            >
              Try Again
            </button>
          </div>
        ) : state.rooms.length === 0 ? (
          <div className=\"text-center py-12\">
            <Building className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />
            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Rooms Found</h3>
            <p className=\"text-gray-600 mb-4\">
              Get started by adding rooms to your tenant account.
            </p>
            <button className=\"px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors\">
              Add Room
            </button>
          </div>
        ) : (
          <div className={`
            ${state.viewMode === 'grid' 
              ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' 
              : 'space-y-4'
            }
          `}>
            {state.rooms.map(renderRoom)}
          </div>
        )}
      </div>
      
      {/* Notifications */}
      {renderNotifications()}
    </div>
  );
}
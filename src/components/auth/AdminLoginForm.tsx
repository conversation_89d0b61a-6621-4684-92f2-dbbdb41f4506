// Admin login form component for tenant admin dashboard
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { 
  Mail, 
  Lock, 
  Eye, 
  EyeOff, 
  Building, 
  AlertCircle,
  CheckCircle,
  LogIn,
  Shield,
  ArrowRight
} from 'lucide-react';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import type { LoginFormData, LoginFormErrors } from '@/types/auth';

interface AdminLoginFormProps {
  className?: string;
  onSuccess?: () => void;
  redirectTo?: string;
}

export function AdminLoginForm({ 
  className = '', 
  onSuccess, 
  redirectTo 
}: AdminLoginFormProps) {
  
  // =====================================================
  // HOOKS & STATE
  // =====================================================
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login, authState } = useAdminAuth();
  
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
    tenantSlug: '',
    rememberMe: false,
  });
  
  const [formErrors, setFormErrors] = useState<LoginFormErrors>({});
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);

  // =====================================================
  // EFFECTS
  // =====================================================
  
  // Pre-fill tenant slug from URL params
  useEffect(() => {
    const tenant = searchParams.get('tenant');
    if (tenant) {
      setFormData(prev => ({ ...prev, tenantSlug: tenant }));
    }
  }, [searchParams]);

  // Redirect if already authenticated
  useEffect(() => {
    if (authState.isAuthenticated && !authState.loading) {
      const destination = redirectTo || '/admin/dashboard';
      router.push(destination);
    }
  }, [authState.isAuthenticated, authState.loading, router, redirectTo]);

  // =====================================================
  // VALIDATION
  // =====================================================
  
  const validateForm = (): boolean => {
    const errors: LoginFormErrors = {};
    
    // Email validation
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }
    
    // Password validation
    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }
    
    // Tenant slug validation
    if (!formData.tenantSlug.trim()) {
      errors.tenantSlug = 'Tenant identifier is required';
    } else if (!/^[a-z0-9-]+$/.test(formData.tenantSlug)) {
      errors.tenantSlug = 'Tenant identifier can only contain lowercase letters, numbers, and hyphens';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // =====================================================
  // EVENT HANDLERS
  // =====================================================
  
  const handleInputChange = (field: keyof LoginFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (formErrors[field as keyof LoginFormErrors]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }));
    }
    
    // Clear general error
    if (formErrors.general) {
      setFormErrors(prev => ({ ...prev, general: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitAttempted(true);
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    setFormErrors({});
    
    try {
      const result = await login({
        email: formData.email.trim(),
        password: formData.password,
        tenantSlug: formData.tenantSlug.trim(),
        rememberMe: formData.rememberMe,
      });
      
      if (result.error) {
        setFormErrors({ general: result.error });
      } else if (result.data) {
        // Success is handled by the auth context (redirect)
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (error) {
      setFormErrors({ 
        general: error instanceof Error ? error.message : 'Login failed' 
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(prev => !prev);
  };

  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderFormField = ({
    id,
    label,
    type = 'text',
    value,
    onChange,
    placeholder,
    icon: Icon,
    error,
    required = false,
    autoComplete,
    onToggleVisibility,
    showToggle = false,
  }: {
    id: string;
    label: string;
    type?: string;
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
    icon?: React.ElementType;
    error?: string;
    required?: boolean;
    autoComplete?: string;
    onToggleVisibility?: () => void;
    showToggle?: boolean;
  }) => {
    const hasError = !!error;
    const hasValue = value.trim().length > 0;
    
    return (
      <div className="space-y-1">
        <label htmlFor={id} className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
        
        <div className="relative">
          {Icon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <Icon className={`w-5 h-5 ${hasError ? 'text-red-400' : hasValue ? 'text-green-500' : 'text-gray-400'}`} />
            </div>
          )}
          
          <input
            id={id}
            type={type}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
            autoComplete={autoComplete}
            className={`
              w-full px-4 py-3 rounded-lg border transition-all
              ${Icon ? 'pl-12' : ''}
              ${showToggle ? 'pr-12' : ''}
              ${hasError 
                ? 'border-red-300 focus:border-red-500 focus:ring-red-200' 
                : hasValue 
                  ? 'border-green-300 bg-green-50 focus:border-green-500 focus:ring-green-200'
                  : 'border-gray-300 focus:border-orange-500 focus:ring-orange-200'
              }
              focus:outline-none focus:ring-2 focus:ring-opacity-50
              placeholder-gray-400
            `}
          />
          
          {showToggle && onToggleVisibility && (
            <button
              type="button"
              onClick={onToggleVisibility}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {type === 'password' ? (
                <Eye className="w-5 h-5" />
              ) : (
                <EyeOff className="w-5 h-5" />
              )}
            </button>
          )}
          
          {hasValue && !hasError && !showToggle && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <CheckCircle className="w-5 h-5 text-green-500" />
            </div>
          )}
        </div>
        
        {error && (
          <div className="flex items-center space-x-1 text-red-600 text-sm">
            <AlertCircle className="w-4 h-4" />
            <span>{error}</span>
          </div>
        )}
      </div>
    );
  };

  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  if (authState.loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" text="Loading..." />
      </div>
    );
  }

  return (
    <div className={`min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8 ${className}`}>
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center">
              <Shield className="w-8 h-8 text-orange-600" />
            </div>
          </div>
          
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Admin Portal
          </h2>
          
          <p className="text-gray-600">
            Sign in to your tenant admin dashboard
          </p>
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="bg-white shadow-lg rounded-lg p-8 space-y-6">
          {/* General Error */}
          {formErrors.general && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
                <div className="text-red-800 text-sm">
                  <p className="font-medium">Sign in failed</p>
                  <p className="mt-1">{formErrors.general}</p>
                </div>
              </div>
            </div>
          )}

          {/* Auth State Error */}
          {authState.error && !formErrors.general && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
                <div className="text-red-800 text-sm">
                  <p className="font-medium">Authentication Error</p>
                  <p className="mt-1">{authState.error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Form Fields */}
          <div className="space-y-4">
            {renderFormField({
              id: 'email',
              label: 'Email Address',
              type: 'email',
              value: formData.email,
              onChange: (value) => handleInputChange('email', value),
              placeholder: '<EMAIL>',
              icon: Mail,
              error: formErrors.email,
              required: true,
              autoComplete: 'email',
            })}

            {renderFormField({
              id: 'password',
              label: 'Password',
              type: showPassword ? 'text' : 'password',
              value: formData.password,
              onChange: (value) => handleInputChange('password', value),
              placeholder: 'Enter your password',
              icon: Lock,
              error: formErrors.password,
              required: true,
              autoComplete: 'current-password',
              onToggleVisibility: togglePasswordVisibility,
              showToggle: true,
            })}

            {renderFormField({
              id: 'tenantSlug',
              label: 'Tenant Identifier',
              value: formData.tenantSlug,
              onChange: (value) => handleInputChange('tenantSlug', value.toLowerCase()),
              placeholder: 'restaurant-name',
              icon: Building,
              error: formErrors.tenantSlug,
              required: true,
              autoComplete: 'organization',
            })}
          </div>

          {/* Remember Me */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="rememberMe"
                type="checkbox"
                checked={formData.rememberMe}
                onChange={(e) => handleInputChange('rememberMe', e.target.checked)}
                className="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
              />
              <label htmlFor="rememberMe" className="ml-2 text-sm text-gray-700">
                Keep me signed in
              </label>
            </div>

            <a
              href="/admin/forgot-password"
              className="text-sm text-orange-600 hover:text-orange-500 underline"
            >
              Forgot password?
            </a>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting || authState.loading}
            className={`
              w-full flex items-center justify-center px-4 py-3 rounded-lg font-semibold text-white transition-all
              ${isSubmitting || authState.loading
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-orange-600 hover:bg-orange-700 active:scale-95 shadow-lg hover:shadow-xl'
              }
            `}
          >
            {isSubmitting ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Signing in...
              </>
            ) : (
              <>
                <LogIn className="w-5 h-5 mr-2" />
                Sign In
                <ArrowRight className="w-4 h-4 ml-2" />
              </>
            )}
          </button>

          {/* Help Text */}
          <div className="text-center">
            <p className="text-sm text-gray-600">
              Need access?{' '}
              <a 
                href="mailto:<EMAIL>" 
                className="text-orange-600 hover:text-orange-500 underline"
              >
                Contact support
              </a>
            </p>
          </div>
        </form>

        {/* Security Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <Shield className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
            <div className="text-blue-800 text-sm">
              <p className="font-medium">Secure Admin Access</p>
              <p className="mt-1">
                This portal is for authorized tenant administrators only. 
                All login attempts are logged and monitored.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
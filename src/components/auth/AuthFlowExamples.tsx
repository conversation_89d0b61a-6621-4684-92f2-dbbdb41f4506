// BHEEMDINE Authentication Flow Examples
// React components demonstrating different auth flows

import React, { useState } from 'react';
import { bheemdineAuth } from '@/lib/auth/supabase-auth';

// =====================================================
// QR CODE GUEST FLOW
// =====================================================

export const QRCodeGuestFlow: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleQRScan = async (qrCode: string) => {
    setLoading(true);
    setError(null);

    try {
      // Step 1: Validate QR code
      const { data: qrData, error: qrError } = await bheemdineAuth.validateQRCode(qrCode);
      
      if (qrError) {
        throw new Error(qrError);
      }

      if (!qrData?.valid) {
        throw new Error('Invalid QR code');
      }

      // Step 2: Create guest session
      const { data: sessionData, error: sessionError } = await bheemdineAuth.createGuestSession(
        qrData.tenant_id,
        qrData.room_id,
        'Guest User'
      );

      if (sessionError) {
        throw new Error(sessionError);
      }

      // Step 3: Redirect to menu
      setSuccess(true);
      window.location.href = `/menu?room=${qrData.room_number}`;

    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="qr-guest-flow">
      <h2>QR Code Scanner</h2>
      {/* QR Scanner component would go here */}
      <button 
        onClick={() => handleQRScan('QR-DEMO-101')}
        disabled={loading}
      >
        Simulate QR Scan
      </button>
      
      {error && <div className="error">{error}</div>}
      {success && <div className="success">Welcome! Redirecting to menu...</div>}
    </div>
  );
};

// =====================================================
// STAFF PIN AUTHENTICATION
// =====================================================

export const StaffPINAuth: React.FC<{ tenantId: string }> = ({ tenantId }) => {
  const [email, setEmail] = useState('');
  const [pin, setPIN] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleStaffLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await bheemdineAuth.authenticateStaffPIN(
        tenantId,
        email,
        pin
      );

      if (error) {
        throw new Error(error);
      }

      // Redirect based on staff role
      switch (data?.staffRole) {
        case 'ADMIN':
        case 'MANAGER':
          window.location.href = '/staff/dashboard';
          break;
        case 'CHEF':
          window.location.href = '/staff/kitchen';
          break;
        case 'WAITER':
          window.location.href = '/staff/orders';
          break;
        default:
          window.location.href = '/staff';
      }

    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleStaffLogin} className="staff-pin-auth">
      <h2>Staff Login</h2>
      
      <input
        type="email"
        placeholder="Email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        required
      />
      
      <input
        type="password"
        placeholder="PIN"
        value={pin}
        onChange={(e) => setPIN(e.target.value)}
        maxLength={6}
        pattern="[0-9]{4,6}"
        required
      />
      
      <button type="submit" disabled={loading}>
        {loading ? 'Logging in...' : 'Login'}
      </button>
      
      {error && <div className="error">{error}</div>}
    </form>
  );
};

// =====================================================
// QUICK STAFF RE-AUTH (FOR SENSITIVE ACTIONS)
// =====================================================

export const QuickStaffReAuth: React.FC<{ onSuccess: () => void }> = ({ onSuccess }) => {
  const [pin, setPIN] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleQuickAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await bheemdineAuth.quickStaffAuth(pin);

      if (error) {
        throw new Error(error);
      }

      if (!data) {
        throw new Error('Invalid PIN');
      }

      onSuccess();
      setPIN(''); // Clear PIN

    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleQuickAuth} className="quick-reauth">
      <h3>Confirm Your PIN</h3>
      <p>This action requires PIN confirmation</p>
      
      <input
        type="password"
        placeholder="Enter PIN"
        value={pin}
        onChange={(e) => setPIN(e.target.value)}
        maxLength={6}
        pattern="[0-9]{4,6}"
        required
        autoFocus
      />
      
      <button type="submit" disabled={loading}>
        Confirm
      </button>
      
      {error && <div className="error">{error}</div>}
    </form>
  );
};

// =====================================================
// GUEST TO CUSTOMER CONVERSION
// =====================================================

export const GuestToCustomerConversion: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleConversion = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await bheemdineAuth.promoteGuestToCustomer(
        email,
        password
      );

      if (error) {
        throw new Error(error);
      }

      setSuccess(true);
      
      // Redirect to account page after 2 seconds
      setTimeout(() => {
        window.location.href = '/account';
      }, 2000);

    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleConversion} className="guest-conversion">
      <h2>Create Account</h2>
      <p>Save your order history and earn rewards!</p>
      
      <input
        type="email"
        placeholder="Email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        required
      />
      
      <input
        type="password"
        placeholder="Password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        minLength={8}
        required
      />
      
      <button type="submit" disabled={loading}>
        {loading ? 'Creating Account...' : 'Create Account'}
      </button>
      
      <button type="button" className="secondary">
        Continue as Guest
      </button>
      
      {error && <div className="error">{error}</div>}
      {success && <div className="success">Account created! Redirecting...</div>}
    </form>
  );
};

// =====================================================
// MULTI-TENANT STAFF SWITCHER
// =====================================================

export const TenantSwitcher: React.FC<{ 
  tenants: Array<{ id: string; name: string }> 
}> = ({ tenants }) => {
  const [switching, setSwitching] = useState(false);
  const [currentTenant, setCurrentTenant] = useState<string>('');

  React.useEffect(() => {
    // Get current tenant from claims
    bheemdineAuth.getCustomClaims().then(claims => {
      setCurrentTenant(claims.tenant_id || '');
    });
  }, []);

  const handleTenantSwitch = async (newTenantId: string) => {
    setSwitching(true);

    try {
      const { data, error } = await bheemdineAuth.switchTenant(newTenantId);

      if (error) {
        throw new Error(error);
      }

      if (data) {
        // Reload to apply new context
        window.location.reload();
      }

    } catch (err) {
      console.error('Failed to switch tenant:', err);
    } finally {
      setSwitching(false);
    }
  };

  return (
    <div className="tenant-switcher">
      <label>Current Location:</label>
      <select 
        value={currentTenant} 
        onChange={(e) => handleTenantSwitch(e.target.value)}
        disabled={switching}
      >
        {tenants.map(tenant => (
          <option key={tenant.id} value={tenant.id}>
            {tenant.name}
          </option>
        ))}
      </select>
    </div>
  );
};

// =====================================================
// AUTH GUARD HOC
// =====================================================

export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredRole?: string
): React.FC<P> {
  return (props: P) => {
    const [authorized, setAuthorized] = useState(false);
    const [loading, setLoading] = useState(true);

    React.useEffect(() => {
      checkAuth();
    }, []);

    const checkAuth = async () => {
      try {
        const session = await bheemdineAuth.getSession();
        
        if (!session) {
          window.location.href = '/login';
          return;
        }

        if (requiredRole) {
          const hasRequiredRole = await bheemdineAuth.hasRole(requiredRole);
          
          if (!hasRequiredRole) {
            window.location.href = '/unauthorized';
            return;
          }
        }

        setAuthorized(true);
      } catch (err) {
        console.error('Auth check failed:', err);
        window.location.href = '/login';
      } finally {
        setLoading(false);
      }
    };

    if (loading) {
      return <div>Loading...</div>;
    }

    if (!authorized) {
      return null;
    }

    return <Component {...props} />;
  };
}

// Usage example:
// const ProtectedAdminPage = withAuth(AdminDashboard, 'ADMIN');
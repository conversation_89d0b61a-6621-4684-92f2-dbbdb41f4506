// Unauthorized access page component
'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { 
  ShieldX, 
  ArrowLeft, 
  Home, 
  LogOut,
  AlertTriangle,
  UserX,
  Lock
} from 'lucide-react';
import { useAdminAuth, useAuthState } from '@/contexts/AdminAuthContext';
import type { AdminPermission, AdminRole } from '@/types/auth';

interface UnauthorizedPageProps {
  requiredRoles?: (AdminRole | string)[];
  requiredPermissions?: AdminPermission[];
  title?: string;
  message?: string;
  showActions?: boolean;
  className?: string;
}

export function UnauthorizedPage({
  requiredRoles = [],
  requiredPermissions = [],
  title = 'Access Denied',
  message,
  showActions = true,
  className = ''
}: UnauthorizedPageProps) {
  
  const router = useRouter();
  const { logout } = useAdminAuth();
  const authState = useAuthState();

  // =====================================================
  // EVENT HANDLERS
  // =====================================================

  const handleGoBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push('/admin/dashboard');
    }
  };

  const handleGoHome = () => {
    router.push('/admin/dashboard');
  };

  const handleLogout = async () => {
    await logout();
  };

  // =====================================================
  // HELPER FUNCTIONS
  // =====================================================

  const getDefaultMessage = (): string => {
    if (message) return message;

    if (!authState.isAuthenticated) {
      return 'You need to be logged in to access this page.';
    }

    if (requiredRoles.length > 0 && requiredPermissions.length > 0) {
      return `You need one of the following roles: ${requiredRoles.join(', ')} and the following permissions: ${requiredPermissions.join(', ')} to access this page.`;
    }

    if (requiredRoles.length > 0) {
      return `You need one of the following roles to access this page: ${requiredRoles.join(', ')}.`;
    }

    if (requiredPermissions.length > 0) {
      return `You need the following permissions to access this page: ${requiredPermissions.join(', ')}.`;
    }

    return 'You do not have permission to access this page.';
  };

  const getIcon = () => {
    if (!authState.isAuthenticated) {
      return <Lock className="w-16 h-16 text-red-500" />;
    }

    if (requiredRoles.length > 0) {
      return <UserX className="w-16 h-16 text-red-500" />;
    }

    return <ShieldX className="w-16 h-16 text-red-500" />;
  };

  const getSuggestions = (): string[] => {
    const suggestions: string[] = [];

    if (!authState.isAuthenticated) {
      suggestions.push('Please log in with an account that has the required permissions.');
      return suggestions;
    }

    if (requiredRoles.length > 0) {
      suggestions.push('Contact your administrator to request the required role.');
    }

    if (requiredPermissions.length > 0) {
      suggestions.push('Contact your administrator to request the required permissions.');
    }

    if (authState.currentTenant) {
      suggestions.push('Make sure you are accessing the correct tenant.');
    }

    suggestions.push('If you believe this is an error, please contact support.');

    return suggestions;
  };

  // =====================================================
  // RENDER
  // =====================================================

  return (
    <div className={`min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8 ${className}`}>
      <div className="max-w-md w-full space-y-8">
        {/* Icon and Title */}
        <div className="text-center">
          <div className="flex justify-center mb-4">
            {getIcon()}
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {title}
          </h1>
          
          <div className="flex items-center justify-center mb-4">
            <AlertTriangle className="w-5 h-5 text-amber-500 mr-2" />
            <span className="text-amber-600 font-medium">
              HTTP 403 - Forbidden
            </span>
          </div>
        </div>

        {/* Error Message */}
        <div className="bg-white shadow rounded-lg p-6">
          <p className="text-gray-700 text-base leading-relaxed mb-4">
            {getDefaultMessage()}
          </p>

          {/* Current User Info */}
          {authState.isAuthenticated && authState.user && (
            <div className="bg-gray-50 rounded-lg p-4 mb-4">
              <h3 className="font-medium text-gray-900 mb-2">Current Session:</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <p><strong>Email:</strong> {authState.user.email}</p>
                {authState.user.claims.admin_role && (
                  <p><strong>Role:</strong> {authState.user.claims.admin_role}</p>
                )}
                {authState.currentTenant && (
                  <p><strong>Tenant:</strong> {authState.currentTenant.name}</p>
                )}
                <p><strong>Permissions:</strong> {authState.permissions.length} granted</p>
              </div>
            </div>
          )}

          {/* Required Access */}
          {(requiredRoles.length > 0 || requiredPermissions.length > 0) && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <h3 className="font-medium text-red-800 mb-2">Required Access:</h3>
              <div className="text-sm text-red-700 space-y-1">
                {requiredRoles.length > 0 && (
                  <p><strong>Roles:</strong> {requiredRoles.join(', ')}</p>
                )}
                {requiredPermissions.length > 0 && (
                  <p><strong>Permissions:</strong> {requiredPermissions.join(', ')}</p>
                )}
              </div>
            </div>
          )}

          {/* Suggestions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-medium text-blue-800 mb-2">What you can do:</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              {getSuggestions().map((suggestion, index) => (
                <li key={index} className="flex items-start">
                  <span className="inline-block w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-2 flex-shrink-0" />
                  {suggestion}
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Actions */}
        {showActions && (
          <div className="space-y-3">
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
              <button
                onClick={handleGoBack}
                className="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Go Back
              </button>
              
              <button
                onClick={handleGoHome}
                className="flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
              >
                <Home className="w-4 h-4 mr-2" />
                Dashboard
              </button>
            </div>

            {authState.isAuthenticated && (
              <button
                onClick={handleLogout}
                className="w-full flex items-center justify-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out & Try Different Account
              </button>
            )}
          </div>
        )}

        {/* Support Info */}
        <div className="text-center">
          <p className="text-sm text-gray-500">
            Need help? Contact{' '}
            <a 
              href="mailto:<EMAIL>" 
              className="text-orange-600 hover:text-orange-500 underline"
            >
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}

// =====================================================
// SPECIALIZED UNAUTHORIZED COMPONENTS
// =====================================================

export function InsufficientPermissionsPage({ permissions }: { permissions: AdminPermission[] }) {
  return (
    <UnauthorizedPage
      requiredPermissions={permissions}
      title="Insufficient Permissions"
      message="You don't have the required permissions to access this feature."
    />
  );
}

export function InvalidRolePage({ roles }: { roles: (AdminRole | string)[] }) {
  return (
    <UnauthorizedPage
      requiredRoles={roles}
      title="Access Restricted"
      message="Your current role doesn't allow access to this area."
    />
  );
}

export function TenantAccessDeniedPage() {
  return (
    <UnauthorizedPage
      title="Tenant Access Denied"
      message="You don't have access to this tenant or the tenant is inactive."
    />
  );
}

export function SessionExpiredPage() {
  return (
    <UnauthorizedPage
      title="Session Expired"
      message="Your session has expired. Please log in again."
      showActions={false}
    />
  );
}
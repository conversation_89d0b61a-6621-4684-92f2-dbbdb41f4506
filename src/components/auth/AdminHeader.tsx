// Admin header component with user profile and logout functionality
'use client';

import React, { useState, useRef, useEffect } from 'react';
import { 
  User, 
  LogOut, 
  Settings, 
  Shield, 
  Building, 
  ChevronDown,
  Menu,
  X,
  Bell,
  Search,
  Sun,
  Moon,
  HelpCircle
} from 'lucide-react';
import { useAdminAuth, useAuthState } from '@/contexts/AdminAuthContext';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface AdminHeaderProps {
  onMenuToggle?: () => void;
  showMenuButton?: boolean;
  className?: string;
}

export function AdminHeader({ 
  onMenuToggle, 
  showMenuButton = true, 
  className = '' 
}: AdminHeaderProps) {
  
  // =====================================================
  // HOOKS & STATE
  // =====================================================
  
  const { logout, getAccessibleTenants, switchTenant } = useAdminAuth();
  const authState = useAuthState();
  
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showTenantSwitcher, setShowTenantSwitcher] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [accessibleTenants, setAccessibleTenants] = useState<any[]>([]);
  
  const userMenuRef = useRef<HTMLDivElement>(null);
  const tenantMenuRef = useRef<HTMLDivElement>(null);

  // =====================================================
  // EFFECTS
  // =====================================================
  
  // Close dropdowns when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
      if (tenantMenuRef.current && !tenantMenuRef.current.contains(event.target as Node)) {
        setShowTenantSwitcher(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Load accessible tenants
  useEffect(() => {
    const loadTenants = async () => {
      if (authState.user) {
        const tenants = await getAccessibleTenants();
        setAccessibleTenants(tenants);
      }
    };
    
    loadTenants();
  }, [authState.user, getAccessibleTenants]);

  // =====================================================
  // EVENT HANDLERS
  // =====================================================
  
  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  const handleTenantSwitch = async (tenantId: string) => {
    if (tenantId === authState.currentTenant?.id) {
      setShowTenantSwitcher(false);
      return;
    }

    try {
      await switchTenant(tenantId);
      setShowTenantSwitcher(false);
    } catch (error) {
      console.error('Tenant switch failed:', error);
    }
  };

  const toggleUserMenu = () => {
    setShowUserMenu(prev => !prev);
    setShowTenantSwitcher(false);
  };

  const toggleTenantSwitcher = () => {
    setShowTenantSwitcher(prev => !prev);
    setShowUserMenu(false);
  };

  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderUserAvatar = () => {
    const user = authState.user;
    if (!user) return null;

    const initials = user.name
      ? user.name.split(' ').map(n => n[0]).join('').toUpperCase()
      : user.email.charAt(0).toUpperCase();

    return (
      <div className="relative">
        {user.avatar_url ? (
          <img
            src={user.avatar_url}
            alt={user.name || user.email}
            className="w-8 h-8 rounded-full object-cover"
          />
        ) : (
          <div className="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
            {initials}
          </div>
        )}
        
        {/* Online status indicator */}
        <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
      </div>
    );
  };

  const renderUserMenu = () => {
    if (!showUserMenu || !authState.user) return null;

    const user = authState.user;

    return (
      <div 
        ref={userMenuRef}
        className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
      >
        {/* User Info Header */}
        <div className="px-4 py-3 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            {renderUserAvatar()}
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user.name || 'Admin User'}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {user.email}
              </p>
            </div>
            <div className="flex-shrink-0">
              <span className={`
                inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                ${user.claims.admin_role === 'SUPER_ADMIN' 
                  ? 'bg-purple-100 text-purple-800' 
                  : user.claims.admin_role === 'TENANT_ADMIN'
                    ? 'bg-orange-100 text-orange-800'
                    : 'bg-blue-100 text-blue-800'
                }
              `}>
                {user.claims.admin_role || 'Admin'}
              </span>
            </div>
          </div>
        </div>

        {/* Current Tenant */}
        {authState.currentTenant && (
          <div className="px-4 py-2 border-b border-gray-200">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Building className="w-4 h-4" />
              <span>Current tenant:</span>
              <span className="font-medium text-gray-900">
                {authState.currentTenant.name}
              </span>
            </div>
          </div>
        )}

        {/* Menu Items */}
        <div className="py-1">
          <button
            onClick={() => {/* Handle profile edit */}}
            className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
          >
            <User className="w-4 h-4 mr-3" />
            Profile Settings
          </button>
          
          <button
            onClick={() => {/* Handle account settings */}}
            className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
          >
            <Settings className="w-4 h-4 mr-3" />
            Account Settings
          </button>
          
          <button
            onClick={() => {/* Handle security settings */}}
            className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
          >
            <Shield className="w-4 h-4 mr-3" />
            Security
          </button>
          
          <button
            onClick={() => {/* Handle help */}}
            className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
          >
            <HelpCircle className="w-4 h-4 mr-3" />
            Help & Support
          </button>
        </div>

        {/* Logout */}
        <div className="border-t border-gray-200 py-1">
          <button
            onClick={handleLogout}
            disabled={isLoggingOut}
            className="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50"
          >
            {isLoggingOut ? (
              <>
                <LoadingSpinner size="sm" className="mr-3" />
                Signing out...
              </>
            ) : (
              <>
                <LogOut className="w-4 h-4 mr-3" />
                Sign Out
              </>
            )}
          </button>
        </div>

        {/* Session Info */}
        <div className="px-4 py-2 border-t border-gray-200 bg-gray-50">
          <div className="text-xs text-gray-500 space-y-1">
            <p>Last sign in: {user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Unknown'}</p>
            <p>Permissions: {authState.permissions.length} active</p>
          </div>
        </div>
      </div>
    );
  };

  const renderTenantSwitcher = () => {
    if (!showTenantSwitcher || accessibleTenants.length <= 1) return null;

    return (
      <div 
        ref={tenantMenuRef}
        className="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
      >
        <div className="px-4 py-2 border-b border-gray-200">
          <p className="text-sm font-medium text-gray-900">Switch Tenant</p>
          <p className="text-xs text-gray-500">Select a different restaurant location</p>
        </div>

        <div className="py-1 max-h-64 overflow-y-auto">
          {accessibleTenants.map((tenant) => (
            <button
              key={tenant.id}
              onClick={() => handleTenantSwitch(tenant.id)}
              className={`
                flex items-center w-full px-4 py-2 text-sm transition-colors
                ${tenant.id === authState.currentTenant?.id
                  ? 'bg-orange-50 text-orange-700 font-medium'
                  : 'text-gray-700 hover:bg-gray-100'
                }
              `}
            >
              <Building className={`w-4 h-4 mr-3 ${
                tenant.id === authState.currentTenant?.id ? 'text-orange-600' : 'text-gray-400'
              }`} />
              <div className="flex-1 text-left">
                <div className="font-medium">{tenant.name}</div>
                <div className="text-xs text-gray-500">{tenant.slug}</div>
              </div>
              {tenant.id === authState.currentTenant?.id && (
                <div className="w-2 h-2 bg-orange-600 rounded-full"></div>
              )}
            </button>
          ))}
        </div>
      </div>
    );
  };

  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  if (!authState.user) {
    return null;
  }

  return (
    <header className={`bg-white shadow-sm border-b border-gray-200 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left side */}
          <div className="flex items-center space-x-4">
            {/* Mobile menu button */}
            {showMenuButton && (
              <button
                onClick={onMenuToggle}
                className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 lg:hidden"
              >
                <Menu className="w-6 h-6" />
              </button>
            )}

            {/* Tenant switcher */}
            {accessibleTenants.length > 1 && (
              <div className="relative">
                <button
                  onClick={toggleTenantSwitcher}
                  className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
                >
                  <Building className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-900 hidden sm:inline">
                    {authState.currentTenant?.name || 'Select Tenant'}
                  </span>
                  <ChevronDown className="w-4 h-4 text-gray-600" />
                </button>
                
                {renderTenantSwitcher()}
              </div>
            )}
          </div>

          {/* Center - Search (optional) */}
          <div className="flex-1 max-w-md mx-4 hidden md:block">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              />
            </div>
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            {/* Notifications */}
            <button className="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-md relative">
              <Bell className="w-5 h-5" />
              <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400"></span>
            </button>

            {/* Theme toggle */}
            <button className="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-md">
              <Sun className="w-5 h-5" />
            </button>

            {/* User menu */}
            <div className="relative">
              <button
                onClick={toggleUserMenu}
                className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                {renderUserAvatar()}
                <div className="hidden sm:block text-left">
                  <p className="text-sm font-medium text-gray-900">
                    {authState.user.name || 'Admin'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {authState.user.claims.admin_role}
                  </p>
                </div>
                <ChevronDown className="w-4 h-4 text-gray-600" />
              </button>
              
              {renderUserMenu()}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
// Protected route components with role-based access control
'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useAdminAuth, useAuthState } from '@/contexts/AdminAuthContext';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { UnauthorizedPage } from '@/components/auth/UnauthorizedPage';
import type { AdminPermission, AdminRole } from '@/types/auth';

// =====================================================
// PROTECTED ROUTE COMPONENT
// =====================================================

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  roles?: (AdminRole | string)[];
  permissions?: AdminPermission[];
  requireAllPermissions?: boolean; // If true, user must have ALL permissions
  fallback?: React.ReactNode;
  redirectTo?: string;
  showUnauthorized?: boolean;
}

export function ProtectedRoute({
  children,
  requireAuth = true,
  roles = [],
  permissions = [],
  requireAllPermissions = false,
  fallback,
  redirectTo,
  showUnauthorized = true,
}: ProtectedRouteProps) {
  const router = useRouter();
  const { hasRole, hasPermission, hasAnyPermission, hasAllPermissions } = useAdminAuth();
  const authState = useAuthState();

  // Show loading while auth state is being determined
  if (authState.loading) {
    return fallback || <LoadingSpinner />;
  }

  // Check authentication requirement
  if (requireAuth && !authState.isAuthenticated) {
    if (redirectTo) {
      router.push(redirectTo);
      return null;
    }
    return fallback || <LoadingSpinner />;
  }

  // Check role requirements
  if (roles.length > 0) {
    const hasRequiredRole = roles.some(role => hasRole(role));
    if (!hasRequiredRole) {
      if (redirectTo) {
        router.push(redirectTo);
        return null;
      }
      if (showUnauthorized) {
        return <UnauthorizedPage requiredRoles={roles} />;
      }
      return fallback || null;
    }
  }

  // Check permission requirements
  if (permissions.length > 0) {
    const hasRequiredPermissions = requireAllPermissions
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);

    if (!hasRequiredPermissions) {
      if (redirectTo) {
        router.push(redirectTo);
        return null;
      }
      if (showUnauthorized) {
        return <UnauthorizedPage requiredPermissions={permissions} />;
      }
      return fallback || null;
    }
  }

  // All checks passed, render children
  return <>{children}</>;
}

// =====================================================
// ROLE-BASED WRAPPER COMPONENTS
// =====================================================

interface RoleBasedProps {
  children: React.ReactNode;
  roles: (AdminRole | string)[];
  fallback?: React.ReactNode;
  showUnauthorized?: boolean;
}

export function RequireRole({ children, roles, fallback, showUnauthorized = false }: RoleBasedProps) {
  return (
    <ProtectedRoute
      roles={roles}
      fallback={fallback}
      showUnauthorized={showUnauthorized}
    >
      {children}
    </ProtectedRoute>
  );
}

interface PermissionBasedProps {
  children: React.ReactNode;
  permissions: AdminPermission[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
  showUnauthorized?: boolean;
}

export function RequirePermission({ 
  children, 
  permissions, 
  requireAll = false, 
  fallback, 
  showUnauthorized = false 
}: PermissionBasedProps) {
  return (
    <ProtectedRoute
      permissions={permissions}
      requireAllPermissions={requireAll}
      fallback={fallback}
      showUnauthorized={showUnauthorized}
    >
      {children}
    </ProtectedRoute>
  );
}

// =====================================================
// CONDITIONAL RENDERING COMPONENTS
// =====================================================

interface ConditionalRenderProps {
  children: React.ReactNode;
  condition: boolean;
  fallback?: React.ReactNode;
}

export function IfAuthenticated({ children, fallback }: Omit<ConditionalRenderProps, 'condition'>) {
  const { isAuthenticated } = useAuthState();
  
  if (!isAuthenticated) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
}

export function IfNotAuthenticated({ children, fallback }: Omit<ConditionalRenderProps, 'condition'>) {
  const { isAuthenticated } = useAuthState();
  
  if (isAuthenticated) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
}

export function IfHasRole({ children, roles, fallback }: RoleBasedProps) {
  const { hasRole } = useAdminAuth();
  
  const hasRequiredRole = roles.some(role => hasRole(role));
  
  if (!hasRequiredRole) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
}

export function IfHasPermission({ children, permissions, requireAll = false, fallback }: PermissionBasedProps) {
  const { hasAnyPermission, hasAllPermissions } = useAdminAuth();
  
  const hasRequiredPermissions = requireAll
    ? hasAllPermissions(permissions)
    : hasAnyPermission(permissions);
  
  if (!hasRequiredPermissions) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
}

// =====================================================
// ADMIN-SPECIFIC COMPONENTS
// =====================================================

export function AdminOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RequireRole roles={['SUPER_ADMIN', 'TENANT_ADMIN']} fallback={fallback}>
      {children}
    </RequireRole>
  );
}

export function SuperAdminOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RequireRole roles={['SUPER_ADMIN']} fallback={fallback}>
      {children}
    </RequireRole>
  );
}

export function TenantAdminOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RequireRole roles={['TENANT_ADMIN']} fallback={fallback}>
      {children}
    </RequireRole>
  );
}

export function ManagerOrAbove({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RequireRole roles={['SUPER_ADMIN', 'TENANT_ADMIN', 'MANAGER']} fallback={fallback}>
      {children}
    </RequireRole>
  );
}

// =====================================================
// HOC FOR PROTECTED PAGES
// =====================================================

interface WithAuthOptions {
  requireAuth?: boolean;
  roles?: (AdminRole | string)[];
  permissions?: AdminPermission[];
  requireAllPermissions?: boolean;
  redirectTo?: string;
  fallback?: React.ComponentType;
}

export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: WithAuthOptions = {}
) {
  const WrappedComponent = (props: P) => {
    const {
      requireAuth = true,
      roles = [],
      permissions = [],
      requireAllPermissions = false,
      redirectTo,
      fallback: Fallback
    } = options;

    return (
      <ProtectedRoute
        requireAuth={requireAuth}
        roles={roles}
        permissions={permissions}
        requireAllPermissions={requireAllPermissions}
        redirectTo={redirectTo}
        fallback={Fallback ? <Fallback /> : undefined}
      >
        <Component {...props} />
      </ProtectedRoute>
    );
  };

  WrappedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

// =====================================================
// USAGE EXAMPLES
// =====================================================

/*
// Basic protected route
<ProtectedRoute>
  <AdminDashboard />
</ProtectedRoute>

// Require specific role
<ProtectedRoute roles={['TENANT_ADMIN', 'SUPER_ADMIN']}>
  <TenantSettings />
</ProtectedRoute>

// Require specific permissions
<ProtectedRoute permissions={['menu:edit', 'menu:create']}>
  <MenuEditor />
</ProtectedRoute>

// Require all permissions
<ProtectedRoute 
  permissions={['orders:view', 'orders:edit']} 
  requireAllPermissions={true}
>
  <OrderManagement />
</ProtectedRoute>

// Conditional rendering
<IfHasRole roles={['SUPER_ADMIN']}>
  <SuperAdminPanel />
</IfHasRole>

<IfHasPermission permissions={['reports:export']}>
  <ExportButton />
</IfHasPermission>

// HOC usage
const ProtectedDashboard = withAuth(Dashboard, {
  roles: ['TENANT_ADMIN'],
  permissions: ['dashboard:view']
});

// Admin-specific shortcuts
<AdminOnly>
  <AdminPanel />
</AdminOnly>

<SuperAdminOnly>
  <SystemSettings />
</SuperAdminOnly>
*/
// BHEEMDINE Digital Menu Component - Mobile-First Responsive Design
// Main menu interface with category navigation, search, and item display

'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Search, Filter, ShoppingCart, Heart, Clock, Users } from 'lucide-react';
import { useMenuStore, useFilteredMenuItems, useCartCount, useSelectedCategory } from '@/stores/menuStore';
import { MenuItemCard } from './MenuItemCard';
import { CategoryNavigation } from './CategoryNavigation';
import { SearchAndFilters } from './SearchAndFilters';
import { CartSummary } from './CartSummary';
import { ItemDetailModal } from './ItemDetailModal';
import { LoadingState } from './LoadingState';
import { EmptyState } from './EmptyState';

interface DigitalMenuProps {
  tenantId: string;
  roomId?: string;
  className?: string;
}

export const DigitalMenu: React.FC<DigitalMenuProps> = ({
  tenantId,
  roomId,
  className = ''
}) => {
  // =====================================================
  // STATE & HOOKS
  // =====================================================
  
  // Zustand store hooks
  const {
    menuItems,
    categories,
    isLoading,
    error,
    selectedCategory,
    searchQuery,
    selectedItemId,
    setMenuItems,
    setCategories,
    setLoading,
    setError,
    refreshMenu,
    setSelectedCategory,
    setSelectedItem,
  } = useMenuStore();
  
  // Memoized selectors for performance
  const filteredMenuItems = useFilteredMenuItems();
  const cartCount = useCartCount();
  const currentCategory = useSelectedCategory();
  
  // Local component state
  const [isInitialized, setIsInitialized] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);
  
  // =====================================================
  // EFFECTS
  // =====================================================
  
  // Initialize menu data on component mount
  useEffect(() => {
    const initializeMenu = async () => {
      if (isInitialized) return;
      
      try {
        setLoading(true);
        
        // In a real app, this would fetch from the API
        // const response = await fetch(`/api/menu/${tenantId}`, {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify({
        //     tenant_id: tenantId,
        //     pricing_context: { room_id: roomId },
        //     filters: { available_only: true }
        //   })
        // });
        
        // Mock data for demo - replace with actual API call
        const mockMenuData = await fetchMockMenuData(tenantId);
        setMenuItems(mockMenuData.items);
        setCategories(mockMenuData.categories);
        
        setIsInitialized(true);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load menu');
      } finally {
        setLoading(false);
      }
    };
    
    initializeMenu();
  }, [tenantId, roomId, isInitialized, setLoading, setError, setMenuItems, setCategories]);
  
  // Handle scroll position for scroll-to-top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  // =====================================================
  // COMPUTED VALUES
  // =====================================================
  
  // Group filtered items by category for display
  const itemsByCategory = useMemo(() => {
    const grouped: Record<string, typeof filteredMenuItems> = {};
    
    filteredMenuItems.forEach(item => {
      if (!grouped[item.category]) {
        grouped[item.category] = [];
      }
      grouped[item.category].push(item);
    });
    
    return grouped;
  }, [filteredMenuItems]);
  
  // Get categories that have items after filtering
  const availableCategories = useMemo(() => {
    return categories.filter(category => itemsByCategory[category]?.length > 0);
  }, [categories, itemsByCategory]);
  
  // =====================================================
  // EVENT HANDLERS
  // =====================================================
  
  const handleCategorySelect = (category: string | null) => {
    setSelectedCategory(category);
    
    // Smooth scroll to category section
    if (category) {
      const element = document.getElementById(`category-${category}`);
      if (element) {
        element.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start',
          inline: 'nearest'
        });
      }
    }
  };
  
  const handleItemClick = (itemId: string) => {
    setSelectedItem(itemId);
  };
  
  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };
  
  const handleRefresh = async () => {
    await refreshMenu();
  };
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderCategorySection = (category: string, items: typeof filteredMenuItems) => (
    <section 
      key={category}
      id={`category-${category}`}
      className="mb-8 scroll-mt-20" // Offset for sticky header
    >
      {/* Category Header */}
      <div className="sticky top-16 z-20 bg-white border-b border-gray-100 pb-2 mb-4">
        <h2 className="text-xl font-bold text-gray-900 capitalize">
          {category}
        </h2>
        <p className="text-sm text-gray-500 mt-1">
          {items.length} item{items.length !== 1 ? 's' : ''} available
        </p>
      </div>
      
      {/* Menu Items Grid */}
      <div className="grid grid-cols-1 gap-4">
        {items.map((item) => (
          <MenuItemCard
            key={item.id}
            item={item}
            onClick={() => handleItemClick(item.id)}
            className="transform transition-all duration-200 hover:scale-[1.02] active:scale-[0.98]"
          />
        ))}
      </div>
    </section>
  );
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  if (isLoading && !isInitialized) {
    return <LoadingState />;
  }
  
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-6 max-w-md w-full text-center">
          <div className="text-red-500 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Oops! Something went wrong
          </h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      {/* ===== STICKY HEADER ===== */}
      <div className="sticky top-0 z-30 bg-white border-b border-gray-200 shadow-sm">
        {/* Main Header */}
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            {/* Logo/Title */}
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">B</span>
              </div>
              <div>
                <h1 className="font-bold text-gray-900 text-lg">BHEEMDINE</h1>
                <p className="text-xs text-gray-500">Digital Menu</p>
              </div>
            </div>
            
            {/* Cart Icon with Badge */}
            {cartCount > 0 && (
              <div className="relative">
                <button className="p-2 text-gray-600 hover:text-orange-500 transition-colors">
                  <ShoppingCart className="w-6 h-6" />
                  <span className="absolute -top-1 -right-1 bg-orange-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center font-bold">
                    {cartCount > 99 ? '99+' : cartCount}
                  </span>
                </button>
              </div>
            )}
          </div>
        </div>
        
        {/* Search and Filters */}
        <SearchAndFilters />
        
        {/* Category Navigation */}
        <CategoryNavigation
          categories={availableCategories}
          selectedCategory={currentCategory}
          onCategorySelect={handleCategorySelect}
        />
      </div>
      
      {/* ===== MAIN CONTENT ===== */}
      <main className="pb-20"> {/* Bottom padding for cart summary */}
        <div className="px-4 py-6">
          {/* Loading State for Refresh */}
          {isLoading && isInitialized && (
            <div className="mb-4 p-3 bg-blue-50 text-blue-700 rounded-lg text-sm text-center">
              <Clock className="w-4 h-4 inline mr-2" />
              Refreshing menu...
            </div>
          )}
          
          {/* Results Summary */}
          {(searchQuery || currentCategory) && (
            <div className="mb-6 p-4 bg-white rounded-lg shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">
                    Showing {filteredMenuItems.length} items
                    {searchQuery && (
                      <span> for "{searchQuery}"</span>
                    )}
                    {currentCategory && (
                      <span> in {currentCategory}</span>
                    )}
                  </p>
                </div>
                {(searchQuery || currentCategory) && (
                  <button
                    onClick={() => {
                      useMenuStore.getState().resetFilters();
                    }}
                    className="text-orange-500 hover:text-orange-600 text-sm font-medium"
                  >
                    Clear filters
                  </button>
                )}
              </div>
            </div>
          )}
          
          {/* Menu Content */}
          {filteredMenuItems.length === 0 ? (
            <EmptyState 
              searchQuery={searchQuery}
              selectedCategory={currentCategory}
              onReset={() => useMenuStore.getState().resetFilters()}
            />
          ) : currentCategory ? (
            // Single category view
            <div className="space-y-4">
              {itemsByCategory[currentCategory]?.map((item) => (
                <MenuItemCard
                  key={item.id}
                  item={item}
                  onClick={() => handleItemClick(item.id)}
                  className="transform transition-all duration-200 hover:scale-[1.01] active:scale-[0.99]"
                />
              ))}
            </div>
          ) : (
            // All categories view
            <div className="space-y-8">
              {availableCategories.map(category => 
                renderCategorySection(category, itemsByCategory[category])
              )}
            </div>
          )}
        </div>
      </main>
      
      {/* ===== FLOATING ELEMENTS ===== */}
      
      {/* Scroll to Top Button */}
      {showScrollTop && (
        <button
          onClick={handleScrollToTop}
          className="fixed bottom-24 right-4 z-40 bg-white shadow-lg border border-gray-200 w-12 h-12 rounded-full flex items-center justify-center text-gray-600 hover:text-orange-500 hover:shadow-xl transition-all duration-300"
          aria-label="Scroll to top"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
          </svg>
        </button>
      )}
      
      {/* Cart Summary (Fixed Bottom) */}
      {cartCount > 0 && <CartSummary />}
      
      {/* Item Detail Modal */}
      {selectedItemId && (
        <ItemDetailModal
          itemId={selectedItemId}
          onClose={() => setSelectedItem(null)}
        />
      )}
    </div>
  );
};

// =====================================================
// MOCK DATA FUNCTION (Replace with real API call)
// =====================================================

async function fetchMockMenuData(tenantId: string) {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  return {
    items: [
      {
        id: '1',
        name: 'Grilled Salmon',
        description: 'Fresh Atlantic salmon grilled to perfection with herbs and lemon',
        category: 'mains',
        pricing: {
          basePrice: 28.00,
          currentPrice: 25.20,
          discountPercentage: 10,
          appliedRules: ['Happy Hour Mains']
        },
        availability: {
          isAvailable: true,
          remainingQuantity: 15,
          estimatedWaitTime: 20
        },
        imageUrl: '/images/salmon.jpg',
        isVegetarian: false,
        isVegan: false,
        preparationTime: 20,
        tags: ['gluten-free', 'healthy', 'popular'],
        allergens: [
          { id: '1', name: 'Fish', icon: '🐟', severity: 'CONTAINS' as const }
        ],
        sortOrder: 1
      },
      {
        id: '2',
        name: 'Margherita Pizza',
        description: 'Classic pizza with fresh mozzarella, tomatoes, and basil',
        category: 'mains',
        pricing: {
          basePrice: 18.00,
          currentPrice: 18.00,
          appliedRules: []
        },
        availability: {
          isAvailable: true,
          remainingQuantity: 8,
          estimatedWaitTime: 15
        },
        imageUrl: '/images/pizza.jpg',
        isVegetarian: true,
        isVegan: false,
        preparationTime: 15,
        tags: ['vegetarian', 'popular'],
        allergens: [
          { id: '2', name: 'Gluten', icon: '🌾', severity: 'CONTAINS' as const },
          { id: '3', name: 'Dairy', icon: '🥛', severity: 'CONTAINS' as const }
        ],
        sortOrder: 2
      },
      // Add more mock items...
    ],
    categories: ['appetizers', 'mains', 'desserts', 'beverages']
  };
}
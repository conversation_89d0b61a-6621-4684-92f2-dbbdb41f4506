// BHEEMDINE Loading State Component
// Skeleton loading interface while menu data is being fetched

'use client';

import React from 'react';

export const LoadingState: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Skeleton */}
      <div className="sticky top-0 z-30 bg-white border-b border-gray-200">
        {/* Main Header */}
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-200 rounded-lg animate-pulse" />
              <div>
                <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
                <div className="w-16 h-3 bg-gray-200 rounded animate-pulse mt-1" />
              </div>
            </div>
            <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse" />
          </div>
        </div>
        
        {/* Search Bar Skeleton */}
        <div className="px-4 py-3 border-b border-gray-100">
          <div className="flex items-center space-x-3">
            <div className="flex-1 h-12 bg-gray-200 rounded-lg animate-pulse" />
            <div className="w-12 h-12 bg-gray-200 rounded-lg animate-pulse" />
          </div>
        </div>
        
        {/* Category Navigation Skeleton */}
        <div className="px-4 py-3">
          <div className="flex space-x-2">
            {[1, 2, 3, 4, 5].map((index) => (
              <div
                key={index}
                className="w-20 h-8 bg-gray-200 rounded-full animate-pulse flex-shrink-0"
              />
            ))}
          </div>
        </div>
      </div>
      
      {/* Content Skeleton */}
      <main className="px-4 py-6">
        <div className="space-y-4">
          {/* Menu Item Skeletons */}
          {[1, 2, 3, 4, 5, 6, 7, 8].map((index) => (
            <div
              key={index}
              className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden"
            >
              <div className="flex">
                {/* Image Skeleton */}
                <div className="w-24 h-24 sm:w-28 sm:h-28 bg-gray-200 animate-pulse flex-shrink-0" />
                
                {/* Content Skeleton */}
                <div className="flex-1 p-4">
                  {/* Title and Price */}
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="w-32 h-4 bg-gray-200 rounded animate-pulse mb-2" />
                      <div className="w-20 h-5 bg-gray-200 rounded animate-pulse" />
                    </div>
                    <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse ml-2" />
                  </div>
                  
                  {/* Description */}
                  <div className="space-y-2 mb-3">
                    <div className="w-full h-3 bg-gray-200 rounded animate-pulse" />
                    <div className="w-3/4 h-3 bg-gray-200 rounded animate-pulse" />
                  </div>
                  
                  {/* Tags */}
                  <div className="flex space-x-2 mb-3">
                    <div className="w-16 h-6 bg-gray-200 rounded-full animate-pulse" />
                    <div className="w-12 h-6 bg-gray-200 rounded-full animate-pulse" />
                  </div>
                  
                  {/* Bottom Row */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-4 bg-gray-200 rounded animate-pulse" />
                    </div>
                    <div className="w-16 h-8 bg-gray-200 rounded-lg animate-pulse" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Loading Text */}
        <div className="text-center py-8">
          <div className="inline-flex items-center space-x-2 text-gray-500">
            <svg className="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span className="text-sm font-medium">Loading delicious menu...</span>
          </div>
        </div>
      </main>
    </div>
  );
};
// BHEEMDINE Category Navigation Component
// Horizontal scrollable category pills for menu navigation

'use client';

import React, { useRef, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface CategoryNavigationProps {
  categories: string[];
  selectedCategory: string | null;
  onCategorySelect: (category: string | null) => void;
  className?: string;
}

export const CategoryNavigation: React.FC<CategoryNavigationProps> = ({
  categories,
  selectedCategory,
  onCategorySelect,
  className = ''
}) => {
  // =====================================================
  // REFS & STATE
  // =====================================================
  
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  
  // =====================================================
  // EFFECTS
  // =====================================================
  
  // Auto-scroll to selected category when it changes
  useEffect(() => {
    if (selectedCategory && scrollContainerRef.current) {
      const selectedButton = scrollContainerRef.current.querySelector(
        `[data-category="${selectedCategory}"]`
      ) as HTMLElement;
      
      if (selectedButton) {
        selectedButton.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center'
        });
      }
    }
  }, [selectedCategory]);
  
  // =====================================================
  // EVENT HANDLERS
  // =====================================================
  
  const handleCategoryClick = (category: string | null) => {
    onCategorySelect(category);
    
    // Haptic feedback on mobile
    if ('vibrate' in navigator) {
      navigator.vibrate(30);
    }
  };
  
  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -150,
        behavior: 'smooth'
      });
    }
  };
  
  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollBy({
        left: 150,
        behavior: 'smooth'
      });
    }
  };
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const getCategoryIcon = (category: string): string => {
    const icons: Record<string, string> = {
      appetizers: '🥗',
      mains: '🍽️',
      desserts: '🍰',
      beverages: '🥤',
      sides: '🍟',
      specials: '⭐',
      breakfast: '🍳',
      lunch: '🥪',
      dinner: '🍝',
      pizza: '🍕',
      burgers: '🍔',
      salads: '🥙',
      soups: '🍲',
      seafood: '🐟',
      steaks: '🥩',
      vegetarian: '🥬',
      vegan: '🌱',
      gluten_free: '🌾',
      kids: '🧒',
      alcohol: '🍷',
      cocktails: '🍸',
      wine: '🍾',
      beer: '🍺',
      coffee: '☕',
      tea: '🫖'
    };
    
    return icons[category.toLowerCase()] || '🍽️';
  };
  
  const formatCategoryName = (category: string): string => {
    return category
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  if (categories.length === 0) {
    return null;
  }
  
  return (
    <div className={`relative bg-white border-b border-gray-100 ${className}`}>
      <div className="flex items-center">
        {/* Left Scroll Button - Hidden on mobile */}
        <button
          onClick={scrollLeft}
          className="hidden sm:flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0 ml-2"
          aria-label="Scroll categories left"
        >
          <ChevronLeft className="w-5 h-5" />
        </button>
        
        {/* Category Pills Container */}
        <div
          ref={scrollContainerRef}
          className="flex-1 overflow-x-auto scrollbar-hide px-4 py-3"
          style={{
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
            WebkitOverflowScrolling: 'touch'
          }}
        >
          <div className="flex space-x-2 min-w-max">
            {/* All Categories Button */}
            <button
              onClick={() => handleCategoryClick(null)}
              className={`
                flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-all
                ${selectedCategory === null
                  ? 'bg-orange-500 text-white shadow-md'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 active:bg-gray-300'
                }
              `}
            >
              <span>🍽️</span>
              <span>All</span>
            </button>
            
            {/* Category Buttons */}
            {categories.map((category) => (
              <button
                key={category}
                data-category={category}
                onClick={() => handleCategoryClick(category)}
                className={`
                  flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-all
                  ${selectedCategory === category
                    ? 'bg-orange-500 text-white shadow-md scale-105'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 active:bg-gray-300 hover:scale-105'
                  }
                `}
              >
                <span>{getCategoryIcon(category)}</span>
                <span>{formatCategoryName(category)}</span>
              </button>
            ))}
          </div>
        </div>
        
        {/* Right Scroll Button - Hidden on mobile */}
        <button
          onClick={scrollRight}
          className="hidden sm:flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0 mr-2"
          aria-label="Scroll categories right"
        >
          <ChevronRight className="w-5 h-5" />
        </button>
      </div>
      
      {/* Scroll Indicators for Mobile */}
      <div className="sm:hidden">
        {/* Left fade indicator */}
        <div className="absolute left-0 top-0 bottom-0 w-4 bg-gradient-to-r from-white to-transparent pointer-events-none z-10" />
        
        {/* Right fade indicator */}
        <div className="absolute right-0 top-0 bottom-0 w-4 bg-gradient-to-l from-white to-transparent pointer-events-none z-10" />
      </div>
    </div>
  );
};
// BHEEMDINE Search and Filters Component
// Mobile-optimized search bar with advanced filtering options

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  X, 
  Sliders,
  Leaf,
  Heart,
  Clock,
  DollarSign,
  AlertTriangle,
  Check
} from 'lucide-react';
import { useMenuStore } from '@/stores/menuStore';

interface SearchAndFiltersProps {
  className?: string;
}

export const SearchAndFilters: React.FC<SearchAndFiltersProps> = ({
  className = ''
}) => {
  // =====================================================
  // STATE & HOOKS
  // =====================================================
  
  const {
    searchQuery,
    filters,
    isFilterModalOpen,
    categories,
    setSearchQuery,
    updateFilters,
    resetFilters,
    toggleFilterModal
  } = useMenuStore();
  
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [priceRange, setPriceRange] = useState({
    min: filters.priceRange.min,
    max: filters.priceRange.max
  });
  
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  
  // =====================================================
  // EFFECTS
  // =====================================================
  
  // Debounced search
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    searchTimeoutRef.current = setTimeout(() => {
      setSearchQuery(localSearchQuery);
    }, 300);
    
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [localSearchQuery, setSearchQuery]);
  
  // Focus search input when component mounts (for better UX)
  useEffect(() => {
    if (searchInputRef.current && !searchQuery) {
      // Only auto-focus if no existing search query
      // searchInputRef.current.focus();
    }
  }, []);
  
  // =====================================================
  // COMPUTED VALUES
  // =====================================================
  
  const hasActiveFilters = 
    filters.categories.length > 0 ||
    filters.dietary.vegetarian ||
    filters.dietary.vegan ||
    filters.dietary.glutenFree ||
    filters.excludeAllergens.length > 0 ||
    filters.priceRange.min > 0 ||
    filters.priceRange.max < 1000 ||
    filters.tags.length > 0 ||
    !filters.availableOnly;
  
  const activeFilterCount = 
    filters.categories.length +
    Object.values(filters.dietary).filter(Boolean).length +
    filters.excludeAllergens.length +
    filters.tags.length +
    (filters.priceRange.min > 0 ? 1 : 0) +
    (filters.priceRange.max < 1000 ? 1 : 0) +
    (!filters.availableOnly ? 1 : 0);
  
  // =====================================================
  // EVENT HANDLERS
  // =====================================================
  
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearchQuery(e.target.value);
  };
  
  const handleSearchClear = () => {
    setLocalSearchQuery('');
    setSearchQuery('');
    searchInputRef.current?.focus();
  };
  
  const handleCategoryToggle = (category: string) => {
    const newCategories = filters.categories.includes(category)
      ? filters.categories.filter(c => c !== category)
      : [...filters.categories, category];
    
    updateFilters({ categories: newCategories });
  };
  
  const handleDietaryToggle = (dietary: keyof typeof filters.dietary) => {
    updateFilters({
      dietary: {
        ...filters.dietary,
        [dietary]: !filters.dietary[dietary]
      }
    });
  };
  
  const handleAllergenToggle = (allergen: string) => {
    const newAllergens = filters.excludeAllergens.includes(allergen)
      ? filters.excludeAllergens.filter(a => a !== allergen)
      : [...filters.excludeAllergens, allergen];
    
    updateFilters({ excludeAllergens: newAllergens });
  };
  
  const handlePriceRangeChange = (type: 'min' | 'max', value: number) => {
    const newRange = { ...priceRange, [type]: value };
    setPriceRange(newRange);
    
    // Debounced update
    clearTimeout(searchTimeoutRef.current);
    searchTimeoutRef.current = setTimeout(() => {
      updateFilters({ priceRange: newRange });
    }, 500);
  };
  
  const handleResetFilters = () => {
    resetFilters();
    setLocalSearchQuery('');
    setPriceRange({ min: 0, max: 1000 });
  };
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderQuickFilters = () => (
    <div className="flex items-center space-x-2 overflow-x-auto pb-2 scrollbar-hide">
      {/* Vegetarian Filter */}
      <button
        onClick={() => handleDietaryToggle('vegetarian')}
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-all
          ${filters.dietary.vegetarian
            ? 'bg-green-100 text-green-700 border border-green-200'
            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }
        `}
      >
        <Leaf className="w-4 h-4" />
        <span>Vegetarian</span>
        {filters.dietary.vegetarian && <Check className="w-4 h-4" />}
      </button>
      
      {/* Vegan Filter */}
      <button
        onClick={() => handleDietaryToggle('vegan')}
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-all
          ${filters.dietary.vegan
            ? 'bg-green-100 text-green-700 border border-green-200'
            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }
        `}
      >
        <Leaf className="w-4 h-4" />
        <span>Vegan</span>
        {filters.dietary.vegan && <Check className="w-4 h-4" />}
      </button>
      
      {/* Quick Price Filters */}
      <button
        onClick={() => {
          const newRange = { min: 0, max: 15 };
          setPriceRange(newRange);
          updateFilters({ priceRange: newRange });
        }}
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-all
          ${filters.priceRange.max <= 15
            ? 'bg-blue-100 text-blue-700 border border-blue-200'
            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }
        `}
      >
        <DollarSign className="w-4 h-4" />
        <span>Under $15</span>
      </button>
      
      {/* Fast Food Filter */}
      <button
        onClick={() => {
          const newTags = filters.tags.includes('quick') 
            ? filters.tags.filter(t => t !== 'quick')
            : [...filters.tags, 'quick'];
          updateFilters({ tags: newTags });
        }}
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-all
          ${filters.tags.includes('quick')
            ? 'bg-orange-100 text-orange-700 border border-orange-200'
            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }
        `}
      >
        <Clock className="w-4 h-4" />
        <span>Quick</span>
        {filters.tags.includes('quick') && <Check className="w-4 h-4" />}
      </button>
    </div>
  );
  
  const renderFilterModal = () => {
    if (!isFilterModalOpen) return null;
    
    return (
      <div className="fixed inset-0 z-50 bg-black/50 flex items-end sm:items-center justify-center p-0 sm:p-4">
        <div className="bg-white w-full max-w-lg max-h-[90vh] overflow-y-auto rounded-t-2xl sm:rounded-2xl">
          {/* Header */}
          <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
            <div className="flex items-center space-x-3">
              {hasActiveFilters && (
                <button
                  onClick={handleResetFilters}
                  className="text-orange-500 hover:text-orange-600 text-sm font-medium"
                >
                  Reset
                </button>
              )}
              <button
                onClick={toggleFilterModal}
                className="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
          
          <div className="p-6 space-y-6">
            {/* Categories */}
            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-3">Categories</h3>
              <div className="grid grid-cols-2 gap-2">
                {categories.map(category => (
                  <button
                    key={category}
                    onClick={() => handleCategoryToggle(category)}
                    className={`
                      px-4 py-3 rounded-lg text-sm font-medium text-left transition-all
                      ${filters.categories.includes(category)
                        ? 'bg-orange-100 text-orange-700 border border-orange-200'
                        : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                      }
                    `}
                  >
                    <span className="capitalize">{category}</span>
                    {filters.categories.includes(category) && (
                      <Check className="w-4 h-4 inline ml-2" />
                    )}
                  </button>
                ))}
              </div>
            </div>
            
            {/* Dietary Preferences */}
            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-3">Dietary Preferences</h3>
              <div className="space-y-2">
                {[
                  { key: 'vegetarian' as const, label: 'Vegetarian', icon: '🥬' },
                  { key: 'vegan' as const, label: 'Vegan', icon: '🌱' },
                  { key: 'glutenFree' as const, label: 'Gluten Free', icon: '🌾' }
                ].map(({ key, label, icon }) => (
                  <label key={key} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.dietary[key]}
                      onChange={() => handleDietaryToggle(key)}
                      className="w-5 h-5 text-orange-500 border-gray-300 rounded focus:ring-orange-500"
                    />
                    <span className="text-xl">{icon}</span>
                    <span className="text-sm font-medium text-gray-700">{label}</span>
                  </label>
                ))}
              </div>
            </div>
            
            {/* Price Range */}
            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-3">Price Range</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="flex-1">
                    <label className="block text-xs text-gray-600 mb-1">Min Price</label>
                    <input
                      type="number"
                      value={priceRange.min}
                      onChange={(e) => handlePriceRangeChange('min', Number(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                      min="0"
                      step="0.5"
                    />
                  </div>
                  <div className="flex-1">
                    <label className="block text-xs text-gray-600 mb-1">Max Price</label>
                    <input
                      type="number"
                      value={priceRange.max}
                      onChange={(e) => handlePriceRangeChange('max', Number(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                      min="0"
                      step="0.5"
                    />
                  </div>
                </div>
                <div className="text-center text-sm text-gray-600">
                  ${priceRange.min.toFixed(2)} - ${priceRange.max.toFixed(2)}
                </div>
              </div>
            </div>
            
            {/* Exclude Allergens */}
            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-3">Exclude Allergens</h3>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { name: 'nuts', icon: '🥜' },
                  { name: 'dairy', icon: '🥛' },
                  { name: 'gluten', icon: '🌾' },
                  { name: 'eggs', icon: '🥚' },
                  { name: 'soy', icon: '🫘' },
                  { name: 'shellfish', icon: '🦐' }
                ].map(allergen => (
                  <button
                    key={allergen.name}
                    onClick={() => handleAllergenToggle(allergen.name)}
                    className={`
                      flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all
                      ${filters.excludeAllergens.includes(allergen.name)
                        ? 'bg-red-100 text-red-700 border border-red-200'
                        : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                      }
                    `}
                  >
                    <span>{allergen.icon}</span>
                    <span className="capitalize">{allergen.name}</span>
                    {filters.excludeAllergens.includes(allergen.name) && (
                      <X className="w-4 h-4" />
                    )}
                  </button>
                ))}
              </div>
            </div>
            
            {/* Availability */}
            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-3">Availability</h3>
              <label className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input
                  type="checkbox"
                  checked={filters.availableOnly}
                  onChange={(e) => updateFilters({ availableOnly: e.target.checked })}
                  className="w-5 h-5 text-orange-500 border-gray-300 rounded focus:ring-orange-500"
                />
                <span className="text-sm font-medium text-gray-700">Show only available items</span>
              </label>
            </div>
          </div>
          
          {/* Footer */}
          <div className="sticky bottom-0 bg-white border-t border-gray-200 px-6 py-4">
            <button
              onClick={toggleFilterModal}
              className="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 rounded-lg font-semibold transition-colors"
            >
              Apply Filters
              {activeFilterCount > 0 && (
                <span className="ml-2 bg-orange-400 text-orange-100 px-2 py-1 rounded-full text-xs">
                  {activeFilterCount}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  };
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  return (
    <div className={`bg-white ${className}`}>
      {/* Search Bar */}
      <div className="px-4 py-3 border-b border-gray-100">
        <div className="relative">
          <div className="flex items-center space-x-3">
            {/* Search Input */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search menu items..."
                value={localSearchQuery}
                onChange={handleSearchChange}
                className="w-full pl-10 pr-10 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 focus:bg-white transition-all text-sm"
              />
              {localSearchQuery && (
                <button
                  onClick={handleSearchClear}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 w-5 h-5"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
            
            {/* Filter Button */}
            <button
              onClick={toggleFilterModal}
              className={`
                relative flex items-center justify-center w-12 h-12 rounded-lg transition-all
                ${hasActiveFilters
                  ? 'bg-orange-100 text-orange-600 border border-orange-200'
                  : 'bg-gray-50 text-gray-600 hover:bg-gray-100 border border-gray-200'
                }
              `}
            >
              <Filter className="w-5 h-5" />
              {activeFilterCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-orange-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center font-bold">
                  {activeFilterCount > 9 ? '9+' : activeFilterCount}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>
      
      {/* Quick Filters */}
      <div className="px-4 py-3">
        {renderQuickFilters()}
      </div>
      
      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="px-4 pb-3">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">
              {activeFilterCount} filter{activeFilterCount !== 1 ? 's' : ''} applied
            </span>
            <button
              onClick={handleResetFilters}
              className="text-orange-500 hover:text-orange-600 font-medium"
            >
              Clear all
            </button>
          </div>
        </div>
      )}
      
      {/* Filter Modal */}
      {renderFilterModal()}
    </div>
  );
};
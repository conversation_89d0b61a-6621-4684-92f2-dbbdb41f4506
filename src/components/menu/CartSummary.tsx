// BHEEMDINE Cart Summary Component
// Fixed bottom cart summary with order management functionality

'use client';

import React, { useState } from 'react';
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  Trash2, 
  Clock, 
  ChevronUp,
  ChevronDown,
  CreditCard,
  MapPin
} from 'lucide-react';
import { useMenuStore, useCartItems, useCartTotal, useCartCount } from '@/stores/menuStore';

interface CartSummaryProps {
  className?: string;
  onCheckout?: () => void;
}

export const CartSummary: React.FC<CartSummaryProps> = ({
  className = '',
  onCheckout
}) => {
  // =====================================================
  // STATE & HOOKS
  // =====================================================
  
  const {
    updateCartItemQuantity,
    removeFromCart,
    clearCart,
    getEstimatedWaitTime,
    getCartSubtotal
  } = useMenuStore();
  
  const cartItems = useCartItems();
  const cartTotal = useCartTotal();
  const cartCount = useCartCount();
  
  const [isExpanded, setIsExpanded] = useState(false);
  const [isProcessingCheckout, setIsProcessingCheckout] = useState(false);
  
  // =====================================================
  // COMPUTED VALUES
  // =====================================================
  
  const estimatedWaitTime = getEstimatedWaitTime();
  const subtotal = getCartSubtotal();
  const tax = subtotal * 0.08; // 8% tax rate
  const total = subtotal + tax;
  
  // Check if cart has items
  if (cartCount === 0) {
    return null;
  }
  
  // =====================================================
  // EVENT HANDLERS
  // =====================================================
  
  const handleQuantityChange = (cartItemId: string, change: number) => {
    const item = cartItems.find(item => item.id === cartItemId);
    if (!item) return;
    
    const newQuantity = item.quantity + change;
    updateCartItemQuantity(cartItemId, newQuantity);
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(30);
    }
  };
  
  const handleRemoveItem = (cartItemId: string) => {
    removeFromCart(cartItemId);
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  };
  
  const handleClearCart = () => {
    if (window.confirm('Are you sure you want to clear your cart?')) {
      clearCart();
    }
  };
  
  const handleCheckout = async () => {
    if (isProcessingCheckout) return;
    
    setIsProcessingCheckout(true);
    
    try {
      // In a real app, this would navigate to checkout or call checkout API
      if (onCheckout) {
        await onCheckout();
      } else {
        // Default checkout behavior
        console.log('Proceeding to checkout...');
        
        // Simulate processing
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Navigate to checkout page
        window.location.href = '/checkout';
      }
      
    } catch (error) {
      console.error('Checkout error:', error);
      alert('Failed to proceed to checkout. Please try again.');
    } finally {
      setIsProcessingCheckout(false);
    }
  };
  
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(30);
    }
  };
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderCartItem = (item: typeof cartItems[0]) => (
    <div key={item.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
      {/* Item Info */}
      <div className="flex-1 min-w-0">
        <h4 className="font-medium text-gray-900 text-sm truncate">
          {item.menuItem.name}
        </h4>
        <p className="text-gray-600 text-xs">
          ${item.unitPrice.toFixed(2)} each
        </p>
        {item.customizations?.notes && (
          <p className="text-gray-500 text-xs mt-1 italic">
            Note: {item.customizations.notes}
          </p>
        )}
      </div>
      
      {/* Quantity Controls */}
      <div className="flex items-center space-x-2">
        <button
          onClick={() => handleQuantityChange(item.id, -1)}
          className="w-7 h-7 flex items-center justify-center rounded-full bg-white border border-gray-200 text-gray-600 hover:text-orange-500 hover:border-orange-200 transition-all active:scale-90"
        >
          <Minus className="w-3 h-3" />
        </button>
        
        <span className="text-sm font-semibold text-gray-900 min-w-[20px] text-center">
          {item.quantity}
        </span>
        
        <button
          onClick={() => handleQuantityChange(item.id, 1)}
          className="w-7 h-7 flex items-center justify-center rounded-full bg-white border border-gray-200 text-gray-600 hover:text-orange-500 hover:border-orange-200 transition-all active:scale-90"
        >
          <Plus className="w-3 h-3" />
        </button>
        
        <button
          onClick={() => handleRemoveItem(item.id)}
          className="w-7 h-7 flex items-center justify-center rounded-full bg-white border border-gray-200 text-red-500 hover:text-red-600 hover:border-red-200 transition-all active:scale-90 ml-1"
        >
          <Trash2 className="w-3 h-3" />
        </button>
      </div>
      
      {/* Item Total */}
      <div className="text-right">
        <p className="font-semibold text-gray-900 text-sm">
          ${item.totalPrice.toFixed(2)}
        </p>
      </div>
    </div>
  );
  
  const renderExpandedCart = () => {
    if (!isExpanded) return null;
    
    return (
      <div className="bg-white border-t border-gray-200 max-h-80 overflow-y-auto">
        {/* Cart Items */}
        <div className="p-4 space-y-3">
          {cartItems.map(renderCartItem)}
        </div>
        
        {/* Order Summary */}
        <div className="px-4 py-3 bg-gray-50 border-t border-gray-200">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Subtotal</span>
              <span className="text-gray-900">${subtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Tax</span>
              <span className="text-gray-900">${tax.toFixed(2)}</span>
            </div>
            <div className="flex justify-between font-semibold text-base border-t border-gray-200 pt-2">
              <span className="text-gray-900">Total</span>
              <span className="text-gray-900">${total.toFixed(2)}</span>
            </div>
          </div>
          
          {/* Estimated Wait Time */}
          {estimatedWaitTime > 0 && (
            <div className="flex items-center justify-center mt-3 text-sm text-gray-600">
              <Clock className="w-4 h-4 mr-1" />
              <span>Estimated wait: {estimatedWaitTime} minutes</span>
            </div>
          )}
          
          {/* Clear Cart Button */}
          <button
            onClick={handleClearCart}
            className="w-full mt-3 py-2 text-red-500 hover:text-red-600 text-sm font-medium transition-colors"
          >
            Clear Cart
          </button>
        </div>
      </div>
    );
  };
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  return (
    <div className={`fixed bottom-0 left-0 right-0 z-40 bg-white shadow-2xl border-t border-gray-200 ${className}`}>
      {/* Expanded Cart */}
      {renderExpandedCart()}
      
      {/* Cart Summary Bar */}
      <div className="px-4 py-3">
        <div className="flex items-center space-x-3">
          {/* Expand/Collapse Button */}
          <button
            onClick={toggleExpanded}
            className="flex items-center space-x-2 flex-1 text-left"
          >
            <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white relative">
              <ShoppingCart className="w-5 h-5" />
              <span className="absolute -top-1 -right-1 bg-orange-600 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center font-bold">
                {cartCount > 99 ? '99+' : cartCount}
              </span>
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-semibold text-gray-900">
                    {cartCount} item{cartCount !== 1 ? 's' : ''}
                  </p>
                  <p className="text-sm text-gray-600">
                    ${cartTotal.toFixed(2)} total
                  </p>
                </div>
                
                <div className="text-gray-400">
                  {isExpanded ? (
                    <ChevronDown className="w-5 h-5" />
                  ) : (
                    <ChevronUp className="w-5 h-5" />
                  )}
                </div>
              </div>
            </div>
          </button>
          
          {/* Checkout Button */}
          <button
            onClick={handleCheckout}
            disabled={isProcessingCheckout}
            className={`
              px-6 py-3 rounded-lg font-semibold text-white transition-all flex items-center space-x-2
              ${isProcessingCheckout
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-orange-500 hover:bg-orange-600 active:scale-95 shadow-lg hover:shadow-xl'
              }
            `}
          >
            {isProcessingCheckout ? (
              <>
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>Processing...</span>
              </>
            ) : (
              <>
                <CreditCard className="w-5 h-5" />
                <span>Checkout</span>
              </>
            )}
          </button>
        </div>
        
        {/* Quick Info Bar */}
        {!isExpanded && (
          <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
            <div className="flex items-center space-x-4">
              {estimatedWaitTime > 0 && (
                <span className="flex items-center">
                  <Clock className="w-3 h-3 mr-1" />
                  {estimatedWaitTime}min
                </span>
              )}
              <span className="flex items-center">
                <MapPin className="w-3 h-3 mr-1" />
                Table service
              </span>
            </div>
            
            <span>
              Tap to {isExpanded ? 'hide' : 'view'} details
            </span>
          </div>
        )}
      </div>
      
      {/* Safe Area Bottom Padding */}
      <div className="h-safe-area-inset-bottom bg-white" />
    </div>
  );
};
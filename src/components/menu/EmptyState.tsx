// BHEEMDINE Empty State Component
// Displays when no menu items match the current filters

'use client';

import React from 'react';
import { Search, Filter, RefreshCw } from 'lucide-react';

interface EmptyStateProps {
  searchQuery?: string;
  selectedCategory?: string | null;
  onReset: () => void;
  className?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  searchQuery,
  selectedCategory,
  onReset,
  className = ''
}) => {
  // =====================================================
  // COMPUTED VALUES
  // =====================================================
  
  const hasFilters = searchQuery || selectedCategory;
  
  // Determine the primary reason for empty state
  const getEmptyStateContent = () => {
    if (searchQuery && selectedCategory) {
      return {
        icon: '🔍',
        title: 'No matches found',
        description: `No items found for "${searchQuery}" in ${selectedCategory}`,
        suggestion: 'Try adjusting your search or browse other categories'
      };
    }
    
    if (searchQuery) {
      return {
        icon: '🔍',
        title: 'No search results',
        description: `No items found for "${searchQuery}"`,
        suggestion: 'Try different keywords or browse our menu categories'
      };
    }
    
    if (selectedCategory) {
      return {
        icon: '🍽️',
        title: 'Category empty',
        description: `No items available in ${selectedCategory} right now`,
        suggestion: 'Check back later or explore other categories'
      };
    }
    
    return {
      icon: '😕',
      title: 'No items available',
      description: 'No menu items are currently available',
      suggestion: 'Please check back later or contact restaurant staff'
    };
  };
  
  const content = getEmptyStateContent();
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderSuggestions = () => {
    const suggestions = [];
    
    if (searchQuery) {
      suggestions.push({
        text: 'Clear search',
        action: () => onReset(),
        icon: <Search className="w-4 h-4" />
      });
    }
    
    if (selectedCategory) {
      suggestions.push({
        text: 'View all categories',
        action: () => onReset(),
        icon: <Filter className="w-4 h-4" />
      });
    }
    
    if (hasFilters) {
      suggestions.push({
        text: 'Reset all filters',
        action: () => onReset(),
        icon: <RefreshCw className="w-4 h-4" />
      });
    }
    
    return suggestions;
  };
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  return (
    <div className={`flex flex-col items-center justify-center py-16 px-4 text-center ${className}`}>
      {/* Icon */}
      <div className="text-6xl mb-4">
        {content.icon}
      </div>
      
      {/* Title */}
      <h3 className="text-xl font-semibold text-gray-900 mb-2">
        {content.title}
      </h3>
      
      {/* Description */}
      <p className="text-gray-600 mb-6 max-w-sm">
        {content.description}
      </p>
      
      {/* Suggestion */}
      <p className="text-sm text-gray-500 mb-8">
        {content.suggestion}
      </p>
      
      {/* Action Buttons */}
      {hasFilters && (
        <div className="flex flex-col sm:flex-row gap-3 w-full max-w-xs">
          {renderSuggestions().map((suggestion, index) => (
            <button
              key={index}
              onClick={suggestion.action}
              className="flex items-center justify-center space-x-2 px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg font-medium transition-colors"
            >
              {suggestion.icon}
              <span>{suggestion.text}</span>
            </button>
          ))}
        </div>
      )}
      
      {/* Alternative Actions */}
      {!hasFilters && (
        <div className="space-y-3">
          <button
            onClick={() => window.location.reload()}
            className="flex items-center space-x-2 px-6 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-lg font-medium transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Refresh Menu</span>
          </button>
          
          <p className="text-xs text-gray-400">
            Having trouble? Contact restaurant staff for assistance
          </p>
        </div>
      )}
      
      {/* Quick Category Suggestions */}
      {searchQuery && !selectedCategory && (
        <div className="mt-8 w-full max-w-md">
          <p className="text-sm text-gray-600 mb-3">Try browsing these popular categories:</p>
          <div className="flex flex-wrap justify-center gap-2">
            {['appetizers', 'mains', 'beverages', 'desserts'].map(category => (
              <button
                key={category}
                onClick={() => {
                  // This would trigger category selection
                  onReset();
                  // In real implementation, you'd call setSelectedCategory(category)
                }}
                className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full text-sm font-medium transition-colors capitalize"
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
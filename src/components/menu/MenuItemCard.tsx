// BHEEMDINE Menu Item Card - Interactive card component for menu items
// Optimized for mobile touch interactions with visual feedback

'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { 
  Plus, 
  Minus, 
  Heart, 
  Clock, 
  AlertCircle, 
  Leaf, 
  Zap,
  Star,
  Info
} from 'lucide-react';
import { useMenuStore } from '@/stores/menuStore';
import { MenuItem } from '@/stores/menuStore';

interface MenuItemCardProps {
  item: MenuItem;
  onClick?: () => void;
  className?: string;
  showQuickAdd?: boolean;
}

export const MenuItemCard: React.FC<MenuItemCardProps> = ({
  item,
  onClick,
  className = '',
  showQuickAdd = true
}) => {
  // =====================================================
  // STATE & HOOKS
  // =====================================================
  
  const { 
    addToCart, 
    getCartItemByMenuId, 
    updateCartItemQuantity, 
    toggleFavorite,
    userPreferences 
  } = useMenuStore();
  
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  
  // Get current cart item for this menu item
  const cartItem = getCartItemByMenuId(item.id);
  const cartQuantity = cartItem?.quantity || 0;
  
  // Check if item is favorited
  const isFavorited = userPreferences.favoriteItems.includes(item.id);
  
  // =====================================================
  // COMPUTED VALUES
  // =====================================================
  
  const hasDiscount = item.pricing.discountPercentage && item.pricing.discountPercentage > 0;
  const isUnavailable = !item.availability.isAvailable;
  const isLowStock = item.availability.remainingQuantity && item.availability.remainingQuantity <= 5;
  const hasLongWait = item.availability.estimatedWaitTime && item.availability.estimatedWaitTime > 30;
  
  // Truncate description for card display
  const maxDescriptionLength = 80;
  const shouldTruncateDescription = item.description && item.description.length > maxDescriptionLength;
  const displayDescription = shouldTruncateDescription && !showFullDescription
    ? `${item.description.slice(0, maxDescriptionLength)}...`
    : item.description;
  
  // =====================================================
  // EVENT HANDLERS
  // =====================================================
  
  const handleAddToCart = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
    
    if (isUnavailable) return;
    
    setIsAddingToCart(true);
    
    try {
      addToCart(item, 1);
      
      // Haptic feedback for mobile devices
      if ('vibrate' in navigator) {
        navigator.vibrate(50);
      }
      
      // Brief visual feedback
      await new Promise(resolve => setTimeout(resolve, 200));
    } finally {
      setIsAddingToCart(false);
    }
  };
  
  const handleQuantityChange = (e: React.MouseEvent, change: number) => {
    e.stopPropagation();
    
    if (!cartItem) return;
    
    const newQuantity = cartQuantity + change;
    
    if (newQuantity <= 0) {
      updateCartItemQuantity(cartItem.id, 0); // This will remove the item
    } else {
      updateCartItemQuantity(cartItem.id, newQuantity);
    }
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(30);
    }
  };
  
  const handleFavoriteToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggleFavorite(item.id);
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  };
  
  const handleCardClick = () => {
    if (onClick && !isUnavailable) {
      onClick();
    }
  };
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderPriceDisplay = () => (
    <div className="flex items-center space-x-2">
      {hasDiscount ? (
        <>
          <span className="text-lg font-bold text-orange-500">
            ${item.pricing.currentPrice.toFixed(2)}
          </span>
          <span className="text-sm text-gray-400 line-through">
            ${item.pricing.basePrice.toFixed(2)}
          </span>
          <span className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded-full font-medium">
            {item.pricing.discountPercentage}% OFF
          </span>
        </>
      ) : (
        <span className="text-lg font-bold text-gray-900">
          ${item.pricing.currentPrice.toFixed(2)}
        </span>
      )}
    </div>
  );
  
  const renderTags = () => {
    const visibleTags = item.tags.slice(0, 3); // Show max 3 tags
    
    return (
      <div className="flex items-center space-x-1 mt-2">
        {item.isVegetarian && (
          <span className="inline-flex items-center text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
            <Leaf className="w-3 h-3 mr-1" />
            Veg
          </span>
        )}
        
        {item.isVegan && (
          <span className="inline-flex items-center text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
            <Leaf className="w-3 h-3 mr-1" />
            Vegan
          </span>
        )}
        
        {visibleTags.includes('spicy') && (
          <span className="inline-flex items-center text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">
            🌶️ Spicy
          </span>
        )}
        
        {visibleTags.includes('popular') && (
          <span className="inline-flex items-center text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded-full">
            <Star className="w-3 h-3 mr-1" />
            Popular
          </span>
        )}
        
        {visibleTags.includes('chef-special') && (
          <span className="inline-flex items-center text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
            ⭐ Chef's Special
          </span>
        )}
      </div>
    );
  };
  
  const renderAvailabilityInfo = () => {
    if (isUnavailable) {
      return (
        <div className="flex items-center text-red-500 text-sm mt-2">
          <AlertCircle className="w-4 h-4 mr-1" />
          {item.availability.unavailableReason || 'Currently unavailable'}
        </div>
      );
    }
    
    return (
      <div className="flex items-center justify-between text-sm text-gray-500 mt-2">
        <div className="flex items-center space-x-3">
          {/* Preparation Time */}
          <span className="flex items-center">
            <Clock className="w-4 h-4 mr-1" />
            {item.preparationTime}min
          </span>
          
          {/* Wait Time Warning */}
          {hasLongWait && (
            <span className="flex items-center text-orange-500">
              <Zap className="w-4 h-4 mr-1" />
              {item.availability.estimatedWaitTime}min wait
            </span>
          )}
        </div>
        
        {/* Stock Status */}
        {isLowStock && (
          <span className="text-orange-500 text-xs">
            Only {item.availability.remainingQuantity} left
          </span>
        )}
      </div>
    );
  };
  
  const renderAllergenInfo = () => {
    if (item.allergens.length === 0) return null;
    
    const majorAllergens = item.allergens.filter(a => a.severity === 'CONTAINS');
    
    if (majorAllergens.length === 0) return null;
    
    return (
      <div className="flex items-center mt-2">
        <AlertCircle className="w-4 h-4 text-orange-500 mr-1" />
        <span className="text-xs text-gray-600">
          Contains: {majorAllergens.map(a => a.name).join(', ')}
        </span>
      </div>
    );
  };
  
  const renderQuantityControls = () => {
    if (!showQuickAdd || isUnavailable) return null;
    
    if (cartQuantity === 0) {
      return (
        <button
          onClick={handleAddToCart}
          disabled={isAddingToCart}
          className={`
            flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-200
            ${isAddingToCart 
              ? 'bg-gray-200 text-gray-400 cursor-not-allowed' 
              : 'bg-orange-500 hover:bg-orange-600 text-white shadow-md hover:shadow-lg active:scale-95'
            }
          `}
        >
          {isAddingToCart ? (
            <div className="w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
          ) : (
            <>
              <Plus className="w-4 h-4 mr-1" />
              Add
            </>
          )}
        </button>
      );
    }
    
    return (
      <div className="flex items-center space-x-3 bg-orange-50 rounded-lg p-1">
        <button
          onClick={(e) => handleQuantityChange(e, -1)}
          className="w-8 h-8 flex items-center justify-center rounded-md bg-white shadow-sm text-orange-500 hover:text-orange-600 active:scale-90 transition-all"
        >
          <Minus className="w-4 h-4" />
        </button>
        
        <span className="text-orange-600 font-semibold min-w-[24px] text-center">
          {cartQuantity}
        </span>
        
        <button
          onClick={(e) => handleQuantityChange(e, 1)}
          className="w-8 h-8 flex items-center justify-center rounded-md bg-white shadow-sm text-orange-500 hover:text-orange-600 active:scale-90 transition-all"
        >
          <Plus className="w-4 h-4" />
        </button>
      </div>
    );
  };
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  return (
    <div
      className={`
        relative bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden cursor-pointer
        transition-all duration-200 hover:shadow-md active:scale-[0.98]
        ${isUnavailable ? 'opacity-60' : ''}
        ${className}
      `}
      onClick={handleCardClick}
    >
      {/* Discount Badge */}
      {hasDiscount && (
        <div className="absolute top-3 left-3 z-10">
          <span className="bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full">
            {item.pricing.discountPercentage}% OFF
          </span>
        </div>
      )}
      
      {/* Favorite Button */}
      <button
        onClick={handleFavoriteToggle}
        className="absolute top-3 right-3 z-10 w-8 h-8 flex items-center justify-center bg-white/80 backdrop-blur-sm rounded-full shadow-sm hover:bg-white transition-all active:scale-90"
      >
        <Heart 
          className={`w-4 h-4 transition-colors ${
            isFavorited ? 'fill-red-500 text-red-500' : 'text-gray-400 hover:text-red-400'
          }`} 
        />
      </button>
      
      <div className="flex">
        {/* Image Section */}
        <div className="relative w-24 h-24 sm:w-28 sm:h-28 flex-shrink-0">
          {item.imageUrl ? (
            <Image
              src={item.imageUrl}
              alt={item.name}
              fill
              className="object-cover"
              sizes="(max-width: 640px) 96px, 112px"
            />
          ) : (
            <div className="w-full h-full bg-gray-100 flex items-center justify-center">
              <span className="text-gray-400 text-2xl">🍽️</span>
            </div>
          )}
          
          {/* Unavailable Overlay */}
          {isUnavailable && (
            <div className="absolute inset-0 bg-gray-900/50 flex items-center justify-center">
              <span className="text-white text-xs font-medium">Unavailable</span>
            </div>
          )}
        </div>
        
        {/* Content Section */}
        <div className="flex-1 p-4 min-w-0"> {/* min-w-0 for text truncation */}
          {/* Header */}
          <div className="flex items-start justify-between mb-2">
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-gray-900 text-sm sm:text-base truncate">
                {item.name}
              </h3>
              {renderPriceDisplay()}
            </div>
            
            {/* Info Button */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                onClick?.();
              }}
              className="ml-2 w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Info className="w-4 h-4" />
            </button>
          </div>
          
          {/* Description */}
          {item.description && (
            <div className="mb-2">
              <p className="text-sm text-gray-600 leading-relaxed">
                {displayDescription}
              </p>
              {shouldTruncateDescription && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowFullDescription(!showFullDescription);
                  }}
                  className="text-orange-500 hover:text-orange-600 text-xs font-medium mt-1"
                >
                  {showFullDescription ? 'Show less' : 'Read more'}
                </button>
              )}
            </div>
          )}
          
          {/* Tags */}
          {renderTags()}
          
          {/* Allergen Info */}
          {renderAllergenInfo()}
          
          {/* Availability Info */}
          {renderAvailabilityInfo()}
          
          {/* Quantity Controls */}
          <div className="flex items-center justify-between mt-3">
            <div className="flex-1" /> {/* Spacer */}
            {renderQuantityControls()}
          </div>
        </div>
      </div>
      
      {/* Applied Pricing Rules Indicator */}
      {item.pricing.appliedRules.length > 0 && (
        <div className="px-4 py-2 bg-orange-50 border-t border-orange-100">
          <p className="text-xs text-orange-700">
            💰 {item.pricing.appliedRules.join(', ')}
          </p>
        </div>
      )}
    </div>
  );
};
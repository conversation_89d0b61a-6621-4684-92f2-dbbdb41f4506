// BHEEMDINE Item Detail Modal - Detailed view with customization options
// Full-screen modal optimized for mobile with smooth animations

'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { 
  X, 
  Plus, 
  Minus, 
  Heart, 
  Clock, 
  Users, 
  AlertCircle,
  Star,
  Leaf,
  MessageSquare,
  Check,
  ChevronDown,
  ChevronUp,
  Info
} from 'lucide-react';
import { useMenuStore } from '@/stores/menuStore';

interface ItemDetailModalProps {
  itemId: string;
  onClose: () => void;
}

export const ItemDetailModal: React.FC<ItemDetailModalProps> = ({
  itemId,
  onClose
}) => {
  // =====================================================
  // STATE & HOOKS
  // =====================================================
  
  const {
    menuItems,
    addToCart,
    getCartItemByMenuId,
    updateCartItemQuantity,
    toggleFavorite,
    userPreferences
  } = useMenuStore();
  
  const [quantity, setQuantity] = useState(1);
  const [customizations, setCustomizations] = useState({
    notes: '',
    modifications: [] as string[]
  });
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [showNutritionInfo, setShowNutritionInfo] = useState(false);
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  
  // Find the menu item
  const item = menuItems.find(item => item.id === itemId);
  const cartItem = item ? getCartItemByMenuId(item.id) : null;
  const isFavorited = item ? userPreferences.favoriteItems.includes(item.id) : false;
  
  // =====================================================
  // EFFECTS
  // =====================================================
  
  // Prevent body scroll when modal is open
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);
  
  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };
    
    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);
  
  // =====================================================
  // EVENT HANDLERS
  // =====================================================
  
  const handleQuantityChange = (change: number) => {
    const newQuantity = Math.max(1, quantity + change);
    setQuantity(newQuantity);
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(30);
    }
  };
  
  const handleAddToCart = async () => {
    if (!item || isAddingToCart) return;
    
    setIsAddingToCart(true);
    
    try {
      addToCart(item, quantity, customizations);
      
      // Success haptic feedback
      if ('vibrate' in navigator) {
        navigator.vibrate([50, 50, 50]);
      }
      
      // Brief delay for visual feedback
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Close modal after adding
      onClose();
      
    } catch (error) {
      console.error('Failed to add to cart:', error);
    } finally {
      setIsAddingToCart(false);
    }
  };
  
  const handleUpdateCart = () => {
    if (!cartItem) return;
    updateCartItemQuantity(cartItem.id, quantity);
    onClose();
  };
  
  const handleModificationToggle = (modification: string) => {
    setCustomizations(prev => ({
      ...prev,
      modifications: prev.modifications.includes(modification)
        ? prev.modifications.filter(m => m !== modification)
        : [...prev.modifications, modification]
    }));
  };
  
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  if (!item) {
    return (
      <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg p-6 text-center">
          <p className="text-gray-600">Item not found</p>
          <button
            onClick={onClose}
            className="mt-4 px-4 py-2 bg-gray-500 text-white rounded-lg"
          >
            Close
          </button>
        </div>
      </div>
    );
  }
  
  const renderImageGallery = () => {
    // Mock multiple images - in real app, item would have multiple images
    const images = item.imageUrl ? [item.imageUrl] : [];
    
    if (images.length === 0) {
      return (
        <div className="h-64 bg-gray-100 flex items-center justify-center">
          <span className="text-6xl">🍽️</span>
        </div>
      );
    }
    
    return (
      <div className="relative h-64 bg-gray-100">
        <Image
          src={images[activeImageIndex]}
          alt={item.name}
          fill
          className="object-cover"
          priority
        />
        
        {/* Image indicators */}
        {images.length > 1 && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {images.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveImageIndex(index)}
                className={`w-2 h-2 rounded-full transition-all ${
                  index === activeImageIndex ? 'bg-white' : 'bg-white/50'
                }`}
              />
            ))}
          </div>
        )}
      </div>
    );
  };
  
  const renderPricing = () => {
    const hasDiscount = item.pricing.discountPercentage && item.pricing.discountPercentage > 0;
    
    return (
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {hasDiscount ? (
            <>
              <span className="text-2xl font-bold text-orange-500">
                ${item.pricing.currentPrice.toFixed(2)}
              </span>
              <span className="text-lg text-gray-400 line-through">
                ${item.pricing.basePrice.toFixed(2)}
              </span>
              <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded-full text-xs font-medium">
                {item.pricing.discountPercentage}% OFF
              </span>
            </>
          ) : (
            <span className="text-2xl font-bold text-gray-900">
              ${item.pricing.currentPrice.toFixed(2)}
            </span>
          )}
        </div>
        
        <button
          onClick={() => toggleFavorite(item.id)}
          className="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 transition-all active:scale-90"
        >
          <Heart 
            className={`w-5 h-5 transition-colors ${
              isFavorited ? 'fill-red-500 text-red-500' : 'text-gray-400 hover:text-red-400'
            }`} 
          />
        </button>
      </div>
    );
  };
  
  const renderTags = () => (
    <div className="flex flex-wrap gap-2">
      {item.isVegetarian && (
        <span className="inline-flex items-center text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
          <Leaf className="w-3 h-3 mr-1" />
          Vegetarian
        </span>
      )}
      
      {item.isVegan && (
        <span className="inline-flex items-center text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
          <Leaf className="w-3 h-3 mr-1" />
          Vegan
        </span>
      )}
      
      {item.tags.includes('spicy') && (
        <span className="inline-flex items-center text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">
          🌶️ Spicy
        </span>
      )}
      
      {item.tags.includes('popular') && (
        <span className="inline-flex items-center text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded-full">
          <Star className="w-3 h-3 mr-1" />
          Popular
        </span>
      )}
      
      {item.tags.includes('chef-special') && (
        <span className="inline-flex items-center text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
          ⭐ Chef's Special
        </span>
      )}
    </div>
  );
  
  const renderAllergenInfo = () => {
    if (item.allergens.length === 0) return null;
    
    return (
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div className="flex items-center mb-2">
          <AlertCircle className="w-5 h-5 text-orange-500 mr-2" />
          <h4 className="font-semibold text-gray-900">Allergen Information</h4>
        </div>
        
        <div className="space-y-2">
          {item.allergens.map(allergen => (
            <div key={allergen.id} className="flex items-center justify-between text-sm">
              <span className="flex items-center">
                {allergen.icon && <span className="mr-2">{allergen.icon}</span>}
                {allergen.name}
              </span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                allergen.severity === 'CONTAINS' 
                  ? 'bg-red-100 text-red-700'
                  : allergen.severity === 'MAY_CONTAIN'
                  ? 'bg-yellow-100 text-yellow-700'
                  : 'bg-gray-100 text-gray-700'
              }`}>
                {allergen.severity.replace('_', ' ')}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };
  
  const renderCustomizations = () => (
    <div className="space-y-4">
      <h4 className="font-semibold text-gray-900">Customizations</h4>
      
      {/* Common modifications */}
      <div>
        <h5 className="text-sm font-medium text-gray-700 mb-2">Modifications</h5>
        <div className="grid grid-cols-1 gap-2">
          {[
            'Extra sauce',
            'No onions',
            'Extra cheese',
            'Light seasoning',
            'On the side',
            'Extra spicy'
          ].map(modification => (
            <label key={modification} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer">
              <input
                type="checkbox"
                checked={customizations.modifications.includes(modification)}
                onChange={() => handleModificationToggle(modification)}
                className="w-4 h-4 text-orange-500 border-gray-300 rounded focus:ring-orange-500"
              />
              <span className="text-sm text-gray-700">{modification}</span>
            </label>
          ))}
        </div>
      </div>
      
      {/* Special notes */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Special Instructions
        </label>
        <textarea
          value={customizations.notes}
          onChange={(e) => setCustomizations(prev => ({ ...prev, notes: e.target.value }))}
          placeholder="Any special requests or dietary needs..."
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-sm"
          rows={3}
          maxLength={200}
        />
        <p className="text-xs text-gray-500 mt-1">
          {customizations.notes.length}/200 characters
        </p>
      </div>
    </div>
  );
  
  const renderNutritionInfo = () => (
    <div className="space-y-3">
      <button
        onClick={() => setShowNutritionInfo(!showNutritionInfo)}
        className="flex items-center justify-between w-full p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
      >
        <div className="flex items-center space-x-2">
          <Info className="w-5 h-5 text-gray-600" />
          <span className="font-medium text-gray-900">Nutrition Information</span>
        </div>
        {showNutritionInfo ? (
          <ChevronUp className="w-5 h-5 text-gray-600" />
        ) : (
          <ChevronDown className="w-5 h-5 text-gray-600" />
        )}
      </button>
      
      {showNutritionInfo && (
        <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
          {/* Mock nutrition data */}
          <div className="text-center">
            <p className="text-lg font-semibold text-gray-900">520</p>
            <p className="text-xs text-gray-600">Calories</p>
          </div>
          <div className="text-center">
            <p className="text-lg font-semibold text-gray-900">28g</p>
            <p className="text-xs text-gray-600">Protein</p>
          </div>
          <div className="text-center">
            <p className="text-lg font-semibold text-gray-900">35g</p>
            <p className="text-xs text-gray-600">Carbs</p>
          </div>
          <div className="text-center">
            <p className="text-lg font-semibold text-gray-900">22g</p>
            <p className="text-xs text-gray-600">Fat</p>
          </div>
        </div>
      )}
    </div>
  );
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  return (
    <div 
      className="fixed inset-0 z-50 bg-black/50 flex items-end sm:items-center justify-center"
      onClick={handleBackdropClick}
    >
      <div className="bg-white w-full max-w-lg max-h-[95vh] overflow-y-auto rounded-t-2xl sm:rounded-2xl transform transition-transform">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between z-10">
          <h2 className="text-lg font-semibold text-gray-900 truncate pr-4">
            {item.name}
          </h2>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 transition-all"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        {/* Content */}
        <div className="pb-6">
          {/* Image Gallery */}
          {renderImageGallery()}
          
          {/* Main Content */}
          <div className="px-6 py-4 space-y-6">
            {/* Pricing and Favorite */}
            {renderPricing()}
            
            {/* Description */}
            <div>
              <p className="text-gray-700 leading-relaxed">
                {showFullDescription || !item.description || item.description.length <= 150
                  ? item.description
                  : `${item.description.slice(0, 150)}...`
                }
              </p>
              {item.description && item.description.length > 150 && (
                <button
                  onClick={() => setShowFullDescription(!showFullDescription)}
                  className="text-orange-500 hover:text-orange-600 text-sm font-medium mt-2"
                >
                  {showFullDescription ? 'Show less' : 'Read more'}
                </button>
              )}
            </div>
            
            {/* Tags */}
            {renderTags()}
            
            {/* Item Info */}
            <div className="flex items-center space-x-6 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>{item.preparationTime} min</span>
              </div>
              
              {item.availability.estimatedWaitTime && (
                <div className="flex items-center space-x-1 text-orange-600">
                  <Users className="w-4 h-4" />
                  <span>{item.availability.estimatedWaitTime} min wait</span>
                </div>
              )}
              
              {item.availability.remainingQuantity && item.availability.remainingQuantity <= 5 && (
                <div className="text-orange-600">
                  Only {item.availability.remainingQuantity} left
                </div>
              )}
            </div>
            
            {/* Applied Pricing Rules */}
            {item.pricing.appliedRules.length > 0 && (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                <p className="text-sm text-orange-700">
                  💰 Active offers: {item.pricing.appliedRules.join(', ')}
                </p>
              </div>
            )}
            
            {/* Allergen Information */}
            {renderAllergenInfo()}
            
            {/* Nutrition Information */}
            {renderNutritionInfo()}
            
            {/* Customizations */}
            {renderCustomizations()}
          </div>
        </div>
        
        {/* Footer - Quantity and Add to Cart */}
        <div className="sticky bottom-0 bg-white border-t border-gray-200 px-6 py-4">
          <div className="flex items-center space-x-4">
            {/* Quantity Controls */}
            <div className="flex items-center space-x-3 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => handleQuantityChange(-1)}
                disabled={quantity <= 1}
                className="w-10 h-10 flex items-center justify-center rounded-md bg-white shadow-sm text-gray-600 hover:text-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all active:scale-90"
              >
                <Minus className="w-4 h-4" />
              </button>
              
              <span className="text-lg font-semibold text-gray-900 min-w-[32px] text-center">
                {quantity}
              </span>
              
              <button
                onClick={() => handleQuantityChange(1)}
                className="w-10 h-10 flex items-center justify-center rounded-md bg-white shadow-sm text-gray-600 hover:text-orange-500 transition-all active:scale-90"
              >
                <Plus className="w-4 h-4" />
              </button>
            </div>
            
            {/* Add to Cart / Update Cart Button */}
            <button
              onClick={cartItem ? handleUpdateCart : handleAddToCart}
              disabled={!item.availability.isAvailable || isAddingToCart}
              className={`
                flex-1 py-3 px-6 rounded-lg font-semibold transition-all flex items-center justify-center space-x-2
                ${!item.availability.isAvailable
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : isAddingToCart
                  ? 'bg-orange-400 text-white cursor-not-allowed'
                  : 'bg-orange-500 hover:bg-orange-600 text-white shadow-lg hover:shadow-xl active:scale-95'
                }
              `}
            >
              {isAddingToCart ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Adding...</span>
                </>
              ) : !item.availability.isAvailable ? (
                <span>Unavailable</span>
              ) : cartItem ? (
                <>
                  <Check className="w-5 h-5" />
                  <span>Update Cart • ${(item.pricing.currentPrice * quantity).toFixed(2)}</span>
                </>
              ) : (
                <>
                  <Plus className="w-5 h-5" />
                  <span>Add to Cart • ${(item.pricing.currentPrice * quantity).toFixed(2)}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
// Main order review and submission component for TapDine
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  ShoppingCart, 
  ArrowLeft, 
  ArrowRight, 
  CheckCircle, 
  AlertTriangle, 
  Clock, 
  CreditCard,
  Receipt,
  Truck,
  MapPin,
  Phone,
  Mail,
  User,
  RefreshCw,
  ExternalLink,
  Copy,
  Check
} from 'lucide-react';
import { useMenuStore, useCartItems, useCartTotal, useCartCount } from '@/stores/menuStore';
import { useOrderSubmission } from '@/hooks/useOrderSubmission';
import { OrderReviewItem } from './OrderReviewItem';
import { OrderFormStep } from './OrderFormStep';
import type { 
  OrderFormData, 
  OrderFormErrors, 
  OrderStep, 
  OrderProgress,
  OrderSubmissionResponse 
} from '@/types/order';

interface OrderReviewSubmissionProps {
  tenantId: string;
  roomId?: string;
  tenantName?: string;
  onClose?: () => void;
  onSuccess?: (response: OrderSubmissionResponse) => void;
  className?: string;
}

export const OrderReviewSubmission: React.FC<OrderReviewSubmissionProps> = ({
  tenantId,
  roomId,
  tenantName = 'Restaurant',
  onClose,
  onSuccess,
  className = '',
}) => {
  
  // =====================================================
  // STATE & HOOKS
  // =====================================================
  
  const cartItems = useCartItems();
  const cartTotal = useCartTotal();
  const cartCount = useCartCount();
  
  const { 
    updateCartItemQuantity, 
    removeFromCart, 
    getCartSubtotal, 
    getEstimatedWaitTime 
  } = useMenuStore();
  
  const [currentStep, setCurrentStep] = useState<OrderStep>('review');
  const [formData, setFormData] = useState<OrderFormData>({
    customerInfo: {
      name: '',
      email: '',
      phone: '',
    },
    deliveryInfo: {
      roomNumber: '',
      deliveryInstructions: '',
      preferredDeliveryTime: '',
      isContactless: false,
    },
    paymentInfo: {
      paymentMethod: 'CASH',
      paymentToken: '',
      billingName: '',
      tipAmount: 0,
    },
    orderNotes: '',
    agreeToTerms: false,
  });
  
  const [formErrors, setFormErrors] = useState<OrderFormErrors>({});
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [copiedOrderNumber, setCopiedOrderNumber] = useState(false);
  
  const {
    isSubmitting,
    submitError,
    submitOrder,
    validateOrder,
    clearError,
    lastSubmission,
  } = useOrderSubmission({
    tenantId,
    roomId,
    onSuccess: (response) => {
      setCurrentStep('confirmation');
      if (onSuccess) onSuccess(response);
    },
  });
  
  // =====================================================
  // COMPUTED VALUES
  // =====================================================
  
  const subtotal = getCartSubtotal();
  const taxAmount = subtotal * 0.08; // 8% tax
  const tipAmount = formData.paymentInfo.tipAmount || 0;
  const totalAmount = subtotal + taxAmount + tipAmount;
  const estimatedWaitTime = getEstimatedWaitTime();
  
  const orderProgress: OrderProgress = {
    currentStep,
    completedSteps: getCompletedSteps(),
    totalSteps: 5,
  };
  
  const isFormValid = Object.keys(formErrors).length === 0 && formData.agreeToTerms;
  
  // =====================================================
  // HELPER FUNCTIONS
  // =====================================================
  
  function getCompletedSteps(): OrderStep[] {
    const completed: OrderStep[] = [];
    
    if (cartItems.length > 0) completed.push('review');
    if (formData.customerInfo.name) completed.push('customer');
    if (formData.deliveryInfo.roomNumber) completed.push('delivery');
    if (formData.paymentInfo.paymentMethod) completed.push('payment');
    if (lastSubmission) completed.push('confirmation');
    
    return completed;
  }
  
  const validateCurrentForm = useCallback(() => {
    const errors = validateOrder(formData);
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  }, [formData, validateOrder]);
  
  // =====================================================
  // EVENT HANDLERS
  // =====================================================
  
  const handleFormDataChange = (newData: Partial<OrderFormData>) => {
    setFormData(prev => ({ ...prev, ...newData }));
    
    // Clear submit error when user makes changes
    if (submitError) {
      clearError();
    }
  };
  
  const handleStepChange = (step: OrderStep) => {
    // Validate current step before moving
    if (submitAttempted) {
      validateCurrentForm();
    }
    
    setCurrentStep(step);
  };
  
  const handleNext = () => {
    const steps: OrderStep[] = ['review', 'customer', 'delivery', 'payment', 'confirmation'];
    const currentIndex = steps.indexOf(currentStep);
    
    if (currentIndex < steps.length - 1) {
      const nextStep = steps[currentIndex + 1];
      
      // Validate before proceeding to payment
      if (currentStep === 'delivery') {
        const isValid = validateCurrentForm();
        if (!isValid && !submitAttempted) {
          setSubmitAttempted(true);
          return;
        }
      }
      
      setCurrentStep(nextStep);
    }
  };
  
  const handleBack = () => {
    const steps: OrderStep[] = ['review', 'customer', 'delivery', 'payment', 'confirmation'];
    const currentIndex = steps.indexOf(currentStep);
    
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1]);
    }
  };
  
  const handleSubmitOrder = async () => {
    setSubmitAttempted(true);
    
    // Final validation
    const isValid = validateCurrentForm();
    if (!isValid) {
      return;
    }
    
    // Submit order
    const response = await submitOrder(formData);
    
    if (response) {
      // Success is handled by the hook's onSuccess callback
      console.log('Order submitted successfully:', response);
    }
  };
  
  const handleCopyOrderNumber = async () => {
    if (!lastSubmission?.orderNumber) return;
    
    try {
      await navigator.clipboard.writeText(lastSubmission.orderNumber);
      setCopiedOrderNumber(true);
      setTimeout(() => setCopiedOrderNumber(false), 2000);
    } catch (error) {
      console.error('Failed to copy order number:', error);
    }
  };
  
  // =====================================================
  // EFFECTS
  // =====================================================
  
  // Redirect to review if cart is empty
  useEffect(() => {
    if (cartItems.length === 0 && currentStep !== 'confirmation') {
      setCurrentStep('review');
    }
  }, [cartItems.length, currentStep]);
  
  // Update form data with calculated total
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      orderTotal: totalAmount,
    }));
  }, [totalAmount]);
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderProgressBar = () => {
    const steps: Array<{ key: OrderStep; label: string; icon: React.ElementType }> = [
      { key: 'review', label: 'Review', icon: ShoppingCart },
      { key: 'customer', label: 'Customer', icon: User },
      { key: 'delivery', label: 'Delivery', icon: MapPin },
      { key: 'payment', label: 'Payment', icon: CreditCard },
      { key: 'confirmation', label: 'Done', icon: CheckCircle },
    ];
    
    return (
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isActive = step.key === currentStep;
            const isCompleted = orderProgress.completedSteps.includes(step.key);
            const isAccessible = index === 0 || orderProgress.completedSteps.includes(steps[index - 1].key);
            
            return (
              <div key={step.key} className="flex items-center">
                <button
                  onClick={() => isAccessible && handleStepChange(step.key)}
                  disabled={!isAccessible}
                  className={`
                    flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all
                    ${isActive 
                      ? 'bg-orange-100 text-orange-700 border border-orange-200' 
                      : isCompleted 
                        ? 'bg-green-100 text-green-700 hover:bg-green-200' 
                        : isAccessible
                          ? 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                          : 'text-gray-400 cursor-not-allowed'
                    }
                  `}
                >
                  <Icon className={`w-4 h-4 ${isCompleted ? 'text-green-600' : ''}`} />
                  <span className="hidden sm:inline">{step.label}</span>
                </button>
                
                {index < steps.length - 1 && (
                  <div className={`w-8 h-0.5 mx-2 ${isCompleted ? 'bg-green-300' : 'bg-gray-200'}`} />
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };
  
  const renderOrderSummary = () => (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
      <h3 className="font-semibold text-gray-900 mb-3">Order Summary</h3>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">Subtotal ({cartCount} items)</span>
          <span className="text-gray-900">${subtotal.toFixed(2)}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Tax (8%)</span>
          <span className="text-gray-900">${taxAmount.toFixed(2)}</span>
        </div>
        
        {tipAmount > 0 && (
          <div className="flex justify-between">
            <span className="text-gray-600">Tip</span>
            <span className="text-gray-900">${tipAmount.toFixed(2)}</span>
          </div>
        )}
        
        <div className="border-t border-gray-300 pt-2 mt-2">
          <div className="flex justify-between font-semibold text-base">
            <span className="text-gray-900">Total</span>
            <span className="text-gray-900">${totalAmount.toFixed(2)}</span>
          </div>
        </div>
        
        {estimatedWaitTime > 0 && (
          <div className="flex items-center justify-center mt-3 text-sm text-gray-600">
            <Clock className="w-4 h-4 mr-1" />
            <span>Estimated wait: {estimatedWaitTime} minutes</span>
          </div>
        )}
      </div>
    </div>
  );
  
  const renderReviewStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <ShoppingCart className="w-12 h-12 text-orange-500 mx-auto mb-3" />
        <h2 className="text-2xl font-bold text-gray-900">Review Your Order</h2>
        <p className="text-gray-600 mt-2">
          Check your items and quantities before proceeding to checkout
        </p>
      </div>
      
      {cartItems.length === 0 ? (
        <div className="text-center py-8">
          <ShoppingCart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 text-lg">Your cart is empty</p>
          <p className="text-gray-500 mt-2">Add some items from the menu to get started</p>
          {onClose && (
            <button
              onClick={onClose}
              className="mt-4 px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
            >
              Back to Menu
            </button>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {cartItems.map((item) => (
            <OrderReviewItem
              key={item.id}
              item={item}
              onUpdateQuantity={updateCartItemQuantity}
              onRemoveItem={removeFromCart}
              showEstimatedTime={true}
            />
          ))}
          
          {renderOrderSummary()}
          
          {/* Order Notes */}
          <div className="space-y-2">
            <label htmlFor="orderNotes" className="block text-sm font-medium text-gray-700">
              Special Instructions (Optional)
            </label>
            <textarea
              id="orderNotes"
              value={formData.orderNotes}
              onChange={(e) => handleFormDataChange({ orderNotes: e.target.value })}
              placeholder="Any special requests or dietary requirements?"
              rows={3}
              maxLength={1000}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
            {formData.orderNotes && (
              <div className="text-right text-xs text-gray-500">
                {formData.orderNotes.length}/1000
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
  
  const renderConfirmationStep = () => {
    if (!lastSubmission) return null;
    
    return (
      <div className="space-y-6 text-center">
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
          <CheckCircle className="w-12 h-12 text-green-600" />
        </div>
        
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Order Confirmed!</h2>
          <p className="text-gray-600">
            Thank you for your order. We'll start preparing it right away.
          </p>
        </div>
        
        {/* Order Number */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center justify-center space-x-2">
            <Receipt className="w-5 h-5 text-green-600" />
            <span className="text-green-800 font-medium">Order Number:</span>
            <span className="font-bold text-green-900 text-lg">{lastSubmission.orderNumber}</span>
            <button
              onClick={handleCopyOrderNumber}
              className="p-1 text-green-600 hover:text-green-700 rounded"
              title="Copy order number"
            >
              {copiedOrderNumber ? (
                <Check className="w-4 h-4" />
              ) : (
                <Copy className="w-4 h-4" />
              )}
            </button>
          </div>
        </div>
        
        {/* Order Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-2">Delivery Information</h3>
            <div className="space-y-1 text-sm text-gray-600">
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-2" />
                <span>Room {lastSubmission.roomNumber || formData.deliveryInfo.roomNumber}</span>
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2" />
                <span>Estimated: {lastSubmission.estimatedPreparationTime} minutes</span>
              </div>
              <div className="flex items-center">
                <Truck className="w-4 h-4 mr-2" />
                <span>Ready by: {new Date(lastSubmission.estimatedReadyTime).toLocaleTimeString()}</span>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-2">Order Total</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Subtotal:</span>
                <span className="text-gray-900">${lastSubmission.subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Tax:</span>
                <span className="text-gray-900">${lastSubmission.taxAmount.toFixed(2)}</span>
              </div>
              {lastSubmission.tipAmount && lastSubmission.tipAmount > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Tip:</span>
                  <span className="text-gray-900">${lastSubmission.tipAmount.toFixed(2)}</span>
                </div>
              )}
              <div className="border-t border-gray-300 pt-1 mt-1">
                <div className="flex justify-between font-semibold">
                  <span className="text-gray-900">Total:</span>
                  <span className="text-gray-900">${lastSubmission.totalAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Confirmation Message */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-blue-800 text-sm">{lastSubmission.confirmationMessage}</p>
        </div>
        
        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {lastSubmission.trackingUrl && (
            <a
              href={lastSubmission.trackingUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Track Order
            </a>
          )}
          
          {onClose && (
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              Close
            </button>
          )}
        </div>
      </div>
    );
  };
  
  const renderActionButtons = () => {
    if (currentStep === 'confirmation') return null;
    
    const canProceed = currentStep === 'review' ? cartItems.length > 0 : true;
    const isLastStep = currentStep === 'payment';
    
    return (
      <div className="flex items-center justify-between space-x-4">
        {/* Back Button */}
        <button
          onClick={currentStep === 'review' && onClose ? onClose : handleBack}
          className="flex items-center space-x-2 px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          disabled={isSubmitting}
        >
          <ArrowLeft className="w-4 h-4" />
          <span>{currentStep === 'review' ? 'Back to Menu' : 'Back'}</span>
        </button>
        
        {/* Next/Submit Button */}
        <button
          onClick={isLastStep ? handleSubmitOrder : handleNext}
          disabled={!canProceed || isSubmitting || (isLastStep && !isFormValid)}
          className={`
            flex items-center space-x-2 px-6 py-3 rounded-lg font-semibold transition-all
            ${!canProceed || isSubmitting || (isLastStep && !isFormValid)
              ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
              : 'bg-orange-500 text-white hover:bg-orange-600 active:scale-95'
            }
          `}
        >
          {isSubmitting ? (
            <>
              <RefreshCw className="w-4 h-4 animate-spin" />
              <span>Processing...</span>
            </>
          ) : isLastStep ? (
            <>
              <CheckCircle className="w-4 h-4" />
              <span>Place Order</span>
            </>
          ) : (
            <>
              <span>Continue</span>
              <ArrowRight className="w-4 h-4" />
            </>
          )}
        </button>
      </div>
    );
  };
  
  const renderTermsCheckbox = () => {
    if (currentStep !== 'payment') return null;
    
    return (
      <div className="flex items-start space-x-3 p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <input
          type="checkbox"
          id="agreeToTerms"
          checked={formData.agreeToTerms}
          onChange={(e) => handleFormDataChange({ agreeToTerms: e.target.checked })}
          className="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 mt-0.5"
        />
        <label htmlFor="agreeToTerms" className="text-sm text-gray-700">
          <span>I agree to the </span>
          <a href="/terms" target="_blank" className="text-orange-600 hover:text-orange-700 underline">
            Terms of Service
          </a>
          <span> and </span>
          <a href="/privacy" target="_blank" className="text-orange-600 hover:text-orange-700 underline">
            Privacy Policy
          </a>
          <span className="text-red-500 ml-1">*</span>
        </label>
        
        {formErrors.agreeToTerms && (
          <div className="mt-1">
            <p className="text-red-600 text-sm">{formErrors.agreeToTerms}</p>
          </div>
        )}
      </div>
    );
  };
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      {/* Progress Bar */}
      {renderProgressBar()}
      
      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {tenantName} - Order Checkout
          </h1>
          <p className="text-gray-600">Complete your order in just a few simple steps</p>
        </div>
        
        {/* Error Display */}
        {submitError && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <h3 className="font-medium text-red-800">{submitError.errorMessage}</h3>
                {submitError.details && (
                  <pre className="mt-2 text-sm text-red-700 bg-red-100 p-2 rounded">
                    {JSON.stringify(submitError.details, null, 2)}
                  </pre>
                )}
                {submitError.fieldErrors && Object.keys(submitError.fieldErrors).length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-red-700 font-medium">Field errors:</p>
                    <ul className="mt-1 text-sm text-red-600 list-disc list-inside">
                      {Object.entries(submitError.fieldErrors).map(([field, errors]) => (
                        <li key={field}>
                          <strong>{field}:</strong> {errors.join(', ')}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
              <button
                onClick={clearError}
                className="text-red-400 hover:text-red-600"
              >
                ×
              </button>
            </div>
          </div>
        )}
        
        {/* Step Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          {currentStep === 'review' && renderReviewStep()}
          {currentStep === 'confirmation' && renderConfirmationStep()}
          {(['customer', 'delivery', 'payment'] as const).includes(currentStep) && (
            <OrderFormStep
              step={currentStep}
              data={formData}
              errors={formErrors}
              onChange={handleFormDataChange}
              onValidate={validateCurrentForm}
            />
          )}
        </div>
        
        {/* Terms Checkbox */}
        {renderTermsCheckbox()}
        
        {/* Action Buttons */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          {renderActionButtons()}
        </div>
      </div>
      
      {/* Fixed Order Summary (Mobile) */}
      {currentStep !== 'review' && currentStep !== 'confirmation' && cartItems.length > 0 && (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 md:hidden">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">{cartCount} items</p>
              <p className="font-semibold text-gray-900">${totalAmount.toFixed(2)} total</p>
            </div>
            <button
              onClick={() => setCurrentStep('review')}
              className="text-orange-600 hover:text-orange-700 text-sm font-medium"
            >
              View Cart
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
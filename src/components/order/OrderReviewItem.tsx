// Individual order item component for the order review screen
'use client';

import React, { useState } from 'react';
import { 
  Plus, 
  Minus, 
  Trash2, 
  Edit3, 
  Clock,
  AlertCircle,
  ChefHat
} from 'lucide-react';
import type { CartItem } from '@/stores/menuStore';

interface OrderReviewItemProps {
  item: CartItem;
  onUpdateQuantity: (cartItemId: string, quantity: number) => void;
  onRemoveItem: (cartItemId: string) => void;
  onEditCustomizations?: (cartItemId: string) => void;
  isEditable?: boolean;
  showEstimatedTime?: boolean;
}

export const OrderReviewItem: React.FC<OrderReviewItemProps> = ({
  item,
  onUpdateQuantity,
  onRemoveItem,
  onEditCustomizations,
  isEditable = true,
  showEstimatedTime = true,
}) => {
  
  // =====================================================
  // STATE
  // =====================================================
  
  const [isUpdating, setIsUpdating] = useState(false);
  
  // =====================================================
  // EVENT HANDLERS
  // =====================================================
  
  const handleQuantityChange = async (change: number) => {
    if (!isEditable || isUpdating) return;
    
    const newQuantity = item.quantity + change;
    if (newQuantity < 0) return;
    
    setIsUpdating(true);
    
    try {
      onUpdateQuantity(item.id, newQuantity);
      
      // Haptic feedback
      if ('vibrate' in navigator) {
        navigator.vibrate(30);
      }
      
    } finally {
      // Brief delay to show visual feedback
      setTimeout(() => setIsUpdating(false), 200);
    }
  };
  
  const handleRemove = () => {
    if (!isEditable) return;
    
    // Confirm removal for items with customizations
    const hasCustomizations = item.customizations?.modifications?.length || item.customizations?.notes;
    
    if (hasCustomizations) {
      const confirmed = window.confirm(
        `Remove "${item.menuItem.name}" from your order? Any customizations will be lost.`
      );
      if (!confirmed) return;
    }
    
    onRemoveItem(item.id);
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  };
  
  const handleEditCustomizations = () => {
    if (onEditCustomizations) {
      onEditCustomizations(item.id);
    }
  };
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderItemDetails = () => (
    <div className="flex-1 min-w-0">
      {/* Item Name and Availability */}
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900 text-base">
            {item.menuItem.name}
          </h3>
          
          {/* Availability Badge */}
          {!item.menuItem.availability.isAvailable && (
            <div className="flex items-center mt-1">
              <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
              <span className="text-red-600 text-sm font-medium">
                Currently Unavailable
              </span>
            </div>
          )}
        </div>
        
        {/* Price */}
        <div className="text-right ml-3">
          <p className="font-bold text-gray-900 text-lg">
            ${item.totalPrice.toFixed(2)}
          </p>
          <p className="text-gray-600 text-sm">
            ${item.unitPrice.toFixed(2)} each
          </p>
        </div>
      </div>
      
      {/* Description */}
      {item.menuItem.description && (
        <p className="text-gray-600 text-sm mt-1 line-clamp-2">
          {item.menuItem.description}
        </p>
      )}
      
      {/* Customizations */}
      {renderCustomizations()}
      
      {/* Estimated Time */}
      {showEstimatedTime && item.menuItem.preparationTime > 0 && (
        <div className="flex items-center mt-2 text-sm text-gray-600">
          <Clock className="w-4 h-4 mr-1" />
          <span>{item.menuItem.preparationTime} min prep time</span>
        </div>
      )}
      
      {/* Dietary and Allergen Info */}
      {renderDietaryInfo()}
    </div>
  );
  
  const renderCustomizations = () => {
    const hasModifications = item.customizations?.modifications?.length;
    const hasNotes = item.customizations?.notes?.trim();
    
    if (!hasModifications && !hasNotes) return null;
    
    return (
      <div className="mt-2 p-2 bg-orange-50 rounded-lg border border-orange-100">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center mb-1">
              <ChefHat className="w-4 h-4 text-orange-600 mr-1" />
              <span className="text-orange-800 font-medium text-sm">Customizations</span>
            </div>
            
            {hasModifications && (
              <div className="mb-1">
                <p className="text-orange-700 text-sm">
                  <strong>Modifications:</strong> {item.customizations!.modifications!.join(', ')}
                </p>
              </div>
            )}
            
            {hasNotes && (
              <p className="text-orange-700 text-sm">
                <strong>Special Instructions:</strong> {item.customizations!.notes}
              </p>
            )}
          </div>
          
          {/* Edit Customizations Button */}
          {isEditable && onEditCustomizations && (
            <button
              onClick={handleEditCustomizations}
              className="ml-2 p-1 text-orange-600 hover:text-orange-700 hover:bg-orange-100 rounded transition-colors"
              title="Edit customizations"
            >
              <Edit3 className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>
    );
  };
  
  const renderDietaryInfo = () => {
    const badges = [];
    
    if (item.menuItem.isVegan) {
      badges.push(
        <span key="vegan" className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          Vegan
        </span>
      );
    } else if (item.menuItem.isVegetarian) {
      badges.push(
        <span key="vegetarian" className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          Vegetarian
        </span>
      );
    }
    
    if (item.menuItem.allergens.length > 0) {
      const allergenNames = item.menuItem.allergens.map(a => a.name).join(', ');
      badges.push(
        <span key="allergens" className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          Contains: {allergenNames}
        </span>
      );
    }
    
    if (badges.length === 0) return null;
    
    return (
      <div className="flex flex-wrap gap-1 mt-2">
        {badges}
      </div>
    );
  };
  
  const renderQuantityControls = () => {
    if (!isEditable) {
      return (
        <div className="flex items-center justify-center w-16 h-10 bg-gray-100 rounded-lg">
          <span className="font-semibold text-gray-700">{item.quantity}</span>
        </div>
      );
    }
    
    return (
      <div className="flex items-center space-x-2">
        {/* Decrease Quantity */}
        <button
          onClick={() => handleQuantityChange(-1)}
          disabled={isUpdating || item.quantity <= 1}
          className={`
            w-8 h-8 flex items-center justify-center rounded-full border transition-all
            ${isUpdating || item.quantity <= 1
              ? 'border-gray-200 text-gray-400 cursor-not-allowed'
              : 'border-gray-300 text-gray-600 hover:text-orange-600 hover:border-orange-300 active:scale-90'
            }
          `}
        >
          <Minus className="w-4 h-4" />
        </button>
        
        {/* Quantity Display */}
        <div className="flex items-center justify-center w-10 h-8">
          {isUpdating ? (
            <div className="w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full animate-spin" />
          ) : (
            <span className="font-bold text-gray-900">{item.quantity}</span>
          )}
        </div>
        
        {/* Increase Quantity */}
        <button
          onClick={() => handleQuantityChange(1)}
          disabled={isUpdating}
          className={`
            w-8 h-8 flex items-center justify-center rounded-full border transition-all
            ${isUpdating
              ? 'border-gray-200 text-gray-400 cursor-not-allowed'
              : 'border-gray-300 text-gray-600 hover:text-orange-600 hover:border-orange-300 active:scale-90'
            }
          `}
        >
          <Plus className="w-4 h-4" />
        </button>
      </div>
    );
  };
  
  const renderActionButtons = () => {
    if (!isEditable) return null;
    
    return (
      <div className="flex items-center space-x-2 mt-3">
        {/* Remove Button */}
        <button
          onClick={handleRemove}
          className="flex items-center space-x-1 px-3 py-1.5 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors text-sm"
        >
          <Trash2 className="w-4 h-4" />
          <span>Remove</span>
        </button>
        
        {/* Edit Customizations Button */}
        {onEditCustomizations && (
          <button
            onClick={handleEditCustomizations}
            className="flex items-center space-x-1 px-3 py-1.5 text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors text-sm"
          >
            <Edit3 className="w-4 h-4" />
            <span>Edit</span>
          </button>
        )}
      </div>
    );
  };
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  return (
    <div className={`
      p-4 bg-white rounded-lg border border-gray-200 transition-all
      ${!item.menuItem.availability.isAvailable ? 'border-red-200 bg-red-50' : ''}
      ${isUpdating ? 'scale-[0.98] opacity-70' : 'hover:shadow-md'}
    `}>
      {/* Main Content */}
      <div className="flex items-start space-x-4">
        {/* Item Image */}
        {item.menuItem.imageUrl && (
          <div className="flex-shrink-0">
            <img
              src={item.menuItem.imageUrl}
              alt={item.menuItem.name}
              className="w-16 h-16 object-cover rounded-lg border border-gray-200"
              loading="lazy"
            />
          </div>
        )}
        
        {/* Item Details */}
        {renderItemDetails()}
      </div>
      
      {/* Quantity Controls */}
      <div className="flex items-center justify-between mt-3">
        {renderQuantityControls()}
        
        {/* Action Buttons */}
        <div className="ml-auto">
          {renderActionButtons()}
        </div>
      </div>
      
      {/* Unavailable Warning */}
      {!item.menuItem.availability.isAvailable && (
        <div className="mt-3 p-2 bg-red-100 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">
            <strong>Note:</strong> This item is currently unavailable. 
            {item.menuItem.availability.unavailableReason && 
              ` ${item.menuItem.availability.unavailableReason}`
            }
          </p>
        </div>
      )}
    </div>
  );
};
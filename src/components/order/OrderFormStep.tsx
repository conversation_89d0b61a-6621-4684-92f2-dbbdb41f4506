// Individual form steps for the order submission process
'use client';

import React, { useState } from 'react';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  MessageSquare, 
  CreditCard, 
  Banknote, 
  Building,
  Smartphone,
  CheckCircle,
  AlertCircle,
  Eye,
  EyeOff
} from 'lucide-react';
import type { OrderFormData, OrderFormErrors } from '@/types/order';

interface OrderFormStepProps {
  step: 'customer' | 'delivery' | 'payment';
  data: OrderFormData;
  errors: OrderFormErrors;
  onChange: (data: Partial<OrderFormData>) => void;
  onValidate?: () => void;
}

export const OrderFormStep: React.FC<OrderFormStepProps> = ({
  step,
  data,
  errors,
  onChange,
  onValidate,
}) => {
  
  // =====================================================
  // STATE
  // =====================================================
  
  const [showPaymentDetails, setShowPaymentDetails] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  
  // =====================================================
  // EVENT HANDLERS
  // =====================================================
  
  const handleInputChange = (field: string, value: any) => {
    const keys = field.split('.');
    
    if (keys.length === 2) {
      const [section, subField] = keys;
      onChange({
        [section]: {
          ...data[section as keyof OrderFormData],
          [subField]: value,
        },
      });
    } else {
      onChange({ [field]: value });
    }
    
    // Clear field error when user starts typing
    if (onValidate) {
      setTimeout(onValidate, 100);
    }
  };
  
  const handleFocus = (field: string) => {
    setFocusedField(field);
  };
  
  const handleBlur = () => {
    setFocusedField(null);
    if (onValidate) {
      onValidate();
    }
  };
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderFormField = ({
    id,
    label,
    type = 'text',
    value,
    onChange: onFieldChange,
    placeholder,
    icon: Icon,
    error,
    required = false,
    disabled = false,
    options,
    rows,
    maxLength,
    pattern,
    autoComplete,
  }: {
    id: string;
    label: string;
    type?: string;
    value: any;
    onChange: (value: any) => void;
    placeholder?: string;
    icon?: React.ElementType;
    error?: string;
    required?: boolean;
    disabled?: boolean;
    options?: Array<{ value: string; label: string; description?: string }>;
    rows?: number;
    maxLength?: number;
    pattern?: string;
    autoComplete?: string;
  }) => {
    const isFocused = focusedField === id;
    const hasError = !!error;
    const hasValue = value && value.toString().trim().length > 0;
    
    const inputClasses = `
      w-full px-4 py-3 rounded-lg border transition-all
      ${hasError 
        ? 'border-red-300 focus:border-red-500 focus:ring-red-200' 
        : isFocused 
          ? 'border-orange-300 focus:border-orange-500 focus:ring-orange-200'
          : hasValue
            ? 'border-green-300 bg-green-50'
            : 'border-gray-300 focus:border-orange-300'
      }
      ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
      focus:outline-none focus:ring-2 focus:ring-opacity-50
      placeholder-gray-400
      ${Icon ? 'pl-12' : ''}
    `;
    
    const renderInput = () => {
      if (type === 'select' && options) {
        return (
          <select
            id={id}
            value={value || ''}
            onChange={(e) => onFieldChange(e.target.value)}
            onFocus={() => handleFocus(id)}
            onBlur={handleBlur}
            disabled={disabled}
            required={required}
            className={inputClasses}
          >
            <option value="">{placeholder || `Select ${label.toLowerCase()}`}</option>
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      }
      
      if (type === 'textarea') {
        return (
          <textarea
            id={id}
            value={value || ''}
            onChange={(e) => onFieldChange(e.target.value)}
            onFocus={() => handleFocus(id)}
            onBlur={handleBlur}
            placeholder={placeholder}
            disabled={disabled}
            required={required}
            rows={rows || 3}
            maxLength={maxLength}
            className={inputClasses}
          />
        );
      }
      
      return (
        <input
          id={id}
          type={type}
          value={value || ''}
          onChange={(e) => onFieldChange(e.target.value)}
          onFocus={() => handleFocus(id)}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          maxLength={maxLength}
          pattern={pattern}
          autoComplete={autoComplete}
          className={inputClasses}
        />
      );
    };
    
    return (
      <div className="space-y-2">
        <label htmlFor={id} className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
        
        <div className="relative">
          {Icon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10">
              <Icon className={`w-5 h-5 ${hasError ? 'text-red-400' : hasValue ? 'text-green-500' : 'text-gray-400'}`} />
            </div>
          )}
          
          {renderInput()}
          
          {hasValue && !hasError && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <CheckCircle className="w-5 h-5 text-green-500" />
            </div>
          )}
        </div>
        
        {/* Character Counter */}
        {maxLength && value && (
          <div className="text-right">
            <span className={`text-xs ${value.length > maxLength * 0.9 ? 'text-red-500' : 'text-gray-500'}`}>
              {value.length}/{maxLength}
            </span>
          </div>
        )}
        
        {/* Field Error */}
        {error && (
          <div className="flex items-center space-x-1 text-red-600 text-sm">
            <AlertCircle className="w-4 h-4" />
            <span>{error}</span>
          </div>
        )}
        
        {/* Options Description */}
        {type === 'select' && options && value && (
          <div className="text-sm text-gray-600">
            {options.find(opt => opt.value === value)?.description}
          </div>
        )}
      </div>
    );
  };
  
  // =====================================================
  // STEP RENDERS
  // =====================================================
  
  const renderCustomerStep = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <User className="w-12 h-12 text-orange-500 mx-auto mb-3" />
        <h2 className="text-2xl font-bold text-gray-900">Customer Information</h2>
        <p className="text-gray-600 mt-2">Please provide your contact details for order updates</p>
      </div>
      
      <div className="space-y-4">
        {renderFormField({
          id: 'customerInfo.name',
          label: 'Full Name',
          value: data.customerInfo.name,
          onChange: (value) => handleInputChange('customerInfo.name', value),
          placeholder: 'Enter your full name',
          icon: User,
          error: errors.customerInfo?.name,
          required: true,
          maxLength: 50,
          autoComplete: 'name',
        })}
        
        {renderFormField({
          id: 'customerInfo.email',
          label: 'Email Address',
          type: 'email',
          value: data.customerInfo.email,
          onChange: (value) => handleInputChange('customerInfo.email', value),
          placeholder: '<EMAIL> (optional)',
          icon: Mail,
          error: errors.customerInfo?.email,
          autoComplete: 'email',
        })}
        
        {renderFormField({
          id: 'customerInfo.phone',
          label: 'Phone Number',
          type: 'tel',
          value: data.customerInfo.phone,
          onChange: (value) => handleInputChange('customerInfo.phone', value),
          placeholder: '+**************** (optional)',
          icon: Phone,
          error: errors.customerInfo?.phone,
          pattern: '[+]?[0-9\\s\\-\\(\\)]+',
          autoComplete: 'tel',
        })}
      </div>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <AlertCircle className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
          <div className="text-blue-800 text-sm">
            <p className="font-medium mb-1">Privacy Notice</p>
            <p>Your contact information is only used for order updates and will not be shared with third parties.</p>
          </div>
        </div>
      </div>
    </div>
  );
  
  const renderDeliveryStep = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <MapPin className="w-12 h-12 text-orange-500 mx-auto mb-3" />
        <h2 className="text-2xl font-bold text-gray-900">Delivery Information</h2>
        <p className="text-gray-600 mt-2">Where should we deliver your order?</p>
      </div>
      
      <div className="space-y-4">
        {renderFormField({
          id: 'deliveryInfo.roomNumber',
          label: 'Room Number',
          value: data.deliveryInfo.roomNumber,
          onChange: (value) => handleInputChange('deliveryInfo.roomNumber', value),
          placeholder: 'e.g., 205A, Suite 301',
          icon: Building,
          error: errors.deliveryInfo?.roomNumber,
          required: true,
          maxLength: 20,
        })}
        
        {renderFormField({
          id: 'deliveryInfo.deliveryInstructions',
          label: 'Delivery Instructions',
          type: 'textarea',
          value: data.deliveryInfo.deliveryInstructions,
          onChange: (value) => handleInputChange('deliveryInfo.deliveryInstructions', value),
          placeholder: 'Any special delivery instructions? (e.g., knock softly, leave at door)',
          icon: MessageSquare,
          error: errors.deliveryInfo?.deliveryInstructions,
          rows: 3,
          maxLength: 500,
        })}
        
        {renderFormField({
          id: 'deliveryInfo.preferredDeliveryTime',
          label: 'Preferred Delivery Time',
          type: 'datetime-local',
          value: data.deliveryInfo.preferredDeliveryTime,
          onChange: (value) => handleInputChange('deliveryInfo.preferredDeliveryTime', value),
          icon: Clock,
          error: errors.deliveryInfo?.preferredDeliveryTime,
        })}
        
        {/* Contactless Delivery Option */}
        <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <input
            type="checkbox"
            id="isContactless"
            checked={data.deliveryInfo.isContactless || false}
            onChange={(e) => handleInputChange('deliveryInfo.isContactless', e.target.checked)}
            className="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
          />
          <label htmlFor="isContactless" className="text-sm text-gray-700">
            <span className="font-medium">Contactless delivery</span>
            <p className="text-gray-600 mt-1">Leave order at the door and notify via text/call</p>
          </label>
        </div>
      </div>
    </div>
  );
  
  const renderPaymentStep = () => {
    const paymentMethods = [
      { 
        value: 'CASH', 
        label: 'Cash on Delivery', 
        description: 'Pay with cash when your order arrives',
        icon: Banknote 
      },
      { 
        value: 'CARD', 
        label: 'Credit/Debit Card', 
        description: 'Pay securely with your card',
        icon: CreditCard 
      },
      { 
        value: 'ROOM_CHARGE', 
        label: 'Charge to Room', 
        description: 'Add charges to your hotel room bill',
        icon: Building 
      },
      { 
        value: 'DIGITAL_WALLET', 
        label: 'Digital Wallet', 
        description: 'Apple Pay, Google Pay, etc.',
        icon: Smartphone 
      },
    ];
    
    return (
      <div className="space-y-6">
        <div className="text-center mb-6">
          <CreditCard className="w-12 h-12 text-orange-500 mx-auto mb-3" />
          <h2 className="text-2xl font-bold text-gray-900">Payment Method</h2>
          <p className="text-gray-600 mt-2">Choose how you'd like to pay for your order</p>
        </div>
        
        {/* Payment Method Selection */}
        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-700">
            Payment Method <span className="text-red-500">*</span>
          </label>
          
          <div className="space-y-2">
            {paymentMethods.map((method) => {
              const Icon = method.icon;
              const isSelected = data.paymentInfo.paymentMethod === method.value;
              
              return (
                <label
                  key={method.value}
                  className={`
                    flex items-center space-x-3 p-4 border rounded-lg cursor-pointer transition-all
                    ${isSelected 
                      ? 'border-orange-500 bg-orange-50' 
                      : 'border-gray-200 hover:border-gray-300'
                    }
                  `}
                >
                  <input
                    type="radio"
                    value={method.value}
                    checked={isSelected}
                    onChange={(e) => handleInputChange('paymentInfo.paymentMethod', e.target.value)}
                    className="w-4 h-4 text-orange-600 border-gray-300 focus:ring-orange-500"
                  />
                  <Icon className={`w-6 h-6 ${isSelected ? 'text-orange-600' : 'text-gray-400'}`} />
                  <div className="flex-1">
                    <p className={`font-medium ${isSelected ? 'text-orange-900' : 'text-gray-900'}`}>
                      {method.label}
                    </p>
                    <p className={`text-sm ${isSelected ? 'text-orange-700' : 'text-gray-600'}`}>
                      {method.description}
                    </p>
                  </div>
                </label>
              );
            })}
          </div>
          
          {errors.paymentInfo?.paymentMethod && (
            <div className="flex items-center space-x-1 text-red-600 text-sm">
              <AlertCircle className="w-4 h-4" />
              <span>{errors.paymentInfo.paymentMethod}</span>
            </div>
          )}
        </div>
        
        {/* Card Payment Details */}
        {data.paymentInfo.paymentMethod === 'CARD' && (
          <div className="space-y-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">Card Payment Details</h3>
              <button
                type="button"
                onClick={() => setShowPaymentDetails(!showPaymentDetails)}
                className="text-orange-600 hover:text-orange-700 text-sm"
              >
                {showPaymentDetails ? 'Hide' : 'Show'} Details
              </button>
            </div>
            
            {showPaymentDetails && (
              <div className="space-y-3">
                <p className="text-sm text-gray-600 mb-3">
                  In a real implementation, this would integrate with a payment processor like Stripe or Square.
                </p>
                
                {renderFormField({
                  id: 'paymentInfo.paymentToken',
                  label: 'Payment Token',
                  value: data.paymentInfo.paymentToken,
                  onChange: (value) => handleInputChange('paymentInfo.paymentToken', value),
                  placeholder: 'This would be handled by payment processor',
                  error: errors.paymentInfo?.paymentToken,
                  disabled: true,
                })}
              </div>
            )}
          </div>
        )}
        
        {/* Billing Name */}
        {renderFormField({
          id: 'paymentInfo.billingName',
          label: 'Billing Name',
          value: data.paymentInfo.billingName,
          onChange: (value) => handleInputChange('paymentInfo.billingName', value),
          placeholder: 'Name on card/payment method',
          icon: User,
          error: errors.paymentInfo?.billingName,
          autoComplete: 'name',
        })}
        
        {/* Tip Amount */}
        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-700">Tip Amount (Optional)</label>
          
          <div className="grid grid-cols-4 gap-2 mb-3">
            {['0', '15', '18', '20'].map((percentage) => {
              const tipAmount = percentage === '0' ? 0 : (data.orderTotal || 0) * (parseInt(percentage) / 100);
              const isSelected = data.paymentInfo.tipAmount === tipAmount;
              
              return (
                <button
                  key={percentage}
                  type="button"
                  onClick={() => handleInputChange('paymentInfo.tipAmount', tipAmount)}
                  className={`
                    p-2 rounded-lg border text-sm font-medium transition-all
                    ${isSelected 
                      ? 'border-orange-500 bg-orange-50 text-orange-700' 
                      : 'border-gray-200 text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  {percentage === '0' ? 'No Tip' : `${percentage}%`}
                  {percentage !== '0' && (
                    <div className="text-xs text-gray-600 mt-1">
                      ${tipAmount.toFixed(2)}
                    </div>
                  )}
                </button>
              );
            })}
          </div>
          
          {renderFormField({
            id: 'paymentInfo.tipAmount',
            label: 'Custom Tip Amount',
            type: 'number',
            value: data.paymentInfo.tipAmount,
            onChange: (value) => handleInputChange('paymentInfo.tipAmount', parseFloat(value) || 0),
            placeholder: '0.00',
            error: errors.paymentInfo?.tipAmount,
          })}
        </div>
      </div>
    );
  };
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  const stepRenderers = {
    customer: renderCustomerStep,
    delivery: renderDeliveryStep,
    payment: renderPaymentStep,
  };
  
  return (
    <div className="max-w-md mx-auto">
      {stepRenderers[step]()}
    </div>
  );
};
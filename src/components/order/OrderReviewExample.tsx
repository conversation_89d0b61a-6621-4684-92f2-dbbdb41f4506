// Example usage of the OrderReviewSubmission component
'use client';

import React, { useState } from 'react';
import { OrderReviewSubmission } from './OrderReviewSubmission';
import { useMenuStore } from '@/stores/menuStore';
import type { MenuItem } from '@/stores/menuStore';
import type { OrderSubmissionResponse } from '@/types/order';

// Mock data for demonstration
const mockMenuItems: MenuItem[] = [
  {
    id: 'item1',
    name: 'Grilled Salmon',
    description: 'Fresh Atlantic salmon grilled to perfection with lemon herbs',
    category: 'Main Course',
    pricing: {
      basePrice: 24.99,
      currentPrice: 22.99,
      discountPercentage: 8,
      appliedRules: ['Happy Hour Discount'],
    },
    availability: {
      isAvailable: true,
      remainingQuantity: 15,
      estimatedWaitTime: 18,
    },
    imageUrl: '/images/grilled-salmon.jpg',
    isVegetarian: false,
    isVegan: false,
    preparationTime: 18,
    tags: ['Healthy', 'Gluten-Free'],
    allergens: [
      { id: 'fish', name: 'Fish', severity: 'CONTAINS' },
    ],
    sortOrder: 1,
  },
  {
    id: 'item2',
    name: 'Caesar Salad',
    description: 'Crisp romaine lettuce with parmesan, croutons, and caesar dressing',
    category: 'Salads',
    pricing: {
      basePrice: 12.99,
      currentPrice: 12.99,
      appliedRules: [],
    },
    availability: {
      isAvailable: true,
      remainingQuantity: 25,
      estimatedWaitTime: 8,
    },
    imageUrl: '/images/caesar-salad.jpg',
    isVegetarian: true,
    isVegan: false,
    preparationTime: 8,
    tags: ['Fresh', 'Vegetarian'],
    allergens: [
      { id: 'dairy', name: 'Dairy', severity: 'CONTAINS' },
      { id: 'gluten', name: 'Gluten', severity: 'CONTAINS' },
    ],
    sortOrder: 2,
  },
  {
    id: 'item3',
    name: 'Chocolate Brownie',
    description: 'Rich chocolate brownie served warm with vanilla ice cream',
    category: 'Desserts',
    pricing: {
      basePrice: 8.99,
      currentPrice: 8.99,
      appliedRules: [],
    },
    availability: {
      isAvailable: true,
      remainingQuantity: 12,
      estimatedWaitTime: 5,
    },
    imageUrl: '/images/chocolate-brownie.jpg',
    isVegetarian: true,
    isVegan: false,
    preparationTime: 5,
    tags: ['Sweet', 'Dessert'],
    allergens: [
      { id: 'dairy', name: 'Dairy', severity: 'CONTAINS' },
      { id: 'eggs', name: 'Eggs', severity: 'CONTAINS' },
      { id: 'gluten', name: 'Gluten', severity: 'CONTAINS' },
      { id: 'nuts', name: 'Tree Nuts', severity: 'MAY_CONTAIN' },
    ],
    sortOrder: 3,
  },
];

export const OrderReviewExample: React.FC = () => {
  // =====================================================
  // STATE
  // =====================================================
  
  const [showOrderReview, setShowOrderReview] = useState(false);
  const [lastOrderResponse, setLastOrderResponse] = useState<OrderSubmissionResponse | null>(null);
  
  const { addToCart, clearCart, cartItems, cartCount } = useMenuStore();
  
  // =====================================================
  // EVENT HANDLERS
  // =====================================================
  
  const handleAddSampleItems = () => {
    // Clear existing cart
    clearCart();
    
    // Add sample items with different customizations
    addToCart(mockMenuItems[0], 2, {
      notes: 'Medium rare, no onions',
      modifications: ['No onions', 'Extra lemon'],
    });
    
    addToCart(mockMenuItems[1], 1, {
      notes: 'Dressing on the side',
      modifications: ['Dressing on side', 'Extra croutons'],
    });
    
    addToCart(mockMenuItems[2], 1, {
      notes: 'Extra warm',
    });
    
    console.log('Sample items added to cart');
  };
  
  const handleOpenOrderReview = () => {
    if (cartCount === 0) {
      alert('Please add some items to your cart first');
      return;
    }
    setShowOrderReview(true);
  };
  
  const handleCloseOrderReview = () => {
    setShowOrderReview(false);
  };
  
  const handleOrderSuccess = (response: OrderSubmissionResponse) => {
    setLastOrderResponse(response);
    console.log('Order submitted successfully:', response);
  };
  
  // =====================================================
  // RENDER HELPERS
  // =====================================================
  
  const renderMenuItems = () => (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      {mockMenuItems.map((item) => (
        <div key={item.id} className="bg-white rounded-lg shadow border border-gray-200 p-4">
          {item.imageUrl && (
            <img
              src={item.imageUrl}
              alt={item.name}
              className="w-full h-32 object-cover rounded-lg mb-3"
              onError={(e) => {
                // Hide broken images
                e.currentTarget.style.display = 'none';
              }}
            />
          )}
          
          <h3 className="font-semibold text-gray-900 mb-2">{item.name}</h3>
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">{item.description}</p>
          
          <div className="flex items-center justify-between">
            <div>
              <span className="font-bold text-lg text-gray-900">
                ${item.pricing.currentPrice.toFixed(2)}
              </span>
              {item.pricing.discountPercentage && (
                <span className="text-green-600 text-sm ml-1">
                  ({item.pricing.discountPercentage}% off)
                </span>
              )}
            </div>
            
            <button
              onClick={() => addToCart(item)}
              className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors text-sm font-medium"
            >
              Add to Cart
            </button>
          </div>
          
          {/* Dietary Info */}
          <div className="flex flex-wrap gap-1 mt-2">
            {item.isVegan && (
              <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                Vegan
              </span>
            )}
            {item.isVegetarian && !item.isVegan && (
              <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                Vegetarian
              </span>
            )}
          </div>
        </div>
      ))}
    </div>
  );
  
  const renderCartSummary = () => {
    if (cartCount === 0) return null;
    
    return (
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
        <h3 className="font-semibold text-orange-900 mb-2">Current Cart</h3>
        <div className="space-y-2">
          {cartItems.map((item) => (
            <div key={item.id} className="flex justify-between text-sm">
              <span className="text-orange-800">
                {item.quantity}x {item.menuItem.name}
                {item.customizations?.notes && (
                  <span className="text-orange-600 italic ml-1">
                    ({item.customizations.notes})
                  </span>
                )}
              </span>
              <span className="text-orange-900 font-medium">
                ${item.totalPrice.toFixed(2)}
              </span>
            </div>
          ))}
          <div className="border-t border-orange-300 pt-2 mt-2">
            <div className="flex justify-between font-semibold text-orange-900">
              <span>Total ({cartCount} items):</span>
              <span>
                ${cartItems.reduce((sum, item) => sum + item.totalPrice, 0).toFixed(2)}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };
  
  const renderLastOrderInfo = () => {
    if (!lastOrderResponse) return null;
    
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
        <h3 className="font-semibold text-green-900 mb-2">Last Order Submitted</h3>
        <div className="text-sm text-green-800">
          <p><strong>Order Number:</strong> {lastOrderResponse.orderNumber}</p>
          <p><strong>Status:</strong> {lastOrderResponse.status}</p>
          <p><strong>Total:</strong> ${lastOrderResponse.totalAmount.toFixed(2)}</p>
          <p><strong>Estimated Ready Time:</strong> {new Date(lastOrderResponse.estimatedReadyTime).toLocaleTimeString()}</p>
        </div>
      </div>
    );
  };
  
  // =====================================================
  // MAIN RENDER
  // =====================================================
  
  if (showOrderReview) {
    return (
      <OrderReviewSubmission
        tenantId="example-tenant-123"
        roomId="room-456"
        tenantName="The Grand Hotel Restaurant"
        onClose={handleCloseOrderReview}
        onSuccess={handleOrderSuccess}
      />
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            TapDine Order Review Demo
          </h1>
          <p className="text-gray-600 text-lg mb-6">
            Experience the complete order submission flow with real validation and error handling
          </p>
          
          {/* Quick Actions */}
          <div className="flex flex-wrap gap-3 justify-center">
            <button
              onClick={handleAddSampleItems}
              className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
            >
              Add Sample Items
            </button>
            
            <button
              onClick={clearCart}
              disabled={cartCount === 0}
              className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                cartCount === 0
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-red-500 text-white hover:bg-red-600'
              }`}
            >
              Clear Cart
            </button>
            
            <button
              onClick={handleOpenOrderReview}
              disabled={cartCount === 0}
              className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                cartCount === 0
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-orange-500 text-white hover:bg-orange-600'
              }`}
            >
              Proceed to Checkout ({cartCount} items)
            </button>
          </div>
        </div>
        
        {/* Last Order Info */}
        {renderLastOrderInfo()}
        
        {/* Cart Summary */}
        {renderCartSummary()}
        
        {/* Sample Menu Items */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Sample Menu Items</h2>
          <p className="text-gray-600 mb-6">
            Add these items to your cart to test the order review and submission process
          </p>
          {renderMenuItems()}
        </div>
        
        {/* Features Overview */}
        <div className="bg-white rounded-lg shadow border border-gray-200 p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Component Features</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">🔍 Order Review</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Interactive item editing</li>
                <li>• Quantity adjustments</li>
                <li>• Customization display</li>
                <li>• Real-time total calculation</li>
                <li>• Estimated preparation time</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">✅ Form Validation</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Real-time field validation</li>
                <li>• Visual error indicators</li>
                <li>• Step-by-step validation</li>
                <li>• Comprehensive error messages</li>
                <li>• Terms agreement requirement</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">💳 Payment Options</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Multiple payment methods</li>
                <li>• Tip calculation</li>
                <li>• Room charge option</li>
                <li>• Secure card processing</li>
                <li>• Payment validation</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">🎯 User Experience</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Progress indicator</li>
                <li>• Mobile-responsive design</li>
                <li>• Loading states</li>
                <li>• Haptic feedback</li>
                <li>• Accessibility features</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">🔗 API Integration</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Backend API integration</li>
                <li>• Error handling</li>
                <li>• Request cancellation</li>
                <li>• Response validation</li>
                <li>• Cart management</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">🎉 Confirmation</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Order confirmation page</li>
                <li>• Order number generation</li>
                <li>• Tracking link</li>
                <li>• Email notifications</li>
                <li>• Success animations</li>
              </ul>
            </div>
          </div>
        </div>
        
        {/* Implementation Notes */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="font-semibold text-blue-900 mb-3">Implementation Notes</h3>
          <div className="text-sm text-blue-800 space-y-2">
            <p>
              <strong>State Management:</strong> Uses Zustand for cart state management with localStorage persistence.
            </p>
            <p>
              <strong>Form Validation:</strong> Custom validation hook with real-time feedback and comprehensive error handling.
            </p>
            <p>
              <strong>API Integration:</strong> RESTful API integration with proper error handling and loading states.
            </p>
            <p>
              <strong>Responsive Design:</strong> Mobile-first approach with progressive enhancement for larger screens.
            </p>
            <p>
              <strong>TypeScript:</strong> Fully typed with comprehensive interfaces for type safety.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
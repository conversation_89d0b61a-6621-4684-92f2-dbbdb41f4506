# BHEEMDINE Menu API Endpoints
# FastAPI endpoint for retrieving menu items with dynamic pricing and availability

import logging
import time
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, HTTPException, Depends, Query, Path, Body
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc

from ..database import get_db
from ..models.menu import (
    MenuItem, Allergen, MenuItemAllergen, Tenant,
    MenuRequest, MenuResponse, MenuItemResponse, MenuFilters, PricingContext,
    ErrorResponse, AllergenInfo, MenuCategory
)
from ..services.pricing_engine import PricingEngine
from ..services.availability_engine import AvailabilityEngine
from ..auth.dependencies import get_current_user, verify_tenant_access

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/v1/menu",
    tags=["menu"],
    responses={
        404: {"model": ErrorResponse, "description": "Menu not found"},
        400: {"model": ErrorResponse, "description": "Invalid request"},
        403: {"model": ErrorResponse, "description": "Access denied"},
    }
)

@router.post(
    "/{tenant_id}",
    response_model=MenuResponse,
    summary="Get menu items for a tenant",
    description="""
    Retrieve menu items for a specific tenant/location with dynamic pricing and availability.
    
    This endpoint provides:
    - Filtered menu items based on category, dietary preferences, allergens, etc.
    - Dynamic pricing based on time, customer type, quantity, and active promotions  
    - Real-time availability based on inventory, time constraints, and kitchen capacity
    - Allergen information for dietary restrictions
    - Pagination and sorting options
    
    The pricing engine applies various rules in order:
    1. Happy hour discounts (time-based)
    2. Bulk quantity discounts
    3. Customer loyalty discounts
    4. Seasonal promotions
    5. Room-type pricing (VIP, regular)
    
    Availability is determined by:
    - Base availability flag
    - Current inventory levels
    - Time-based restrictions (breakfast/lunch/dinner)
    - Daily quantity limits
    - Kitchen capacity and preparation constraints
    """,
    response_description="Menu items with pricing and availability information"
)
async def get_menu_items(
    tenant_id: UUID = Path(..., description="Tenant (restaurant/hotel) UUID"),
    request: MenuRequest = Body(..., description="Menu request with filters and context"),
    db: Session = Depends(get_db),
    current_user: Optional[dict] = Depends(get_current_user)
) -> MenuResponse:
    """
    Get menu items for a specific tenant with dynamic pricing and availability
    
    Args:
        tenant_id: UUID of the tenant (restaurant/hotel)
        request: Menu request with filters and pricing context
        db: Database session
        current_user: Current authenticated user (optional for public menu access)
        
    Returns:
        MenuResponse with filtered menu items, pricing, and availability
        
    Raises:
        HTTPException: 404 if tenant not found, 400 for invalid filters, 403 for access denied
    """
    
    # Track request timing for performance monitoring
    start_time = time.time()
    request_time = datetime.utcnow()
    
    try:
        logger.info(f"Menu request for tenant {tenant_id} with filters: {request.filters}")
        
        # Validate tenant exists and is active
        tenant = await _validate_tenant(db, tenant_id)
        
        # Verify user has access to this tenant (if authenticated)
        if current_user:
            await _verify_tenant_access(current_user, tenant_id)
        
        # Initialize engines
        pricing_engine = PricingEngine(db)
        availability_engine = AvailabilityEngine(db)
        
        # Build base query with eager loading for performance
        query = (
            db.query(MenuItem)
            .options(
                joinedload(MenuItem.allergens).joinedload(MenuItemAllergen.allergen)
            )
            .filter(MenuItem.tenantId == tenant_id)
        )
        
        # Apply filters
        query, applied_filters = await _apply_filters(query, request.filters)
        
        # Get total count before pagination
        total_items = query.count()
        
        # Apply sorting
        query = await _apply_sorting(query, request.sort_by, request.sort_order)
        
        # Apply pagination
        offset = (request.page - 1) * request.page_size
        menu_items = query.offset(offset).limit(request.page_size).all()
        
        # Process each menu item with pricing and availability
        response_items = []
        for item in menu_items:
            try:
                # Calculate dynamic pricing
                pricing_info = pricing_engine.calculate_item_pricing(
                    item, 
                    request.pricing_context
                )
                
                # Check availability
                availability_info = availability_engine.check_item_availability(
                    item,
                    request_time,
                    request.pricing_context.order_quantity if request.pricing_context else 1
                )
                
                # Get allergen information
                allergen_info = await _get_allergen_info(db, item)
                
                # Build response item
                response_item = MenuItemResponse(
                    id=item.id,
                    name=item.name,
                    description=item.description,
                    category=item.category,
                    pricing=pricing_info,
                    availability=availability_info,
                    imageUrl=item.imageUrl,
                    isVegetarian=item.isVegetarian,
                    isVegan=item.isVegan,
                    preparationTime=item.preparationTime,
                    tags=item.tags or [],
                    allergens=allergen_info,
                    sortOrder=item.sortOrder,
                    updatedAt=item.updatedAt
                )
                
                response_items.append(response_item)
                
            except Exception as e:
                logger.error(f"Error processing menu item {item.id}: {str(e)}")
                # Continue processing other items, don't fail the entire request
                continue
        
        # Get available categories for this tenant
        available_categories = await _get_available_categories(db, tenant_id)
        
        # Get active pricing rules for transparency
        active_pricing_rules = await _get_active_pricing_rules(db, tenant_id, request_time)
        
        # Calculate pagination info
        total_pages = (total_items + request.page_size - 1) // request.page_size
        
        # Calculate response time
        response_time_ms = (time.time() - start_time) * 1000
        
        # Build final response
        response = MenuResponse(
            items=response_items,
            total_items=total_items,
            total_pages=total_pages,
            current_page=request.page,
            page_size=request.page_size,
            applied_filters=applied_filters,
            pricing_context=request.pricing_context,
            request_time=request_time,
            response_time_ms=response_time_ms,
            available_categories=available_categories,
            active_pricing_rules=active_pricing_rules
        )
        
        logger.info(f"Menu request completed in {response_time_ms:.2f}ms, returned {len(response_items)} items")
        
        return response
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error in get_menu_items: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Internal server error",
                "error_code": "MENU_FETCH_ERROR",
                "details": {"message": str(e)},
                "timestamp": datetime.utcnow()
            }
        )

@router.get(
    "/{tenant_id}/categories",
    response_model=List[str],
    summary="Get available menu categories",
    description="Get list of all menu categories available for a tenant"
)
async def get_menu_categories(
    tenant_id: UUID = Path(..., description="Tenant UUID"),
    db: Session = Depends(get_db)
) -> List[str]:
    """
    Get all available menu categories for a tenant
    
    Args:
        tenant_id: UUID of the tenant
        db: Database session
        
    Returns:
        List of category names
    """
    
    # Validate tenant exists
    await _validate_tenant(db, tenant_id)
    
    # Get distinct categories
    categories = (
        db.query(MenuItem.category)
        .filter(
            MenuItem.tenantId == tenant_id,
            MenuItem.isAvailable == True
        )
        .distinct()
        .all()
    )
    
    return [category[0] for category in categories]

@router.get(
    "/{tenant_id}/item/{item_id}",
    response_model=MenuItemResponse,
    summary="Get single menu item details",
    description="Get detailed information for a specific menu item including pricing and availability"
)
async def get_menu_item(
    tenant_id: UUID = Path(..., description="Tenant UUID"),
    item_id: UUID = Path(..., description="Menu item UUID"),
    quantity: int = Query(1, ge=1, le=50, description="Quantity for pricing calculation"),
    room_id: Optional[UUID] = Query(None, description="Room ID for room-based pricing"),
    db: Session = Depends(get_db),
    current_user: Optional[dict] = Depends(get_current_user)
) -> MenuItemResponse:
    """
    Get detailed information for a single menu item
    
    Args:
        tenant_id: UUID of the tenant
        item_id: UUID of the menu item
        quantity: Quantity for bulk pricing calculation
        room_id: Room ID for room-based pricing
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Detailed menu item information
    """
    
    # Validate tenant
    await _validate_tenant(db, tenant_id)
    
    # Get menu item
    menu_item = (
        db.query(MenuItem)
        .options(
            joinedload(MenuItem.allergens).joinedload(MenuItemAllergen.allergen)
        )
        .filter(
            MenuItem.id == item_id,
            MenuItem.tenantId == tenant_id
        )
        .first()
    )
    
    if not menu_item:
        raise HTTPException(
            status_code=404,
            detail={
                "error": "Menu item not found",
                "error_code": "ITEM_NOT_FOUND",
                "details": {"item_id": str(item_id)},
                "timestamp": datetime.utcnow()
            }
        )
    
    # Create pricing context
    pricing_context = PricingContext(
        room_id=room_id,
        order_quantity=quantity,
        customer_id=UUID(current_user['sub']) if current_user else None
    )
    
    # Initialize engines
    pricing_engine = PricingEngine(db)
    availability_engine = AvailabilityEngine(db)
    
    # Calculate pricing and availability
    pricing_info = pricing_engine.calculate_item_pricing(menu_item, pricing_context)
    availability_info = availability_engine.check_item_availability(
        menu_item,
        datetime.utcnow(),
        quantity
    )
    
    # Get allergen information
    allergen_info = await _get_allergen_info(db, menu_item)
    
    # Build response
    return MenuItemResponse(
        id=menu_item.id,
        name=menu_item.name,
        description=menu_item.description,
        category=menu_item.category,
        pricing=pricing_info,
        availability=availability_info,
        imageUrl=menu_item.imageUrl,
        isVegetarian=menu_item.isVegetarian,
        isVegan=menu_item.isVegan,
        preparationTime=menu_item.preparationTime,
        tags=menu_item.tags or [],
        allergens=allergen_info,
        sortOrder=menu_item.sortOrder,
        updatedAt=menu_item.updatedAt
    )

# =====================================================
# HELPER FUNCTIONS
# =====================================================

async def _validate_tenant(db: Session, tenant_id: UUID) -> Tenant:
    """Validate that tenant exists and is active"""
    
    tenant = (
        db.query(Tenant)
        .filter(
            Tenant.id == tenant_id,
            Tenant.isActive == True,
            Tenant.isDeleted == False
        )
        .first()
    )
    
    if not tenant:
        raise HTTPException(
            status_code=404,
            detail={
                "error": "Tenant not found or inactive",
                "error_code": "TENANT_NOT_FOUND",
                "details": {"tenant_id": str(tenant_id)},
                "timestamp": datetime.utcnow()
            }
        )
    
    return tenant

async def _verify_tenant_access(current_user: dict, tenant_id: UUID) -> None:
    """Verify user has access to the specified tenant"""
    
    user_tenant_id = current_user.get('tenant_id')
    if user_tenant_id and str(user_tenant_id) != str(tenant_id):
        raise HTTPException(
            status_code=403,
            detail={
                "error": "Access denied to tenant",
                "error_code": "TENANT_ACCESS_DENIED",
                "details": {
                    "requested_tenant": str(tenant_id),
                    "user_tenant": str(user_tenant_id)
                },
                "timestamp": datetime.utcnow()
            }
        )

async def _apply_filters(query, filters: Optional[MenuFilters]) -> tuple:
    """Apply filters to the menu query"""
    
    applied_filters = {}
    
    if not filters:
        return query, applied_filters
    
    # Category filtering
    if filters.categories:
        query = query.filter(MenuItem.category.in_(filters.categories))
        applied_filters['categories'] = filters.categories
    
    # Dietary preference filtering
    if filters.vegetarian_only:
        query = query.filter(MenuItem.isVegetarian == True)
        applied_filters['vegetarian_only'] = True
    
    if filters.vegan_only:
        query = query.filter(MenuItem.isVegan == True)
        applied_filters['vegan_only'] = True
    
    # Price range filtering
    if filters.min_price is not None:
        query = query.filter(MenuItem.price >= filters.min_price)
        applied_filters['min_price'] = filters.min_price
    
    if filters.max_price is not None:
        query = query.filter(MenuItem.price <= filters.max_price)
        applied_filters['max_price'] = filters.max_price
    
    # Availability filtering
    if filters.available_only:
        query = query.filter(MenuItem.isAvailable == True)
        applied_filters['available_only'] = True
    
    # Preparation time filtering
    if filters.max_prep_time is not None:
        query = query.filter(MenuItem.preparationTime <= filters.max_prep_time)
        applied_filters['max_prep_time'] = filters.max_prep_time
    
    # Search query filtering
    if filters.search_query:
        search_term = f"%{filters.search_query}%"
        query = query.filter(
            or_(
                MenuItem.name.ilike(search_term),
                MenuItem.description.ilike(search_term)
            )
        )
        applied_filters['search_query'] = filters.search_query
    
    # Tags filtering
    if filters.tags:
        # Using PostgreSQL JSON contains operator
        for tag in filters.tags:
            query = query.filter(MenuItem.tags.contains([tag]))
        applied_filters['tags'] = filters.tags
    
    # Allergen exclusion filtering
    if filters.exclude_allergens:
        # Subquery to find items with excluded allergens
        excluded_items = (
            query.session.query(MenuItemAllergen.menuItemId)
            .join(Allergen)
            .filter(Allergen.name.in_(filters.exclude_allergens))
            .subquery()
        )
        
        query = query.filter(~MenuItem.id.in_(excluded_items))
        applied_filters['exclude_allergens'] = filters.exclude_allergens
    
    return query, applied_filters

async def _apply_sorting(query, sort_by: str, sort_order: str):
    """Apply sorting to the query"""
    
    sort_column = getattr(MenuItem, sort_by, MenuItem.sortOrder)
    
    if sort_order == 'desc':
        query = query.order_by(desc(sort_column))
    else:
        query = query.order_by(asc(sort_column))
    
    return query

async def _get_allergen_info(db: Session, menu_item: MenuItem) -> List[AllergenInfo]:
    """Get allergen information for a menu item"""
    
    allergen_data = (
        db.query(Allergen, MenuItemAllergen.severity)
        .join(MenuItemAllergen)
        .filter(MenuItemAllergen.menuItemId == menu_item.id)
        .all()
    )
    
    return [
        AllergenInfo(
            id=allergen.id,
            name=allergen.name,
            icon=allergen.icon,
            severity=severity
        )
        for allergen, severity in allergen_data
    ]

async def _get_available_categories(db: Session, tenant_id: UUID) -> List[str]:
    """Get all available categories for a tenant"""
    
    categories = (
        db.query(MenuItem.category)
        .filter(
            MenuItem.tenantId == tenant_id,
            MenuItem.isAvailable == True
        )
        .distinct()
        .all()
    )
    
    return [category[0] for category in categories]

async def _get_active_pricing_rules(db: Session, tenant_id: UUID, request_time: datetime) -> List[str]:
    """Get names of currently active pricing rules"""
    
    from ..models.menu import PricingRule
    
    rules = (
        db.query(PricingRule.name)
        .filter(
            PricingRule.tenantId == tenant_id,
            PricingRule.isActive == True,
            or_(
                PricingRule.validFrom.is_(None),
                PricingRule.validFrom <= request_time
            ),
            or_(
                PricingRule.validTo.is_(None),
                PricingRule.validTo >= request_time
            )
        )
        .all()
    )
    
    return [rule[0] for rule in rules]
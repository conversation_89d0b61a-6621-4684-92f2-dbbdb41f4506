# BHEEMDINE WhatsApp Notification Endpoints
# FastAPI endpoints for managing WhatsApp notifications

import logging
from datetime import datetime
from typing import Dict, List, Optional
from uuid import UUID

from fastapi import APIRouter, HTTPException, Depends, status, BackgroundTasks
from pydantic import BaseModel, <PERSON>, validator
from fastapi.responses import JSONResponse

from ..services.whatsapp_notification import (
    WhatsAppNotificationService, 
    WhatsAppNotificationError,
    create_whatsapp_service,
    validate_phone_number,
    format_phone_number
)
from ..auth.dependencies import get_current_user_optional

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/v1/notifications",
    tags=["notifications"],
    responses={
        400: {"description": "Invalid request"},
        401: {"description": "Authentication failed"},
        403: {"description": "Access denied"},
        500: {"description": "Internal server error"},
    }
)

# =====================================================
# REQUEST/RESPONSE MODELS
# =====================================================

class PhoneNumber(BaseModel):
    """Phone number model with validation"""
    number: str = Field(..., description="Phone number with country code")
    
    @validator('number')
    def validate_phone(cls, v):
        if not validate_phone_number(v):
            raise ValueError('Invalid phone number format. Include country code (e.g., +1234567890)')
        return format_phone_number(v)

class OrderNotificationRequest(BaseModel):
    """Request model for new order notifications"""
    order_id: UUID = Field(..., description="Order UUID")
    order_number: str = Field(..., description="Human-readable order number")
    customer_name: str = Field(..., description="Customer name")
    room_number: str = Field(..., description="Room number")
    total_amount: float = Field(..., ge=0, description="Total order amount")
    item_count: int = Field(..., ge=1, description="Number of items in order")
    order_notes: Optional[str] = Field(None, description="Special order instructions")
    tenant_name: Optional[str] = Field(None, description="Restaurant/tenant name")
    staff_phones: List[PhoneNumber] = Field(..., description="List of staff phone numbers to notify")
    priority: Optional[str] = Field("normal", description="Order priority (normal, urgent, special)")
    
    # Detailed order items for kitchen notifications
    items: Optional[List[Dict]] = Field(None, description="Detailed order items")

class StatusUpdateRequest(BaseModel):
    """Request model for order status updates"""
    order_id: UUID = Field(..., description="Order UUID")
    order_number: str = Field(..., description="Human-readable order number")
    customer_phone: PhoneNumber = Field(..., description="Customer phone number")
    new_status: str = Field(..., description="New order status")
    estimated_time: Optional[str] = Field(None, description="Estimated completion time")
    tenant_name: Optional[str] = Field(None, description="Restaurant/tenant name")

class KitchenNotificationRequest(BaseModel):
    """Request model for kitchen notifications"""
    order_id: UUID = Field(..., description="Order UUID")
    order_number: str = Field(..., description="Human-readable order number")
    room_number: str = Field(..., description="Room number")
    items: List[Dict] = Field(..., description="Detailed order items with customizations")
    kitchen_phones: List[PhoneNumber] = Field(..., description="Kitchen staff phone numbers")
    priority: Optional[str] = Field("normal", description="Order priority")
    order_notes: Optional[str] = Field(None, description="Special order instructions")
    delivery_instructions: Optional[str] = Field(None, description="Delivery instructions")

class BulkNotificationRequest(BaseModel):
    """Request model for sending notifications to multiple recipients"""
    message: str = Field(..., max_length=1600, description="Message content (max 1600 chars)")
    phone_numbers: List[PhoneNumber] = Field(..., description="List of recipient phone numbers")
    message_type: Optional[str] = Field("general", description="Type of message for tracking")

class NotificationResponse(BaseModel):
    """Response model for notification operations"""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Result message")
    total_recipients: Optional[int] = Field(None, description="Total number of recipients")
    successful_sends: Optional[int] = Field(None, description="Number of successful sends")
    failed_sends: Optional[int] = Field(None, description="Number of failed sends")
    results: Optional[List[Dict]] = Field(None, description="Detailed results per recipient")
    sent_at: str = Field(..., description="Timestamp when notification was sent")

class ServiceHealthResponse(BaseModel):
    """Response model for service health check"""
    status: str = Field(..., description="Service status (healthy, degraded, unhealthy)")
    account_status: Optional[str] = Field(None, description="Twilio account status")
    from_number: Optional[str] = Field(None, description="WhatsApp from number")
    tested_at: str = Field(..., description="Test timestamp")
    error: Optional[str] = Field(None, description="Error message if unhealthy")

# =====================================================
# ENDPOINTS
# =====================================================

@router.post(
    "/order/new",
    response_model=NotificationResponse,
    status_code=status.HTTP_200_OK,
    summary="Send new order notification",
    description="""
    Send WhatsApp notifications to assigned staff when a new order is received.
    
    This endpoint:
    - Sends formatted order details to all specified staff members
    - Includes order items, customer info, and special instructions
    - Handles partial failures gracefully
    - Returns detailed results for each recipient
    
    **Authentication:** Optional (for internal service calls)
    **Rate Limiting:** Applied per tenant
    """
)
async def send_new_order_notification(
    request: OrderNotificationRequest,
    background_tasks: BackgroundTasks,
    current_user: Optional[Dict] = Depends(get_current_user_optional),
    whatsapp_service: WhatsAppNotificationService = Depends(create_whatsapp_service)
) -> NotificationResponse:
    """
    Send new order notification to staff
    
    Args:
        request: Order notification details
        background_tasks: FastAPI background tasks
        current_user: Optional authenticated user
        whatsapp_service: WhatsApp notification service
        
    Returns:
        NotificationResponse with results
    """
    
    try:
        logger.info(f"Sending new order notification for order {request.order_number}")
        
        # Prepare order data for notification service
        order_data = {
            "order_id": str(request.order_id),
            "order_number": request.order_number,
            "customer_name": request.customer_name,
            "room_number": request.room_number,
            "total_amount": request.total_amount,
            "item_count": request.item_count,
            "order_notes": request.order_notes,
            "items": request.items or [],
            "tenant_name": request.tenant_name
        }
        
        # Extract phone numbers
        staff_phones = [phone.number for phone in request.staff_phones]
        
        # Prepare tenant info
        tenant_info = {"name": request.tenant_name} if request.tenant_name else None
        
        # Send notifications
        result = await whatsapp_service.send_new_order_notification(
            order_data=order_data,
            staff_phones=staff_phones,
            tenant_info=tenant_info
        )
        
        # Log success
        logger.info(
            f"New order notification completed for {request.order_number}: "
            f"{result['successful_sends']}/{result['total_recipients']} sent"
        )
        
        return NotificationResponse(
            success=result['successful_sends'] > 0,
            message=f"Notification sent to {result['successful_sends']}/{result['total_recipients']} staff members",
            total_recipients=result['total_recipients'],
            successful_sends=result['successful_sends'],
            failed_sends=result['total_recipients'] - result['successful_sends'],
            results=result['results'],
            sent_at=result['sent_at']
        )
        
    except WhatsAppNotificationError as e:
        logger.error(f"WhatsApp notification error: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error_code": e.error_code,
                "error_message": e.message,
                "details": e.details
            }
        )
    except Exception as e:
        logger.error(f"Unexpected error in new order notification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "Failed to send order notification", "details": str(e)}
        )

@router.post(
    "/order/status-update",
    response_model=NotificationResponse,
    status_code=status.HTTP_200_OK,
    summary="Send order status update",
    description="""
    Send WhatsApp notification to customer about order status changes.
    
    Supports status updates like:
    - CONFIRMED: Order confirmed and being prepared
    - PREPARING: Order is being prepared
    - READY: Order ready for pickup/delivery
    - OUT_FOR_DELIVERY: Order out for delivery
    - DELIVERED: Order delivered
    - CANCELLED: Order cancelled
    """
)
async def send_status_update_notification(
    request: StatusUpdateRequest,
    background_tasks: BackgroundTasks,
    current_user: Optional[Dict] = Depends(get_current_user_optional),
    whatsapp_service: WhatsAppNotificationService = Depends(create_whatsapp_service)
) -> NotificationResponse:
    """
    Send order status update to customer
    
    Args:
        request: Status update details
        background_tasks: FastAPI background tasks
        current_user: Optional authenticated user
        whatsapp_service: WhatsApp notification service
        
    Returns:
        NotificationResponse with result
    """
    
    try:
        logger.info(f"Sending status update for order {request.order_number}: {request.new_status}")
        
        # Prepare order data
        order_data = {
            "order_id": str(request.order_id),
            "order_number": request.order_number,
            "tenant_name": request.tenant_name
        }
        
        # Send status update
        result = await whatsapp_service.send_order_status_update(
            order_data=order_data,
            customer_phone=request.customer_phone.number,
            new_status=request.new_status,
            estimated_time=request.estimated_time
        )
        
        logger.info(f"Status update sent for order {request.order_number}")
        
        return NotificationResponse(
            success=True,
            message=f"Status update sent to customer: {request.new_status}",
            total_recipients=1,
            successful_sends=1,
            failed_sends=0,
            results=[{
                "phone": request.customer_phone.number,
                "status": "sent",
                "message_sid": result.get("message_sid"),
                "sent_at": result.get("sent_at")
            }],
            sent_at=result.get("sent_at", datetime.utcnow().isoformat())
        )
        
    except WhatsAppNotificationError as e:
        logger.error(f"WhatsApp notification error: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error_code": e.error_code,
                "error_message": e.message,
                "details": e.details
            }
        )
    except Exception as e:
        logger.error(f"Unexpected error in status update: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "Failed to send status update", "details": str(e)}
        )

@router.post(
    "/kitchen/order",
    response_model=NotificationResponse,
    status_code=status.HTTP_200_OK,
    summary="Send kitchen notification",
    description="""
    Send detailed order notification to kitchen staff.
    
    Includes:
    - Complete item list with quantities
    - Customizations and modifications
    - Special cooking instructions
    - Order priority level
    - Estimated preparation requirements
    """
)
async def send_kitchen_notification(
    request: KitchenNotificationRequest,
    background_tasks: BackgroundTasks,
    current_user: Optional[Dict] = Depends(get_current_user_optional),
    whatsapp_service: WhatsAppNotificationService = Depends(create_whatsapp_service)
) -> NotificationResponse:
    """
    Send kitchen notification for new orders
    
    Args:
        request: Kitchen notification details
        background_tasks: FastAPI background tasks
        current_user: Optional authenticated user
        whatsapp_service: WhatsApp notification service
        
    Returns:
        NotificationResponse with results
    """
    
    try:
        logger.info(f"Sending kitchen notification for order {request.order_number}")
        
        # Prepare order data
        order_data = {
            "order_id": str(request.order_id),
            "order_number": request.order_number,
            "room_number": request.room_number,
            "items": request.items,
            "order_notes": request.order_notes,
            "delivery_instructions": request.delivery_instructions
        }
        
        # Extract kitchen phone numbers
        kitchen_phones = [phone.number for phone in request.kitchen_phones]
        
        # Send kitchen notification
        result = await whatsapp_service.send_kitchen_notification(
            order_data=order_data,
            kitchen_phones=kitchen_phones,
            priority=request.priority
        )
        
        logger.info(
            f"Kitchen notification completed for {request.order_number}: "
            f"{result['successful_sends']}/{result['total_recipients']} sent"
        )
        
        return NotificationResponse(
            success=result['successful_sends'] > 0,
            message=f"Kitchen notification sent to {result['successful_sends']}/{result['total_recipients']} staff members",
            total_recipients=result['total_recipients'],
            successful_sends=result['successful_sends'],
            failed_sends=result['total_recipients'] - result['successful_sends'],
            results=result['results'],
            sent_at=result['sent_at']
        )
        
    except WhatsAppNotificationError as e:
        logger.error(f"WhatsApp notification error: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error_code": e.error_code,
                "error_message": e.message,
                "details": e.details
            }
        )
    except Exception as e:
        logger.error(f"Unexpected error in kitchen notification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "Failed to send kitchen notification", "details": str(e)}
        )

@router.post(
    "/bulk",
    response_model=NotificationResponse,
    status_code=status.HTTP_200_OK,
    summary="Send bulk notification",
    description="""
    Send a custom message to multiple recipients.
    
    Useful for:
    - Service announcements
    - Emergency notifications
    - Promotional messages
    - Staff communications
    
    **Note:** Message length is limited to 1600 characters.
    """
)
async def send_bulk_notification(
    request: BulkNotificationRequest,
    background_tasks: BackgroundTasks,
    current_user: Optional[Dict] = Depends(get_current_user_optional),
    whatsapp_service: WhatsAppNotificationService = Depends(create_whatsapp_service)
) -> NotificationResponse:
    """
    Send bulk notification to multiple recipients
    
    Args:
        request: Bulk notification details
        background_tasks: FastAPI background tasks
        current_user: Optional authenticated user
        whatsapp_service: WhatsApp notification service
        
    Returns:
        NotificationResponse with results
    """
    
    try:
        logger.info(f"Sending bulk notification to {len(request.phone_numbers)} recipients")
        
        # Extract phone numbers
        phone_numbers = [phone.number for phone in request.phone_numbers]
        
        # Send notifications to all recipients
        results = []
        for phone in phone_numbers:
            try:
                result = await whatsapp_service._send_whatsapp_message(
                    to_number=phone,
                    message=request.message,
                    message_type=request.message_type
                )
                results.append({
                    "phone": phone,
                    "status": "sent",
                    "message_sid": result.get("sid"),
                    "sent_at": datetime.utcnow().isoformat()
                })
                
            except Exception as e:
                logger.error(f"Failed to send bulk notification to {phone}: {str(e)}")
                results.append({
                    "phone": phone,
                    "status": "failed",
                    "error": str(e),
                    "attempted_at": datetime.utcnow().isoformat()
                })
        
        # Calculate success metrics
        successful = len([r for r in results if r["status"] == "sent"])
        total = len(results)
        
        logger.info(f"Bulk notification completed: {successful}/{total} sent")
        
        return NotificationResponse(
            success=successful > 0,
            message=f"Bulk notification sent to {successful}/{total} recipients",
            total_recipients=total,
            successful_sends=successful,
            failed_sends=total - successful,
            results=results,
            sent_at=datetime.utcnow().isoformat()
        )
        
    except WhatsAppNotificationError as e:
        logger.error(f"WhatsApp notification error: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error_code": e.error_code,
                "error_message": e.message,
                "details": e.details
            }
        )
    except Exception as e:
        logger.error(f"Unexpected error in bulk notification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "Failed to send bulk notification", "details": str(e)}
        )

@router.get(
    "/health",
    response_model=ServiceHealthResponse,
    summary="WhatsApp service health check",
    description="""
    Check the health and connectivity of the WhatsApp notification service.
    
    Returns:
    - Service status (healthy/degraded/unhealthy)
    - Twilio account status
    - Configuration validity
    - Last test timestamp
    """
)
async def check_service_health(
    whatsapp_service: WhatsAppNotificationService = Depends(create_whatsapp_service)
) -> ServiceHealthResponse:
    """
    Check WhatsApp notification service health
    
    Args:
        whatsapp_service: WhatsApp notification service
        
    Returns:
        ServiceHealthResponse with health status
    """
    
    try:
        # Test service connectivity
        test_result = await whatsapp_service.test_connection()
        
        if test_result["status"] == "success":
            return ServiceHealthResponse(
                status="healthy",
                account_status=test_result.get("account_status"),
                from_number=test_result.get("from_number"),
                tested_at=test_result["tested_at"]
            )
        else:
            return ServiceHealthResponse(
                status="unhealthy",
                error=test_result.get("error"),
                tested_at=test_result["tested_at"]
            )
            
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return ServiceHealthResponse(
            status="unhealthy",
            error=str(e),
            tested_at=datetime.utcnow().isoformat()
        )

# =====================================================
# UTILITY ENDPOINTS
# =====================================================

@router.post(
    "/validate-phone",
    summary="Validate phone number",
    description="Validate and format a phone number for WhatsApp messaging"
)
async def validate_phone_endpoint(phone: str) -> Dict:
    """
    Validate and format phone number
    
    Args:
        phone: Phone number to validate
        
    Returns:
        Dict with validation result and formatted number
    """
    
    try:
        is_valid = validate_phone_number(phone)
        formatted = format_phone_number(phone) if is_valid else None
        
        return {
            "valid": is_valid,
            "original": phone,
            "formatted": formatted,
            "whatsapp_format": f"whatsapp:{formatted}" if formatted else None
        }
        
    except Exception as e:
        return {
            "valid": False,
            "original": phone,
            "error": str(e)
        }
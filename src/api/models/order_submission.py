# BHEEMDINE Order Submission Models
# Pydantic models for validating guest order submissions with comprehensive validation

from datetime import datetime
from decimal import Decimal, InvalidOperation
from enum import Enum
from typing import List, Optional, Dict, Any, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, validator, root_validator
import re

# =====================================================
# ENUMS
# =====================================================

class OrderItemStatus(str, Enum):
    """Status of individual order items"""
    PENDING = "PENDING"
    CONFIRMED = "CONFIRMED" 
    PREPARING = "PREPARING"
    READY = "READY"
    SERVED = "SERVED"
    CANCELLED = "CANCELLED"

class OrderStatus(str, Enum):
    """Overall order status"""
    PENDING = "PENDING"        # Order submitted, awaiting confirmation
    CONFIRMED = "CONFIRMED"    # Order confirmed by restaurant
    PREPARING = "PREPARING"    # Being prepared in kitchen
    READY = "READY"           # Ready for delivery/pickup
    DELIVERED = "DELIVERED"    # Delivered to customer
    COMPLETED = "COMPLETED"    # Order completed and paid
    CANCELLED = "CANCELLED"    # Order cancelled

class PaymentMethod(str, Enum):
    """Available payment methods"""
    CASH = "CASH"
    CARD = "CARD"
    DIGITAL_WALLET = "DIGITAL_WALLET"
    ROOM_CHARGE = "ROOM_CHARGE"  # For hotel guests

# =====================================================
# CUSTOMER INFORMATION MODELS
# =====================================================

class GuestCustomerInfo(BaseModel):
    """
    Information for guest customers (no account required)
    All fields are optional to support anonymous ordering
    """
    
    name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=100,
        description="Guest name for order identification"
    )
    
    email: Optional[str] = Field(
        None,
        description="Email for order notifications (optional for guests)"
    )
    
    phone: Optional[str] = Field(
        None,
        description="Phone number for order updates (optional for guests)"
    )
    
    @validator('email')
    def validate_email(cls, v):
        """Validate email format if provided"""
        if v is None:
            return v
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError('Invalid email format')
        
        return v.lower().strip()
    
    @validator('phone')
    def validate_phone(cls, v):
        """Validate phone number format if provided"""
        if v is None:
            return v
        
        # Remove all non-digit characters
        cleaned_phone = re.sub(r'\D', '', v)
        
        # Check if it's a valid length (10-15 digits)
        if len(cleaned_phone) < 10 or len(cleaned_phone) > 15:
            raise ValueError('Phone number must be between 10-15 digits')
        
        return cleaned_phone
    
    @validator('name')
    def validate_name(cls, v):
        """Validate name format if provided"""
        if v is None:
            return v
        
        # Remove extra whitespace and check for valid characters
        cleaned_name = ' '.join(v.strip().split())
        
        if not re.match(r'^[a-zA-Z\s\-\'\.]+$', cleaned_name):
            raise ValueError('Name contains invalid characters')
        
        return cleaned_name

# =====================================================
# ORDER ITEM MODELS
# =====================================================

class OrderItemCustomization(BaseModel):
    """
    Customizations for individual order items
    Supports modifications and special instructions
    """
    
    modifications: List[str] = Field(
        default_factory=list,
        max_items=10,
        description="List of modifications (e.g., 'No onions', 'Extra cheese')"
    )
    
    special_instructions: Optional[str] = Field(
        None,
        max_length=500,
        description="Special preparation instructions"
    )
    
    spice_level: Optional[str] = Field(
        None,
        regex=r'^(mild|medium|hot|extra_hot)$',
        description="Spice level preference"
    )
    
    @validator('modifications')
    def validate_modifications(cls, v):
        """Validate modification list"""
        if not v:
            return v
        
        # Check each modification
        for mod in v:
            if not isinstance(mod, str) or len(mod.strip()) == 0:
                raise ValueError('Each modification must be a non-empty string')
            
            if len(mod) > 100:
                raise ValueError('Each modification must be less than 100 characters')
        
        # Remove duplicates while preserving order
        seen = set()
        unique_mods = []
        for mod in v:
            cleaned_mod = mod.strip()
            if cleaned_mod not in seen:
                seen.add(cleaned_mod)
                unique_mods.append(cleaned_mod)
        
        return unique_mods
    
    @validator('special_instructions')
    def validate_special_instructions(cls, v):
        """Clean and validate special instructions"""
        if v is None:
            return v
        
        # Clean up whitespace
        cleaned = ' '.join(v.strip().split())
        
        # Check for potential security issues (basic sanitization)
        if any(char in cleaned for char in ['<', '>', '{', '}', '[', ']']):
            raise ValueError('Special instructions contain invalid characters')
        
        return cleaned if cleaned else None

class OrderItemRequest(BaseModel):
    """
    Individual item in an order submission
    Represents one menu item with quantity and customizations
    """
    
    menu_item_id: UUID = Field(
        ...,
        description="UUID of the menu item being ordered"
    )
    
    quantity: int = Field(
        ...,
        ge=1,
        le=99,
        description="Quantity of this item (1-99)"
    )
    
    unit_price: Decimal = Field(
        ...,
        ge=0,
        le=9999.99,
        decimal_places=2,
        description="Unit price at time of order (for validation)"
    )
    
    customizations: Optional[OrderItemCustomization] = Field(
        None,
        description="Item customizations and special requests"
    )
    
    notes: Optional[str] = Field(
        None,
        max_length=200,
        description="Additional notes for this specific item"
    )
    
    @validator('unit_price')
    def validate_unit_price(cls, v):
        """Validate unit price format and range"""
        if v < 0:
            raise ValueError('Unit price cannot be negative')
        
        # Ensure proper decimal places
        if v.as_tuple().exponent < -2:
            raise ValueError('Unit price cannot have more than 2 decimal places')
        
        return v
    
    @validator('notes')
    def validate_notes(cls, v):
        """Clean and validate item notes"""
        if v is None:
            return v
        
        cleaned = ' '.join(v.strip().split())
        return cleaned if cleaned else None

# =====================================================
# DELIVERY/PICKUP INFORMATION
# =====================================================

class DeliveryInfo(BaseModel):
    """
    Delivery information for room service or pickup
    """
    
    room_number: Optional[str] = Field(
        None,
        max_length=20,
        description="Room number for hotel room service"
    )
    
    delivery_instructions: Optional[str] = Field(
        None,
        max_length=300,
        description="Special delivery instructions"
    )
    
    preferred_delivery_time: Optional[datetime] = Field(
        None,
        description="Preferred delivery time (if scheduling ahead)"
    )
    
    is_contactless: bool = Field(
        False,
        description="Request contactless delivery"
    )
    
    @validator('room_number')
    def validate_room_number(cls, v):
        """Validate room number format"""
        if v is None:
            return v
        
        cleaned = v.strip().upper()
        
        # Basic room number pattern (alphanumeric with some special chars)
        if not re.match(r'^[A-Z0-9\-\.\/]+$', cleaned):
            raise ValueError('Room number contains invalid characters')
        
        return cleaned
    
    @validator('preferred_delivery_time')
    def validate_delivery_time(cls, v):
        """Validate delivery time is in the future"""
        if v is None:
            return v
        
        # Check if time is in the future (with 5 minute buffer)
        min_time = datetime.utcnow().replace(second=0, microsecond=0)
        if v <= min_time:
            raise ValueError('Delivery time must be in the future')
        
        # Check if time is not too far in the future (24 hours max)
        max_time = min_time.replace(hour=23, minute=59)
        if v > max_time:
            raise ValueError('Delivery time cannot be more than 24 hours in advance')
        
        return v

# =====================================================
# PAYMENT INFORMATION
# =====================================================

class PaymentInfo(BaseModel):
    """
    Payment information for the order
    Note: Sensitive payment data should be handled by payment processor
    """
    
    payment_method: PaymentMethod = Field(
        ...,
        description="Selected payment method"
    )
    
    payment_token: Optional[str] = Field(
        None,
        min_length=10,
        max_length=200,
        description="Payment processor token (for card/digital payments)"
    )
    
    billing_name: Optional[str] = Field(
        None,
        max_length=100,
        description="Name on payment method"
    )
    
    tip_amount: Optional[Decimal] = Field(
        None,
        ge=0,
        le=999.99,
        decimal_places=2,
        description="Tip amount"
    )
    
    @validator('payment_token')
    def validate_payment_token(cls, v, values):
        """Validate payment token is provided for card/digital payments"""
        payment_method = values.get('payment_method')
        
        if payment_method in [PaymentMethod.CARD, PaymentMethod.DIGITAL_WALLET]:
            if not v:
                raise ValueError(f'Payment token required for {payment_method}')
        
        return v
    
    @validator('billing_name')
    def validate_billing_name(cls, v):
        """Clean and validate billing name"""
        if v is None:
            return v
        
        cleaned = ' '.join(v.strip().split())
        
        if not re.match(r'^[a-zA-Z\s\-\'\.]+$', cleaned):
            raise ValueError('Billing name contains invalid characters')
        
        return cleaned

# =====================================================
# MAIN ORDER SUBMISSION MODEL
# =====================================================

class OrderSubmissionRequest(BaseModel):
    """
    Complete order submission request from guest customers
    Contains all information needed to create and process an order
    """
    
    # Tenant and location information
    tenant_id: UUID = Field(
        ...,
        description="UUID of the restaurant/hotel tenant"
    )
    
    room_id: UUID = Field(
        ...,
        description="UUID of the room/table making the order"
    )
    
    # Customer information (optional for guests)
    customer_info: Optional[GuestCustomerInfo] = Field(
        None,
        description="Guest customer information (optional)"
    )
    
    # Order items (required)
    items: List[OrderItemRequest] = Field(
        ...,
        min_items=1,
        max_items=50,
        description="List of items being ordered"
    )
    
    # Delivery information
    delivery_info: Optional[DeliveryInfo] = Field(
        None,
        description="Delivery or pickup information"
    )
    
    # Payment information
    payment_info: PaymentInfo = Field(
        ...,
        description="Payment method and information"
    )
    
    # Order-level information
    order_notes: Optional[str] = Field(
        None,
        max_length=500,
        description="General notes for the entire order"
    )
    
    estimated_total: Decimal = Field(
        ...,
        ge=0,
        le=99999.99,
        decimal_places=2,
        description="Client-calculated total for validation"
    )
    
    # Metadata
    client_info: Optional[Dict[str, Any]] = Field(
        None,
        description="Client application information for debugging"
    )
    
    @validator('items')
    def validate_items_uniqueness(cls, v):
        """Ensure no duplicate menu items (same item can only appear once)"""
        if not v:
            return v
        
        seen_items = set()
        for item in v:
            if item.menu_item_id in seen_items:
                raise ValueError(f'Duplicate menu item ID: {item.menu_item_id}')
            seen_items.add(item.menu_item_id)
        
        return v
    
    @validator('estimated_total')
    def validate_estimated_total(cls, v, values):
        """Validate estimated total matches sum of items"""
        items = values.get('items', [])
        
        if not items:
            return v
        
        # Calculate total from items
        calculated_total = Decimal('0')
        for item in items:
            item_total = item.unit_price * item.quantity
            calculated_total += item_total
        
        # Allow small variance for rounding differences
        variance = abs(calculated_total - v)
        max_variance = Decimal('0.05')  # 5 cents tolerance
        
        if variance > max_variance:
            raise ValueError(
                f'Estimated total {v} does not match calculated total {calculated_total}'
            )
        
        return v
    
    @validator('order_notes')
    def validate_order_notes(cls, v):
        """Clean and validate order notes"""
        if v is None:
            return v
        
        cleaned = ' '.join(v.strip().split())
        
        # Check for potential security issues
        if any(char in cleaned for char in ['<', '>', '{', '}', '[', ']']):
            raise ValueError('Order notes contain invalid characters')
        
        return cleaned if cleaned else None
    
    @root_validator
    def validate_delivery_payment_consistency(cls, values):
        """Validate delivery and payment information consistency"""
        delivery_info = values.get('delivery_info')
        payment_info = values.get('payment_info')
        
        if not payment_info:
            return values
        
        # If room service delivery, room charge should be available
        if (delivery_info and delivery_info.room_number and 
            payment_info.payment_method == PaymentMethod.ROOM_CHARGE):
            # This is valid - room service with room charge
            pass
        elif payment_info.payment_method == PaymentMethod.ROOM_CHARGE and not delivery_info:
            raise ValueError('Room charge payment requires delivery information')
        
        return values

# =====================================================
# RESPONSE MODELS
# =====================================================

class OrderItemResponse(BaseModel):
    """Response model for individual order items"""
    
    id: UUID
    menu_item_id: UUID
    menu_item_name: str
    quantity: int
    unit_price: Decimal
    total_price: Decimal
    status: OrderItemStatus
    customizations: Optional[OrderItemCustomization]
    notes: Optional[str]

class OrderSubmissionResponse(BaseModel):
    """
    Response after successful order submission
    Contains order confirmation details and next steps
    """
    
    # Order identification
    order_id: UUID = Field(..., description="Unique order ID")
    order_number: str = Field(..., description="Human-readable order number")
    
    # Order status
    status: OrderStatus = Field(..., description="Current order status")
    
    # Timing information
    placed_at: datetime = Field(..., description="When order was placed")
    estimated_preparation_time: int = Field(..., description="Estimated prep time in minutes")
    estimated_ready_time: datetime = Field(..., description="Estimated ready time")
    
    # Financial information
    subtotal: Decimal = Field(..., description="Order subtotal")
    tax_amount: Decimal = Field(..., description="Tax amount")
    tip_amount: Optional[Decimal] = Field(None, description="Tip amount")
    total_amount: Decimal = Field(..., description="Total order amount")
    
    # Order details
    items: List[OrderItemResponse] = Field(..., description="Confirmed order items")
    
    # Customer information
    customer_id: Optional[UUID] = Field(None, description="Customer ID if registered")
    
    # Communication
    confirmation_message: str = Field(..., description="Order confirmation message")
    tracking_url: Optional[str] = Field(None, description="Order tracking URL")
    
    # Metadata
    tenant_name: str = Field(..., description="Restaurant/hotel name")
    room_number: Optional[str] = Field(None, description="Room/table number")

class OrderSubmissionError(BaseModel):
    """Error response for failed order submissions"""
    
    error_code: str = Field(..., description="Machine-readable error code")
    error_message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    field_errors: Optional[Dict[str, List[str]]] = Field(None, description="Field-specific validation errors")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")
    request_id: Optional[str] = Field(None, description="Request ID for tracking")

# =====================================================
# VALIDATION HELPER FUNCTIONS
# =====================================================

def validate_menu_item_availability(item_id: UUID, quantity: int, tenant_id: UUID) -> Dict[str, Any]:
    """
    Validate that menu item is available and in sufficient quantity
    This would be called during order processing
    """
    # This is a placeholder - actual implementation would query database
    return {
        'is_available': True,
        'remaining_quantity': 10,
        'current_price': Decimal('15.99'),
        'estimated_prep_time': 15
    }

def calculate_order_totals(items: List[OrderItemRequest], tenant_id: UUID) -> Dict[str, Decimal]:
    """
    Calculate order totals including tax and fees
    """
    subtotal = sum(item.unit_price * item.quantity for item in items)
    
    # Tax calculation (would be tenant-specific)
    tax_rate = Decimal('0.08')  # 8% tax rate
    tax_amount = subtotal * tax_rate
    
    # Service fee (if applicable)
    service_fee = Decimal('0.00')
    
    total = subtotal + tax_amount + service_fee
    
    return {
        'subtotal': subtotal,
        'tax_amount': tax_amount,
        'service_fee': service_fee,
        'total': total
    }

def generate_order_number(tenant_id: UUID) -> str:
    """
    Generate human-readable order number
    Format: ORD-{YYMMDD}-{NNNN}
    """
    from datetime import datetime
    
    date_str = datetime.now().strftime('%y%m%d')
    # In production, this would get the next sequence number from database
    sequence = 1
    
    return f"ORD-{date_str}-{sequence:04d}"
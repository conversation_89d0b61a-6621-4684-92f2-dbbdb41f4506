# BHEEMDINE Menu API Models
# Pydantic models for menu item data structures and validation

from datetime import datetime, time
from decimal import Decimal
from enum import Enum
from typing import List, Optional, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, validator, root_validator
from sqlalchemy import Column, String, Boolean, DateTime, Integer, Numeric, Text, ForeignKey, JSON
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

# =====================================================
# ENUMS
# =====================================================

class AllergenSeverity(str, Enum):
    """Allergen severity levels"""
    CONTAINS = "CONTAINS"         # Definitely contains this allergen
    MAY_CONTAIN = "MAY_CONTAIN"   # May contain traces
    TRACE = "TRACE"               # Processed in facility that handles this

class MenuCategory(str, Enum):
    """Standard menu categories"""
    APPETIZERS = "appetizers"
    MAINS = "mains"
    DESSERTS = "desserts"
    BEVERAGES = "beverages"
    SIDES = "sides"
    SPECIALS = "specials"

class DayOfWeek(str, Enum):
    """Days of the week for availability"""
    MONDAY = "monday"
    TUESDAY = "tuesday"
    WEDNESDAY = "wednesday"
    THURSDAY = "thursday"
    FRIDAY = "friday"
    SATURDAY = "saturday"
    SUNDAY = "sunday"

class PricingRuleType(str, Enum):
    """Types of dynamic pricing rules"""
    HAPPY_HOUR = "happy_hour"        # Time-based discounts
    BULK_DISCOUNT = "bulk_discount"  # Quantity-based discounts
    SEASONAL = "seasonal"            # Date-based pricing
    ROOM_TYPE = "room_type"          # Room-based pricing (VIP, regular)
    LOYALTY = "loyalty"              # Customer loyalty discounts

# =====================================================
# SQLALCHEMY MODELS (Database Tables)
# =====================================================

class MenuItem(Base):
    """Database model for menu items"""
    __tablename__ = "MenuItem"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True)
    tenantId = Column(PGUUID(as_uuid=True), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    category = Column(String(50), nullable=False)
    price = Column(Numeric(10, 2), nullable=False)
    imageUrl = Column(String(500))
    isAvailable = Column(Boolean, default=True)
    isVegetarian = Column(Boolean, default=False)
    isVegan = Column(Boolean, default=False)
    preparationTime = Column(Integer, default=15)
    sortOrder = Column(Integer, default=0)
    createdAt = Column(DateTime, default=datetime.utcnow)
    updatedAt = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Additional fields for dynamic pricing
    inventory = Column(Integer, default=100)  # Available quantity
    maxDailyQuantity = Column(Integer)        # Daily limit
    tags = Column(JSON)                       # Tags for filtering (spicy, popular, etc.)

class MenuItemAllergen(Base):
    """Database model for menu item allergens"""
    __tablename__ = "MenuItemAllergen"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True)
    menuItemId = Column(PGUUID(as_uuid=True), nullable=False)
    allergenId = Column(PGUUID(as_uuid=True), nullable=False)
    severity = Column(String(20), default="CONTAINS")

class Allergen(Base):
    """Database model for allergens"""
    __tablename__ = "Allergen"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True)
    tenantId = Column(PGUUID(as_uuid=True), nullable=False)
    name = Column(String(100), nullable=False)
    icon = Column(String(100))

class PricingRule(Base):
    """Database model for dynamic pricing rules"""
    __tablename__ = "PricingRule"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True)
    tenantId = Column(PGUUID(as_uuid=True), nullable=False)
    name = Column(String(255), nullable=False)
    ruleType = Column(String(50), nullable=False)
    isActive = Column(Boolean, default=True)
    
    # Rule conditions (stored as JSON)
    conditions = Column(JSON)  # e.g., {"start_time": "17:00", "end_time": "19:00"}
    
    # Pricing adjustments
    discountPercentage = Column(Numeric(5, 2))  # e.g., 10.00 for 10%
    discountAmount = Column(Numeric(10, 2))     # Fixed amount discount
    markupPercentage = Column(Numeric(5, 2))    # e.g., 20.00 for 20% markup
    
    # Applicability
    applicableCategories = Column(JSON)  # Categories this rule applies to
    applicableItems = Column(JSON)       # Specific items this rule applies to
    
    validFrom = Column(DateTime)
    validTo = Column(DateTime)
    
    createdAt = Column(DateTime, default=datetime.utcnow)

# =====================================================
# PYDANTIC RESPONSE MODELS
# =====================================================

class AllergenInfo(BaseModel):
    """Allergen information for menu items"""
    id: UUID
    name: str
    icon: Optional[str] = None
    severity: AllergenSeverity
    
    class Config:
        from_attributes = True

class PricingInfo(BaseModel):
    """Dynamic pricing information"""
    basePrice: Decimal = Field(..., description="Original menu price")
    currentPrice: Decimal = Field(..., description="Current price after adjustments")
    discountPercentage: Optional[Decimal] = Field(None, description="Applied discount percentage")
    discountAmount: Optional[Decimal] = Field(None, description="Applied discount amount")
    appliedRules: List[str] = Field(default_factory=list, description="Names of applied pricing rules")
    
    @validator('currentPrice', 'basePrice')
    def validate_price(cls, v):
        """Ensure prices are positive and have max 2 decimal places"""
        if v < 0:
            raise ValueError('Price must be positive')
        return round(v, 2)

class AvailabilityInfo(BaseModel):
    """Availability information for menu items"""
    isAvailable: bool = Field(..., description="Whether item is currently available")
    remainingQuantity: Optional[int] = Field(None, description="Remaining inventory")
    estimatedWaitTime: Optional[int] = Field(None, description="Estimated preparation time in minutes")
    unavailableReason: Optional[str] = Field(None, description="Reason why item is unavailable")
    availableAgainAt: Optional[datetime] = Field(None, description="When item will be available again")

class MenuItemResponse(BaseModel):
    """Complete menu item response with all details"""
    id: UUID
    name: str
    description: Optional[str] = None
    category: str
    
    # Pricing information
    pricing: PricingInfo
    
    # Availability information  
    availability: AvailabilityInfo
    
    # Item details
    imageUrl: Optional[str] = None
    isVegetarian: bool = False
    isVegan: bool = False
    preparationTime: int = 15
    tags: List[str] = Field(default_factory=list)
    
    # Allergen information
    allergens: List[AllergenInfo] = Field(default_factory=list)
    
    # Metadata
    sortOrder: int = 0
    updatedAt: datetime
    
    class Config:
        from_attributes = True

# =====================================================
# REQUEST MODELS
# =====================================================

class MenuFilters(BaseModel):
    """Filters for menu item queries"""
    
    # Category filtering
    categories: Optional[List[MenuCategory]] = Field(
        None, 
        description="Filter by menu categories",
        example=["mains", "appetizers"]
    )
    
    # Dietary preferences
    vegetarian_only: Optional[bool] = Field(
        None, 
        description="Show only vegetarian items"
    )
    vegan_only: Optional[bool] = Field(
        None, 
        description="Show only vegan items"
    )
    
    # Allergen filtering
    exclude_allergens: Optional[List[str]] = Field(
        None, 
        description="Exclude items containing these allergens",
        example=["peanuts", "dairy", "gluten"]
    )
    
    # Price filtering
    min_price: Optional[Decimal] = Field(
        None, 
        ge=0, 
        description="Minimum price filter"
    )
    max_price: Optional[Decimal] = Field(
        None, 
        ge=0, 
        description="Maximum price filter"
    )
    
    # Availability filtering
    available_only: bool = Field(
        True, 
        description="Show only available items"
    )
    
    # Time-based filtering
    max_prep_time: Optional[int] = Field(
        None, 
        ge=0, 
        description="Maximum preparation time in minutes"
    )
    
    # Search
    search_query: Optional[str] = Field(
        None, 
        description="Search in item name and description",
        min_length=2
    )
    
    # Tags
    tags: Optional[List[str]] = Field(
        None, 
        description="Filter by tags (e.g., 'spicy', 'popular')",
        example=["spicy", "chef-special"]
    )
    
    @validator('max_price')
    def validate_price_range(cls, v, values):
        """Ensure max_price is greater than min_price"""
        min_price = values.get('min_price')
        if min_price is not None and v is not None and v < min_price:
            raise ValueError('max_price must be greater than min_price')
        return v

class PricingContext(BaseModel):
    """Context for dynamic pricing calculations"""
    
    # Customer context
    room_id: Optional[UUID] = Field(None, description="Room ID for room-based pricing")
    customer_type: Optional[str] = Field(None, description="Customer type (vip, regular, loyalty)")
    customer_id: Optional[UUID] = Field(None, description="Customer ID for loyalty pricing")
    
    # Order context
    order_quantity: int = Field(1, ge=1, description="Quantity being ordered")
    current_order_total: Optional[Decimal] = Field(None, description="Current order total for bulk discounts")
    
    # Time context
    request_time: Optional[datetime] = Field(
        default_factory=datetime.utcnow,
        description="Time of request for time-based pricing"
    )
    
    # Special contexts
    special_event: Optional[str] = Field(None, description="Special event code")
    promo_code: Optional[str] = Field(None, description="Promotional code")

class MenuRequest(BaseModel):
    """Complete menu request with filters and context"""
    
    # Required tenant identification
    tenant_id: UUID = Field(..., description="Tenant (restaurant/hotel) ID")
    
    # Optional filtering
    filters: Optional[MenuFilters] = Field(None, description="Menu item filters")
    
    # Pricing context for dynamic pricing
    pricing_context: Optional[PricingContext] = Field(None, description="Context for dynamic pricing")
    
    # Pagination
    page: int = Field(1, ge=1, description="Page number (1-based)")
    page_size: int = Field(50, ge=1, le=100, description="Items per page (max 100)")
    
    # Sorting
    sort_by: Optional[str] = Field(
        "sortOrder", 
        description="Sort field",
        regex="^(name|price|category|preparationTime|sortOrder|popularity)$"
    )
    sort_order: Optional[str] = Field(
        "asc", 
        description="Sort order", 
        regex="^(asc|desc)$"
    )

# =====================================================
# RESPONSE MODELS
# =====================================================

class MenuResponse(BaseModel):
    """Complete menu response with pagination"""
    
    # Menu items
    items: List[MenuItemResponse] = Field(..., description="Menu items")
    
    # Pagination info
    total_items: int = Field(..., description="Total number of items matching filters")
    total_pages: int = Field(..., description="Total number of pages")
    current_page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Items per page")
    
    # Request context
    applied_filters: Dict[str, Any] = Field(..., description="Filters that were applied")
    pricing_context: Optional[PricingContext] = Field(None, description="Pricing context used")
    
    # Metadata
    request_time: datetime = Field(default_factory=datetime.utcnow)
    response_time_ms: Optional[float] = Field(None, description="Response time in milliseconds")
    
    # Categories available in this tenant
    available_categories: List[str] = Field(..., description="All categories available for this tenant")
    
    # Active pricing rules
    active_pricing_rules: List[str] = Field(default_factory=list, description="Currently active pricing rules")

class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error message")
    error_code: str = Field(..., description="Error code for client handling")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str] = Field(None, description="Request ID for tracking")
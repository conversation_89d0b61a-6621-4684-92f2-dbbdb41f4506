# BHEEMDINE Prisma Database Client
# Handles database connections and operations with proper connection management

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import <PERSON><PERSON>, AsyncGenerator
from prisma import Prisma
from prisma.models import (
    Tenant, User, Room, MenuItem, Order, OrderItem, 
    Staff, Allergen, MenuItemAllergen, OrderEvent
)
import os

# Configure logging
logger = logging.getLogger(__name__)

class PrismaClient:
    """
    Singleton Prisma client with connection management
    Handles database connections, transactions, and cleanup
    """
    
    _instance: Optional['PrismaClient'] = None
    _client: Optional[Prisma] = None
    _connection_lock = asyncio.Lock()
    
    def __new__(cls) -> 'PrismaClient':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    async def connect(self) -> None:
        """
        Initialize database connection
        Called once at application startup
        """
        async with self._connection_lock:
            if self._client is None:
                try:
                    self._client = Prisma()
                    await self._client.connect()
                    logger.info("Successfully connected to database")
                except Exception as e:
                    logger.error(f"Failed to connect to database: {e}")
                    raise
    
    async def disconnect(self) -> None:
        """
        Close database connection
        Called at application shutdown
        """
        async with self._connection_lock:
            if self._client is not None:
                try:
                    await self._client.disconnect()
                    self._client = None
                    logger.info("Successfully disconnected from database")
                except Exception as e:
                    logger.error(f"Error disconnecting from database: {e}")
    
    @property
    def client(self) -> Prisma:
        """
        Get the Prisma client instance
        Raises exception if not connected
        """
        if self._client is None:
            raise RuntimeError("Database not connected. Call connect() first.")
        return self._client
    
    @asynccontextmanager
    async def transaction(self) -> AsyncGenerator[Prisma, None]:
        """
        Context manager for database transactions
        Automatically handles commit/rollback
        
        Usage:
            async with db.transaction() as tx:
                await tx.order.create(...)
                await tx.orderitem.create_many(...)
        """
        client = self.client
        async with client.tx() as transaction:
            try:
                yield transaction
            except Exception as e:
                logger.error(f"Transaction failed: {e}")
                raise

# Global database instance
db = PrismaClient()

# =====================================================
# DATABASE OPERATIONS FOR ORDER SUBMISSION
# =====================================================

class OrderRepository:
    """
    Repository pattern for order-related database operations
    Encapsulates all order submission database logic
    """
    
    def __init__(self, client: Prisma):
        self.client = client
    
    async def validate_tenant_and_room(self, tenant_id: str, room_id: str) -> dict:
        """
        Validate that tenant exists and room belongs to tenant
        
        Args:
            tenant_id: UUID string of the tenant
            room_id: UUID string of the room
            
        Returns:
            Dict with tenant and room information
            
        Raises:
            ValueError: If tenant or room not found or invalid
        """
        try:
            # Fetch tenant and room in parallel
            tenant_task = self.client.tenant.find_unique(
                where={'id': tenant_id},
                include={'rooms': False}  # Don't load all rooms
            )
            
            room_task = self.client.room.find_unique(
                where={'id': room_id},
                include={'tenant': True}
            )
            
            tenant, room = await asyncio.gather(tenant_task, room_task)
            
            # Validate tenant exists and is active
            if not tenant:
                raise ValueError(f"Tenant {tenant_id} not found")
            
            if not tenant.isActive or tenant.isDeleted:
                raise ValueError(f"Tenant {tenant_id} is not active")
            
            # Validate room exists and belongs to tenant
            if not room:
                raise ValueError(f"Room {room_id} not found")
            
            if room.tenantId != tenant_id:
                raise ValueError(f"Room {room_id} does not belong to tenant {tenant_id}")
            
            if room.status not in ['AVAILABLE', 'OCCUPIED']:
                raise ValueError(f"Room {room_id} is not available for orders (status: {room.status})")
            
            return {
                'tenant': {
                    'id': tenant.id,
                    'name': tenant.name,
                    'slug': tenant.slug,
                    'settings': tenant.settings
                },
                'room': {
                    'id': room.id,
                    'roomNumber': room.roomNumber,
                    'floor': room.floor,
                    'capacity': room.capacity
                }
            }
            
        except Exception as e:
            logger.error(f"Error validating tenant and room: {e}")
            raise
    
    async def validate_menu_items(self, tenant_id: str, item_requests: list) -> dict:
        """
        Validate menu items exist, are available, and prices are correct
        
        Args:
            tenant_id: UUID string of the tenant
            item_requests: List of OrderItemRequest objects
            
        Returns:
            Dict with validated menu items and totals
            
        Raises:
            ValueError: If any menu item is invalid or unavailable
        """
        try:
            # Extract menu item IDs
            menu_item_ids = [str(item.menu_item_id) for item in item_requests]
            
            # Fetch all menu items in one query
            menu_items = await self.client.menuitem.find_many(
                where={
                    'id': {'in': menu_item_ids},
                    'tenantId': tenant_id
                },
                include={
                    'allergens': {
                        'include': {
                            'allergen': True
                        }
                    }
                }
            )
            
            # Create lookup map
            menu_items_map = {item.id: item for item in menu_items}
            
            # Validate each requested item
            validated_items = []
            total_amount = 0
            
            for item_request in item_requests:
                item_id = str(item_request.menu_item_id)
                
                # Check if item exists
                if item_id not in menu_items_map:
                    raise ValueError(f"Menu item {item_id} not found")
                
                menu_item = menu_items_map[item_id]
                
                # Check if item is available
                if not menu_item.isAvailable:
                    raise ValueError(f"Menu item '{menu_item.name}' is not available")
                
                # Validate price (allow small variance for dynamic pricing)
                current_price = float(menu_item.price)
                submitted_price = float(item_request.unit_price)
                price_variance = abs(current_price - submitted_price)
                
                if price_variance > 0.05:  # 5 cent tolerance
                    raise ValueError(
                        f"Price mismatch for '{menu_item.name}': "
                        f"current ${current_price:.2f}, submitted ${submitted_price:.2f}"
                    )
                
                # Check inventory if available
                if menu_item.inventory is not None and menu_item.inventory < item_request.quantity:
                    raise ValueError(
                        f"Insufficient inventory for '{menu_item.name}': "
                        f"requested {item_request.quantity}, available {menu_item.inventory}"
                    )
                
                # Calculate item total
                item_total = current_price * item_request.quantity
                total_amount += item_total
                
                validated_items.append({
                    'menu_item': menu_item,
                    'quantity': item_request.quantity,
                    'unit_price': current_price,
                    'total_price': item_total,
                    'customizations': item_request.customizations,
                    'notes': item_request.notes
                })
            
            return {
                'items': validated_items,
                'subtotal': total_amount,
                'total_prep_time': max(
                    (item['menu_item'].preparationTime * item['quantity']) 
                    for item in validated_items
                )
            }
            
        except Exception as e:
            logger.error(f"Error validating menu items: {e}")
            raise
    
    async def create_or_get_customer(self, tenant_id: str, room_id: str, customer_info: dict = None) -> str:
        """
        Create a guest customer or get existing customer
        
        Args:
            tenant_id: UUID string of the tenant
            room_id: UUID string of the room
            customer_info: Optional customer information
            
        Returns:
            Customer ID (UUID string)
        """
        try:
            # For guest orders, we create a temporary user record
            customer_data = {
                'tenantId': tenant_id,
                'roomId': room_id,
                'isGuest': True,
                'name': 'Guest User',
                'email': 'guest@local',
                'phone': '0000000000'
            }
            
            # Update with provided customer info if available
            if customer_info:
                if customer_info.name:
                    customer_data['name'] = customer_info.name
                if customer_info.email:
                    customer_data['email'] = customer_info.email
                if customer_info.phone:
                    customer_data['phone'] = customer_info.phone
            
            # Check if guest user already exists for this room
            existing_user = await self.client.user.find_first(
                where={
                    'tenantId': tenant_id,
                    'roomId': room_id,
                    'isGuest': True,
                    'email': customer_data['email']
                }
            )
            
            if existing_user:
                return existing_user.id
            
            # Create new guest user
            new_user = await self.client.user.create(
                data=customer_data
            )
            
            return new_user.id
            
        except Exception as e:
            logger.error(f"Error creating customer: {e}")
            raise
    
    async def create_order_with_items(
        self, 
        tenant_id: str, 
        user_id: str, 
        room_id: str,
        validated_items: list,
        totals: dict,
        order_notes: str = None,
        delivery_info: dict = None
    ) -> dict:
        """
        Create order and order items in a transaction
        
        Args:
            tenant_id: UUID string of the tenant
            user_id: UUID string of the customer
            room_id: UUID string of the room
            validated_items: List of validated menu items
            totals: Dictionary with calculated totals
            order_notes: Optional order notes
            delivery_info: Optional delivery information
            
        Returns:
            Created order with items
        """
        try:
            async with self.client.tx() as transaction:
                # Generate order number
                order_number = await self._generate_order_number(transaction, tenant_id)
                
                # Calculate totals
                subtotal = totals['subtotal']
                tax_rate = 0.08  # 8% tax rate (would be tenant-specific)
                tax_amount = subtotal * tax_rate
                total_amount = subtotal + tax_amount
                
                # Create order
                order_data = {
                    'tenantId': tenant_id,
                    'userId': user_id,
                    'roomId': room_id,
                    'orderNumber': order_number,
                    'status': 'PENDING',
                    'totalAmount': float(total_amount),
                    'notes': order_notes
                }
                
                order = await transaction.order.create(data=order_data)
                
                # Create order items
                order_items_data = []
                for item in validated_items:
                    item_data = {
                        'orderId': order.id,
                        'menuItemId': item['menu_item'].id,
                        'quantity': item['quantity'],
                        'unitPrice': item['unit_price'],
                        'notes': item['notes'],
                        'status': 'PENDING'
                    }
                    order_items_data.append(item_data)
                
                order_items = await transaction.orderitem.create_many(
                    data=order_items_data
                )
                
                # Create order event for audit trail
                await transaction.orderevent.create(
                    data={
                        'tenantId': tenant_id,
                        'orderId': order.id,
                        'eventType': 'STATUS_CHANGED',
                        'toStatus': 'PENDING',
                        'metadata': {
                            'source': 'guest_submission',
                            'item_count': len(validated_items),
                            'total_amount': float(total_amount)
                        }
                    }
                )
                
                # Update room status to occupied if not already
                await transaction.room.update(
                    where={'id': room_id},
                    data={'status': 'OCCUPIED'}
                )
                
                # Update menu item inventory
                for item in validated_items:
                    if item['menu_item'].inventory is not None:
                        await transaction.menuitem.update(
                            where={'id': item['menu_item'].id},
                            data={
                                'inventory': {
                                    'decrement': item['quantity']
                                }
                            }
                        )
                
                return {
                    'order': order,
                    'order_items': order_items_data,
                    'subtotal': subtotal,
                    'tax_amount': tax_amount,
                    'total_amount': total_amount,
                    'estimated_prep_time': totals.get('total_prep_time', 30)
                }
                
        except Exception as e:
            logger.error(f"Error creating order: {e}")
            raise
    
    async def _generate_order_number(self, transaction: Prisma, tenant_id: str) -> str:
        """
        Generate unique order number for tenant
        Format: ORD-YYMMDD-NNNN
        """
        from datetime import datetime
        
        date_str = datetime.now().strftime('%y%m%d')
        
        # Get today's order count for this tenant
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        order_count = await transaction.order.count(
            where={
                'tenantId': tenant_id,
                'placedAt': {
                    'gte': today_start
                }
            }
        )
        
        sequence = order_count + 1
        return f"ORD-{date_str}-{sequence:04d}"

# =====================================================
# DATABASE UTILITY FUNCTIONS
# =====================================================

async def get_db() -> Prisma:
    """
    Dependency for FastAPI to get database client
    Ensures connection is established
    """
    if db._client is None:
        await db.connect()
    return db.client

async def create_order_repository() -> OrderRepository:
    """
    Factory function to create OrderRepository with database client
    """
    client = await get_db()
    return OrderRepository(client)

# =====================================================
# DATABASE HEALTH CHECK
# =====================================================

async def check_database_health() -> dict:
    """
    Check database connectivity and return health status
    Used for application health checks
    """
    try:
        client = await get_db()
        
        # Simple query to test connection
        tenant_count = await client.tenant.count()
        
        return {
            'status': 'healthy',
            'connected': True,
            'tenant_count': tenant_count,
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            'status': 'unhealthy',
            'connected': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

# =====================================================
# CONNECTION LIFECYCLE MANAGEMENT
# =====================================================

async def startup_database():
    """
    Initialize database connection at application startup
    """
    try:
        await db.connect()
        logger.info("Database startup completed")
    except Exception as e:
        logger.error(f"Database startup failed: {e}")
        raise

async def shutdown_database():
    """
    Close database connection at application shutdown
    """
    try:
        await db.disconnect()
        logger.info("Database shutdown completed")
    except Exception as e:
        logger.error(f"Database shutdown error: {e}")

# =====================================================
# PRISMA QUERY HELPERS
# =====================================================

def build_order_include():
    """
    Standard include for order queries with all related data
    """
    return {
        'user': True,
        'room': {
            'include': {
                'tenant': True
            }
        },
        'items': {
            'include': {
                'menuItem': {
                    'include': {
                        'allergens': {
                            'include': {
                                'allergen': True
                            }
                        }
                    }
                }
            }
        },
        'events': {
            'orderBy': {
                'createdAt': 'desc'
            },
            'take': 5  # Latest 5 events
        }
    }

def build_menu_item_include():
    """
    Standard include for menu item queries
    """
    return {
        'allergens': {
            'include': {
                'allergen': True
            }
        }
    }
# BHEEMDINE WhatsApp Notification Service
# Real-time WhatsApp notifications for TapDine orders using Twilio

import asyncio
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Union
from uuid import UUID

from twilio.rest import Client
from twilio.base.exceptions import TwilioException
import httpx

# Configure logging
logger = logging.getLogger(__name__)

class WhatsAppNotificationError(Exception):
    """Custom exception for WhatsApp notification errors"""
    
    def __init__(self, message: str, error_code: str = "NOTIFICATION_ERROR", details: Dict = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(message)

class WhatsAppNotificationService:
    """
    Service for sending WhatsApp notifications using Twilio
    Handles order notifications to staff, kitchen, and customers
    """
    
    def __init__(self, 
                 account_sid: str = None, 
                 auth_token: str = None, 
                 from_number: str = None):
        """
        Initialize WhatsApp notification service
        
        Args:
            account_sid: Twilio Account SID (optional, can use env var)
            auth_token: Twilio Auth Token (optional, can use env var) 
            from_number: Twilio WhatsApp from number (optional, can use env var)
        """
        
        # Use provided credentials or environment variables
        self.account_sid = account_sid or os.getenv('TWILIO_ACCOUNT_SID')
        self.auth_token = auth_token or os.getenv('TWILIO_AUTH_TOKEN')
        self.from_number = from_number or os.getenv('TWILIO_WHATSAPP_FROM')
        
        # Validate configuration
        if not all([self.account_sid, self.auth_token, self.from_number]):
            raise WhatsAppNotificationError(
                "Missing Twilio configuration. Ensure TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, and TWILIO_WHATSAPP_FROM are set",
                "CONFIGURATION_ERROR"
            )
        
        # Initialize Twilio client
        try:
            self.client = Client(self.account_sid, self.auth_token)
        except Exception as e:
            raise WhatsAppNotificationError(
                f"Failed to initialize Twilio client: {str(e)}",
                "CLIENT_INITIALIZATION_ERROR"
            )
        
        # Default notification settings
        self.max_retries = 3
        self.retry_delay = 5  # seconds
        self.timeout = 30  # seconds
        
        logger.info("WhatsApp notification service initialized successfully")
    
    async def send_new_order_notification(self, 
                                        order_data: Dict, 
                                        staff_phones: List[str],
                                        tenant_info: Dict = None) -> Dict:
        """
        Send new order notification to assigned staff
        
        Args:
            order_data: Order information including items, customer, etc.
            staff_phones: List of staff phone numbers to notify
            tenant_info: Optional tenant/restaurant information
            
        Returns:
            Dict with notification results
        """
        
        try:
            # Build order notification message
            message = self._build_new_order_message(order_data, tenant_info)
            
            # Send notifications to all staff members
            results = []
            for phone in staff_phones:
                try:
                    result = await self._send_whatsapp_message(
                        to_number=phone,
                        message=message,
                        message_type="new_order"
                    )
                    results.append({
                        "phone": phone,
                        "status": "sent",
                        "message_sid": result.get("sid"),
                        "sent_at": datetime.utcnow().isoformat()
                    })
                    
                except Exception as e:
                    logger.error(f"Failed to send notification to {phone}: {str(e)}")
                    results.append({
                        "phone": phone,
                        "status": "failed",
                        "error": str(e),
                        "attempted_at": datetime.utcnow().isoformat()
                    })
            
            # Log summary
            successful = len([r for r in results if r["status"] == "sent"])
            total = len(results)
            
            logger.info(
                f"New order notification sent to {successful}/{total} staff members "
                f"for order {order_data.get('order_number', 'N/A')}"
            )
            
            return {
                "order_id": order_data.get("order_id"),
                "order_number": order_data.get("order_number"),
                "notification_type": "new_order",
                "total_recipients": total,
                "successful_sends": successful,
                "results": results,
                "sent_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to send new order notifications: {str(e)}")
            raise WhatsAppNotificationError(
                f"Failed to send order notifications: {str(e)}",
                "NOTIFICATION_SEND_ERROR",
                {"order_id": order_data.get("order_id")}
            )
    
    async def send_order_status_update(self, 
                                     order_data: Dict, 
                                     customer_phone: str, 
                                     new_status: str,
                                     estimated_time: Optional[str] = None) -> Dict:
        """
        Send order status update to customer
        
        Args:
            order_data: Order information
            customer_phone: Customer's phone number
            new_status: New order status (CONFIRMED, PREPARING, READY, etc.)
            estimated_time: Optional estimated completion time
            
        Returns:
            Dict with notification result
        """
        
        try:
            # Build status update message
            message = self._build_status_update_message(order_data, new_status, estimated_time)
            
            # Send notification to customer
            result = await self._send_whatsapp_message(
                to_number=customer_phone,
                message=message,
                message_type="status_update"
            )
            
            logger.info(
                f"Status update sent to customer for order {order_data.get('order_number', 'N/A')}: {new_status}"
            )
            
            return {
                "order_id": order_data.get("order_id"),
                "order_number": order_data.get("order_number"),
                "notification_type": "status_update",
                "status": new_status,
                "customer_phone": customer_phone,
                "message_sid": result.get("sid"),
                "sent_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to send status update: {str(e)}")
            raise WhatsAppNotificationError(
                f"Failed to send status update: {str(e)}",
                "STATUS_UPDATE_ERROR",
                {"order_id": order_data.get("order_id")}
            )
    
    async def send_kitchen_notification(self, 
                                      order_data: Dict, 
                                      kitchen_phones: List[str],
                                      priority: str = "normal") -> Dict:
        """
        Send notification to kitchen staff about new orders
        
        Args:
            order_data: Order information with items and special instructions
            kitchen_phones: List of kitchen staff phone numbers
            priority: Order priority (normal, urgent, special)
            
        Returns:
            Dict with notification results
        """
        
        try:
            # Build kitchen notification message
            message = self._build_kitchen_notification_message(order_data, priority)
            
            # Send notifications to kitchen staff
            results = []
            for phone in kitchen_phones:
                try:
                    result = await self._send_whatsapp_message(
                        to_number=phone,
                        message=message,
                        message_type="kitchen_notification"
                    )
                    results.append({
                        "phone": phone,
                        "status": "sent",
                        "message_sid": result.get("sid"),
                        "sent_at": datetime.utcnow().isoformat()
                    })
                    
                except Exception as e:
                    logger.error(f"Failed to send kitchen notification to {phone}: {str(e)}")
                    results.append({
                        "phone": phone,
                        "status": "failed",
                        "error": str(e),
                        "attempted_at": datetime.utcnow().isoformat()
                    })
            
            successful = len([r for r in results if r["status"] == "sent"])
            total = len(results)
            
            logger.info(
                f"Kitchen notification sent to {successful}/{total} staff members "
                f"for order {order_data.get('order_number', 'N/A')}"
            )
            
            return {
                "order_id": order_data.get("order_id"),
                "order_number": order_data.get("order_number"),
                "notification_type": "kitchen_notification",
                "priority": priority,
                "total_recipients": total,
                "successful_sends": successful,
                "results": results,
                "sent_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to send kitchen notifications: {str(e)}")
            raise WhatsAppNotificationError(
                f"Failed to send kitchen notifications: {str(e)}",
                "KITCHEN_NOTIFICATION_ERROR",
                {"order_id": order_data.get("order_id")}
            )
    
    async def _send_whatsapp_message(self, 
                                   to_number: str, 
                                   message: str, 
                                   message_type: str = "general") -> Dict:
        """
        Send a WhatsApp message via Twilio with retry logic
        
        Args:
            to_number: Recipient phone number (must include country code)
            message: Message content
            message_type: Type of message for logging
            
        Returns:
            Dict with message sending result
        """
        
        # Ensure proper WhatsApp format
        if not to_number.startswith("whatsapp:"):
            # Ensure phone number has country code
            if not to_number.startswith("+"):
                to_number = f"+{to_number}"
            to_number = f"whatsapp:{to_number}"
        
        # Ensure from number is in WhatsApp format
        from_number = self.from_number
        if not from_number.startswith("whatsapp:"):
            if not from_number.startswith("+"):
                from_number = f"+{from_number}"
            from_number = f"whatsapp:{from_number}"
        
        # Attempt to send message with retries
        last_error = None
        for attempt in range(self.max_retries):
            try:
                # Send message via Twilio
                message_instance = self.client.messages.create(
                    body=message,
                    from_=from_number,
                    to=to_number
                )
                
                logger.info(
                    f"WhatsApp message sent successfully: {message_type} to {to_number}, "
                    f"SID: {message_instance.sid}"
                )
                
                return {
                    "sid": message_instance.sid,
                    "status": message_instance.status,
                    "to": to_number,
                    "from": from_number,
                    "message_type": message_type,
                    "attempt": attempt + 1
                }
                
            except TwilioException as e:
                last_error = e
                logger.warning(
                    f"Twilio error on attempt {attempt + 1}/{self.max_retries}: "
                    f"{e.code} - {e.msg}"
                )
                
                # Don't retry on certain error codes
                if e.code in [21211, 21614, 63016]:  # Invalid phone, unsubscribed, etc.
                    break
                
                # Wait before retry
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay)
                    
            except Exception as e:
                last_error = e
                logger.warning(f"Unexpected error on attempt {attempt + 1}/{self.max_retries}: {str(e)}")
                
                # Wait before retry
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay)
        
        # If we get here, all retries failed
        error_msg = f"Failed to send WhatsApp message after {self.max_retries} attempts: {str(last_error)}"
        logger.error(error_msg)
        raise WhatsAppNotificationError(
            error_msg,
            "MESSAGE_SEND_FAILED",
            {"to_number": to_number, "attempts": self.max_retries}
        )
    
    def _build_new_order_message(self, order_data: Dict, tenant_info: Dict = None) -> str:
        """
        Build new order notification message for staff
        
        Args:
            order_data: Order information
            tenant_info: Optional tenant information
            
        Returns:
            Formatted message string
        """
        
        restaurant_name = tenant_info.get('name', 'Restaurant') if tenant_info else 'Restaurant'
        order_number = order_data.get('order_number', 'N/A')
        room_number = order_data.get('room_number', 'N/A')
        customer_name = order_data.get('customer_name', 'Guest')
        total_amount = order_data.get('total_amount', 0)
        item_count = len(order_data.get('items', []))
        
        # Build items summary
        items_text = ""
        for item in order_data.get('items', [])[:5]:  # Show max 5 items
            qty = item.get('quantity', 1)
            name = item.get('menu_item_name', 'Item')
            items_text += f"• {qty}x {name}\n"
        
        if len(order_data.get('items', [])) > 5:
            items_text += f"... and {len(order_data.get('items', [])) - 5} more items\n"
        
        # Special instructions
        special_notes = ""
        order_notes = order_data.get('order_notes', '')
        if order_notes:
            special_notes = f"\n📝 *Special Instructions:*\n{order_notes}\n"
        
        message = f"""🚨 *NEW ORDER ALERT* 🚨

🏪 *{restaurant_name}*
📋 Order #{order_number}
🏠 Room: {room_number}
👤 Customer: {customer_name}
💰 Total: ${total_amount:.2f}

📦 *Items ({item_count}):*
{items_text}{special_notes}
⏰ Received: {datetime.now().strftime('%I:%M %p')}

Please confirm receipt and estimated preparation time."""
        
        return message
    
    def _build_status_update_message(self, 
                                   order_data: Dict, 
                                   new_status: str, 
                                   estimated_time: Optional[str] = None) -> str:
        """
        Build order status update message for customers
        
        Args:
            order_data: Order information
            new_status: New order status
            estimated_time: Optional estimated completion time
            
        Returns:
            Formatted message string
        """
        
        order_number = order_data.get('order_number', 'N/A')
        restaurant_name = order_data.get('tenant_name', 'Restaurant')
        
        # Status-specific messages
        status_messages = {
            'CONFIRMED': '✅ Your order has been confirmed and is being prepared.',
            'PREPARING': '👨‍🍳 Your order is currently being prepared.',
            'READY': '🎉 Your order is ready for pickup/delivery!',
            'OUT_FOR_DELIVERY': '🚚 Your order is out for delivery.',
            'DELIVERED': '✅ Your order has been delivered. Enjoy your meal!',
            'CANCELLED': '❌ Your order has been cancelled.'
        }
        
        status_text = status_messages.get(new_status, f'Status updated to: {new_status}')
        
        time_text = ""
        if estimated_time:
            time_text = f"\n⏱️ Estimated time: {estimated_time}"
        
        message = f"""📱 *Order Update* 📱

🏪 {restaurant_name}
📋 Order #{order_number}

{status_text}{time_text}

Thank you for your patience! 🙏"""
        
        return message
    
    def _build_kitchen_notification_message(self, order_data: Dict, priority: str = "normal") -> str:
        """
        Build kitchen notification message
        
        Args:
            order_data: Order information
            priority: Order priority level
            
        Returns:
            Formatted message string
        """
        
        order_number = order_data.get('order_number', 'N/A')
        room_number = order_data.get('room_number', 'N/A')
        
        # Priority emoji
        priority_emoji = {
            'urgent': '🔥',
            'special': '⭐',
            'normal': '📋'
        }.get(priority, '📋')
        
        # Build detailed items list
        items_text = ""
        for item in order_data.get('items', []):
            qty = item.get('quantity', 1)
            name = item.get('menu_item_name', 'Item')
            notes = item.get('notes', '')
            customizations = item.get('customizations', {})
            
            items_text += f"• {qty}x {name}"
            
            # Add customizations
            if customizations:
                mods = customizations.get('modifications', [])
                if mods:
                    items_text += f" ({', '.join(mods)})"
            
            # Add item notes
            if notes:
                items_text += f" - {notes}"
            
            items_text += "\n"
        
        # Special instructions
        special_notes = ""
        order_notes = order_data.get('order_notes', '')
        delivery_instructions = order_data.get('delivery_instructions', '')
        
        if order_notes:
            special_notes += f"\n📝 *Order Notes:* {order_notes}"
        if delivery_instructions:
            special_notes += f"\n🚚 *Delivery:* {delivery_instructions}"
        
        message = f"""{priority_emoji} *KITCHEN ORDER* {priority_emoji}

📋 Order #{order_number}
🏠 Room: {room_number}
⏰ Time: {datetime.now().strftime('%I:%M %p')}

🍽️ *Items:*
{items_text}{special_notes}

Please confirm and provide estimated prep time."""
        
        return message
    
    async def test_connection(self) -> Dict:
        """
        Test WhatsApp notification service connectivity
        
        Returns:
            Dict with test results
        """
        
        try:
            # Test Twilio account connectivity
            account = self.client.api.accounts(self.account_sid).fetch()
            
            # Test message sending capability (using a test number if available)
            test_result = {
                "status": "success",
                "account_sid": self.account_sid,
                "account_status": account.status,
                "from_number": self.from_number,
                "tested_at": datetime.utcnow().isoformat()
            }
            
            logger.info("WhatsApp notification service test passed")
            return test_result
            
        except Exception as e:
            logger.error(f"WhatsApp notification service test failed: {str(e)}")
            return {
                "status": "failed",
                "error": str(e),
                "tested_at": datetime.utcnow().isoformat()
            }

# =====================================================
# SERVICE FACTORY
# =====================================================

def create_whatsapp_service() -> WhatsAppNotificationService:
    """
    Factory function to create WhatsApp notification service
    """
    return WhatsAppNotificationService()

# =====================================================
# UTILITY FUNCTIONS
# =====================================================

def format_phone_number(phone: str) -> str:
    """
    Format phone number for WhatsApp
    
    Args:
        phone: Phone number string
        
    Returns:
        Formatted phone number
    """
    # Remove all non-digit characters except +
    cleaned = ''.join(c for c in phone if c.isdigit() or c == '+')
    
    # Ensure country code is present
    if not cleaned.startswith('+'):
        # Assume US number if no country code
        if len(cleaned) == 10:
            cleaned = f"+1{cleaned}"
        else:
            cleaned = f"+{cleaned}"
    
    return cleaned

def validate_phone_number(phone: str) -> bool:
    """
    Validate phone number format
    
    Args:
        phone: Phone number string
        
    Returns:
        True if valid, False otherwise
    """
    try:
        formatted = format_phone_number(phone)
        # Basic validation - should be at least 10 digits with country code
        return len(formatted) >= 12 and formatted.startswith('+')
    except:
        return False
# BHEEMDINE Availability Engine
# Handles menu item availability based on time, inventory, and business rules

import logging
from datetime import datetime, time, timedelta
from typing import List, Dict, Any, Optional
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from ..models.menu import MenuItem, AvailabilityInfo

logger = logging.getLogger(__name__)

class AvailabilityEngine:
    """
    Engine for determining menu item availability based on various factors:
    - Base availability flag
    - Inventory levels
    - Time-based availability (breakfast, lunch, dinner)
    - Kitchen capacity
    - Preparation time constraints
    """
    
    def __init__(self, db: Session):
        self.db = db
    
    def check_item_availability(
        self, 
        menu_item: MenuItem, 
        request_time: Optional[datetime] = None,
        requested_quantity: int = 1
    ) -> AvailabilityInfo:
        """
        Check if a menu item is available for ordering
        
        Args:
            menu_item: The menu item to check
            request_time: When the item is being requested (defaults to now)
            requested_quantity: How many items are being requested
            
        Returns:
            AvailabilityInfo object with availability status and details
        """
        if request_time is None:
            request_time = datetime.utcnow()
        
        logger.info(f"Checking availability for item {menu_item.id} at {request_time}")
        
        # Start with base availability
        if not menu_item.isAvailable:
            return AvailabilityInfo(
                isAvailable=False,
                unavailableReason="Item is temporarily unavailable",
                estimatedWaitTime=None
            )
        
        # Check inventory levels
        inventory_check = self._check_inventory(menu_item, requested_quantity)
        if not inventory_check['available']:
            return AvailabilityInfo(
                isAvailable=False,
                remainingQuantity=inventory_check['remaining'],
                unavailableReason=inventory_check['reason'],
                availableAgainAt=inventory_check.get('available_again_at')
            )
        
        # Check time-based availability
        time_check = self._check_time_based_availability(menu_item, request_time)
        if not time_check['available']:
            return AvailabilityInfo(
                isAvailable=False,
                unavailableReason=time_check['reason'],
                availableAgainAt=time_check.get('available_again_at')
            )
        
        # Check kitchen capacity and preparation constraints
        prep_check = self._check_preparation_constraints(menu_item, request_time)
        
        # Item is available
        return AvailabilityInfo(
            isAvailable=True,
            remainingQuantity=inventory_check['remaining'],
            estimatedWaitTime=prep_check['estimated_wait_time']
        )
    
    def _check_inventory(
        self, 
        menu_item: MenuItem, 
        requested_quantity: int
    ) -> Dict[str, Any]:
        """
        Check if sufficient inventory is available
        
        Args:
            menu_item: The menu item to check
            requested_quantity: Requested quantity
            
        Returns:
            Dictionary with availability info and remaining quantity
        """
        current_inventory = menu_item.inventory or 0
        
        # Check if we have enough inventory
        if current_inventory < requested_quantity:
            if current_inventory == 0:
                return {
                    'available': False,
                    'remaining': 0,
                    'reason': 'Out of stock',
                    'available_again_at': self._estimate_restock_time(menu_item)
                }
            else:
                return {
                    'available': False,
                    'remaining': current_inventory,
                    'reason': f'Only {current_inventory} remaining, requested {requested_quantity}'
                }
        
        # Check daily quantity limits
        if menu_item.maxDailyQuantity:
            daily_sold = self._get_daily_sold_quantity(menu_item)
            remaining_daily = menu_item.maxDailyQuantity - daily_sold
            
            if remaining_daily < requested_quantity:
                return {
                    'available': False,
                    'remaining': remaining_daily,
                    'reason': f'Daily limit reached. Only {remaining_daily} remaining today.',
                    'available_again_at': self._get_tomorrow_start()
                }
        
        return {
            'available': True,
            'remaining': current_inventory
        }
    
    def _check_time_based_availability(
        self, 
        menu_item: MenuItem, 
        request_time: datetime
    ) -> Dict[str, Any]:
        """
        Check if item is available at the current time
        
        This includes:
        - Breakfast/Lunch/Dinner time restrictions
        - Day of week restrictions
        - Special event availability
        """
        
        # Get item's time-based availability rules from tags or category
        availability_rules = self._get_time_availability_rules(menu_item)
        
        if not availability_rules:
            # No time restrictions
            return {'available': True}
        
        current_time = request_time.time()
        current_day = request_time.strftime('%A').lower()
        
        # Check day of week restrictions
        if 'days_of_week' in availability_rules:
            if current_day not in availability_rules['days_of_week']:
                next_available = self._find_next_available_day(
                    availability_rules['days_of_week'], 
                    request_time
                )
                return {
                    'available': False,
                    'reason': f'Not available on {current_day.title()}',
                    'available_again_at': next_available
                }
        
        # Check time of day restrictions
        if 'time_ranges' in availability_rules:
            is_in_time_range = False
            next_available_time = None
            
            for time_range in availability_rules['time_ranges']:
                start_time = time.fromisoformat(time_range['start'])
                end_time = time.fromisoformat(time_range['end'])
                
                if self._is_time_in_range(current_time, start_time, end_time):
                    is_in_time_range = True
                    break
                else:
                    # Calculate next available time
                    candidate_time = self._calculate_next_time_slot(
                        request_time, start_time, end_time
                    )
                    if next_available_time is None or candidate_time < next_available_time:
                        next_available_time = candidate_time
            
            if not is_in_time_range:
                return {
                    'available': False,
                    'reason': self._get_time_restriction_reason(availability_rules),
                    'available_again_at': next_available_time
                }
        
        return {'available': True}
    
    def _check_preparation_constraints(
        self, 
        menu_item: MenuItem, 
        request_time: datetime
    ) -> Dict[str, Any]:
        """
        Check kitchen capacity and estimate preparation time
        
        Args:
            menu_item: The menu item
            request_time: Current request time
            
        Returns:
            Dictionary with preparation info
        """
        base_prep_time = menu_item.preparationTime or 15
        
        # Get current kitchen load
        kitchen_load = self._get_current_kitchen_load(menu_item.tenantId)
        
        # Calculate additional wait time based on kitchen load
        additional_wait = 0
        if kitchen_load > 0.8:  # 80% capacity
            additional_wait = int(base_prep_time * 0.5)  # Add 50% more time
        elif kitchen_load > 0.6:  # 60% capacity
            additional_wait = int(base_prep_time * 0.25)  # Add 25% more time
        
        estimated_wait_time = base_prep_time + additional_wait
        
        return {
            'estimated_wait_time': estimated_wait_time,
            'kitchen_load': kitchen_load
        }
    
    def _get_time_availability_rules(self, menu_item: MenuItem) -> Optional[Dict[str, Any]]:
        """
        Get time-based availability rules for a menu item
        
        This could be stored in item tags, category rules, or a separate table
        """
        
        # Example rules based on category
        category_rules = {
            'breakfast': {
                'time_ranges': [
                    {'start': '06:00', 'end': '11:00'}
                ]
            },
            'lunch': {
                'time_ranges': [
                    {'start': '11:00', 'end': '16:00'}
                ]
            },
            'dinner': {
                'time_ranges': [
                    {'start': '17:00', 'end': '23:00'}
                ]
            },
            'beverages': {
                # Available all day
                'time_ranges': [
                    {'start': '06:00', 'end': '23:59'}
                ]
            },
            'bar': {
                'time_ranges': [
                    {'start': '16:00', 'end': '02:00'}
                ],
                'days_of_week': ['friday', 'saturday', 'sunday']
            }
        }
        
        # Check if item has specific tags that override category rules
        if menu_item.tags:
            tags = menu_item.tags if isinstance(menu_item.tags, list) else []
            
            if 'all_day' in tags:
                return None  # Available all day
            
            if 'weekends_only' in tags:
                return {
                    'days_of_week': ['friday', 'saturday', 'sunday']
                }
            
            if 'breakfast_only' in tags:
                return category_rules.get('breakfast')
        
        # Return category-based rules
        return category_rules.get(menu_item.category.lower())
    
    def _is_time_in_range(self, current_time: time, start_time: time, end_time: time) -> bool:
        """Check if current time is within the given range"""
        if start_time <= end_time:
            # Same day range
            return start_time <= current_time <= end_time
        else:
            # Overnight range (e.g., 22:00 to 02:00)
            return current_time >= start_time or current_time <= end_time
    
    def _calculate_next_time_slot(
        self, 
        current_datetime: datetime, 
        start_time: time, 
        end_time: time
    ) -> datetime:
        """Calculate when the item will next be available"""
        current_date = current_datetime.date()
        
        # Try today first
        start_datetime = datetime.combine(current_date, start_time)
        if start_datetime > current_datetime:
            return start_datetime
        
        # Try tomorrow
        tomorrow = current_date + timedelta(days=1)
        return datetime.combine(tomorrow, start_time)
    
    def _find_next_available_day(
        self, 
        available_days: List[str], 
        current_datetime: datetime
    ) -> datetime:
        """Find the next day when the item will be available"""
        current_day = current_datetime.strftime('%A').lower()
        
        # Map day names to numbers
        day_map = {
            'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
            'friday': 4, 'saturday': 5, 'sunday': 6
        }
        
        current_day_num = day_map[current_day]
        available_day_nums = [day_map[day] for day in available_days]
        
        # Find next available day
        for i in range(1, 8):  # Check next 7 days
            check_day = (current_day_num + i) % 7
            if check_day in available_day_nums:
                days_ahead = i
                next_date = current_datetime.date() + timedelta(days=days_ahead)
                return datetime.combine(next_date, time(6, 0))  # 6 AM next available day
        
        # Fallback (shouldn't happen if days are valid)
        return current_datetime + timedelta(days=1)
    
    def _get_daily_sold_quantity(self, menu_item: MenuItem) -> int:
        """Get quantity sold today for this menu item"""
        today = datetime.utcnow().date()
        
        # This would query OrderItem table to get today's sales
        # For now, return a mock value
        # 
        # daily_sold = (
        #     self.db.query(func.sum(OrderItem.quantity))
        #     .join(Order)
        #     .filter(
        #         OrderItem.menuItemId == menu_item.id,
        #         func.date(Order.placedAt) == today,
        #         Order.status != 'CANCELLED'
        #     )
        #     .scalar() or 0
        # )
        
        return 0  # Mock implementation
    
    def _get_current_kitchen_load(self, tenant_id: UUID) -> float:
        """
        Calculate current kitchen load as a percentage (0.0 to 1.0)
        
        This could be based on:
        - Number of active orders
        - Kitchen capacity
        - Staff availability
        """
        
        # Mock implementation - in real app, this would query active orders
        # and calculate based on preparation times and kitchen capacity
        
        # active_orders = (
        #     self.db.query(Order)
        #     .filter(
        #         Order.tenantId == tenant_id,
        #         Order.status.in_(['CONFIRMED', 'PREPARING'])
        #     )
        #     .count()
        # )
        
        # Assume max capacity is 20 concurrent orders
        # return min(active_orders / 20.0, 1.0)
        
        return 0.3  # Mock 30% load
    
    def _estimate_restock_time(self, menu_item: MenuItem) -> Optional[datetime]:
        """Estimate when item will be restocked"""
        
        # Simple logic: assume restock happens at 6 AM next day
        # In real implementation, this could be based on:
        # - Supplier delivery schedules
        # - Kitchen preparation schedules
        # - Inventory management system
        
        tomorrow = datetime.utcnow().date() + timedelta(days=1)
        return datetime.combine(tomorrow, time(6, 0))
    
    def _get_tomorrow_start(self) -> datetime:
        """Get start of tomorrow (for daily limit resets)"""
        tomorrow = datetime.utcnow().date() + timedelta(days=1)
        return datetime.combine(tomorrow, time(0, 0))
    
    def _get_time_restriction_reason(self, availability_rules: Dict[str, Any]) -> str:
        """Generate human-readable reason for time restriction"""
        
        if 'time_ranges' in availability_rules:
            ranges = availability_rules['time_ranges']
            if len(ranges) == 1:
                start = ranges[0]['start']
                end = ranges[0]['end']
                return f"Available only from {start} to {end}"
            else:
                time_list = [f"{r['start']}-{r['end']}" for r in ranges]
                return f"Available only during: {', '.join(time_list)}"
        
        return "Not available at this time"
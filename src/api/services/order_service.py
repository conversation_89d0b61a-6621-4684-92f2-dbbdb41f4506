# BHEEMDINE Order Service
# Business logic for order processing with validation and security

import asyncio
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from uuid import UUID, uuid4

from ..database.prisma_client import OrderRepository, create_order_repository
from ..models.order_submission import (
    OrderSubmissionRequest, OrderSubmissionResponse, OrderSubmissionError,
    OrderItemResponse, OrderStatus, OrderItemStatus
)
from .whatsapp_notification import WhatsAppNotificationService, create_whatsapp_service

# Configure logging
logger = logging.getLogger(__name__)

class OrderValidationError(Exception):
    """Custom exception for order validation errors"""
    
    def __init__(self, message: str, error_code: str = "VALIDATION_ERROR", details: Dict = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(message)

class OrderSecurityError(Exception):
    """Custom exception for order security violations"""
    
    def __init__(self, message: str, error_code: str = "SECURITY_ERROR", details: Dict = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(message)

class OrderService:
    """
    Service class handling order submission business logic
    Encapsulates validation, security checks, and order processing
    """
    
    def __init__(self, repository: OrderRepository, whatsapp_service: WhatsAppNotificationService = None):
        self.repository = repository
        self.whatsapp_service = whatsapp_service
        self.max_order_amount = Decimal('500.00')  # Maximum order amount
        self.max_items_per_order = 20  # Maximum items per order
        self.order_timeout_minutes = 15  # Order expiration time
    
    async def submit_guest_order(self, request: OrderSubmissionRequest) -> OrderSubmissionResponse:
        """
        Main method to process guest order submission
        
        Args:
            request: Validated order submission request
            
        Returns:
            OrderSubmissionResponse with order confirmation
            
        Raises:
            OrderValidationError: For validation failures
            OrderSecurityError: For security violations
            Exception: For unexpected errors
        """
        try:
            logger.info(
                f"Processing order submission for tenant {request.tenant_id}, "
                f"room {request.room_id}, {len(request.items)} items"
            )
            
            # Step 1: Security and business rule validation
            await self._validate_order_security(request)
            
            # Step 2: Validate tenant and room
            tenant_room_info = await self.repository.validate_tenant_and_room(
                str(request.tenant_id), 
                str(request.room_id)
            )
            
            # Step 3: Validate menu items and calculate totals
            menu_validation = await self.repository.validate_menu_items(
                str(request.tenant_id),
                request.items
            )
            
            # Step 4: Verify calculated totals match request
            await self._verify_order_totals(request, menu_validation)
            
            # Step 5: Create or get customer
            customer_id = await self.repository.create_or_get_customer(
                str(request.tenant_id),
                str(request.room_id),
                request.customer_info
            )
            
            # Step 6: Process payment (placeholder - would integrate with payment processor)
            payment_result = await self._process_payment(request.payment_info, menu_validation['subtotal'])
            
            # Step 7: Create order and items in database
            order_result = await self.repository.create_order_with_items(
                tenant_id=str(request.tenant_id),
                user_id=customer_id,
                room_id=str(request.room_id),
                validated_items=menu_validation['items'],
                totals=menu_validation,
                order_notes=request.order_notes,
                delivery_info=request.delivery_info.dict() if request.delivery_info else None
            )
            
            # Step 8: Send notifications (async, don't wait)
            asyncio.create_task(self._send_order_notifications(order_result, tenant_room_info))
            
            # Step 9: Build response
            response = await self._build_order_response(
                order_result, 
                tenant_room_info, 
                payment_result
            )
            
            logger.info(f"Order {response.order_number} submitted successfully")
            return response
            
        except (OrderValidationError, OrderSecurityError) as e:
            logger.warning(f"Order submission failed: {e.message}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during order submission: {e}")
            raise OrderValidationError(
                "An unexpected error occurred while processing your order. Please try again.",
                "PROCESSING_ERROR",
                {"original_error": str(e)}
            )
    
    async def _validate_order_security(self, request: OrderSubmissionRequest) -> None:
        """
        Validate order for security and business rules
        
        Args:
            request: Order submission request
            
        Raises:
            OrderValidationError: For validation failures
            OrderSecurityError: For security violations
        """
        
        # Check maximum order amount
        if request.estimated_total > self.max_order_amount:
            raise OrderValidationError(
                f"Order amount ${request.estimated_total} exceeds maximum allowed ${self.max_order_amount}",
                "AMOUNT_EXCEEDED",
                {"max_amount": str(self.max_order_amount), "requested_amount": str(request.estimated_total)}
            )
        
        # Check maximum items per order
        if len(request.items) > self.max_items_per_order:
            raise OrderValidationError(
                f"Order contains {len(request.items)} items, maximum allowed is {self.max_items_per_order}",
                "TOO_MANY_ITEMS",
                {"max_items": self.max_items_per_order, "requested_items": len(request.items)}
            )
        
        # Check for suspicious patterns
        await self._check_suspicious_patterns(request)
        
        # Validate business hours (would be tenant-specific)
        await self._validate_business_hours(request.tenant_id)
        
        # Rate limiting check (would be implemented with Redis or similar)
        await self._check_rate_limits(request.tenant_id, request.room_id)
    
    async def _check_suspicious_patterns(self, request: OrderSubmissionRequest) -> None:
        """
        Check for suspicious ordering patterns that might indicate fraud or abuse
        """
        
        # Check for excessive quantities
        total_quantity = sum(item.quantity for item in request.items)
        if total_quantity > 50:
            logger.warning(f"High quantity order detected: {total_quantity} items")
        
        # Check for suspicious price patterns
        for item in request.items:
            if item.unit_price < Decimal('0.01'):  # Suspiciously low price
                raise OrderSecurityError(
                    "Invalid pricing detected",
                    "SUSPICIOUS_PRICING",
                    {"item_id": str(item.menu_item_id), "price": str(item.unit_price)}
                )
        
        # Check for SQL injection patterns in text fields
        suspicious_patterns = ['<script', 'javascript:', 'onload=', 'SELECT ', 'DROP ', 'INSERT ']
        
        text_fields = []
        if request.order_notes:
            text_fields.append(request.order_notes)
        if request.customer_info:
            if request.customer_info.name:
                text_fields.append(request.customer_info.name)
        
        for item in request.items:
            if item.notes:
                text_fields.append(item.notes)
            if item.customizations and item.customizations.special_instructions:
                text_fields.append(item.customizations.special_instructions)
        
        for field in text_fields:
            for pattern in suspicious_patterns:
                if pattern.lower() in field.lower():
                    raise OrderSecurityError(
                        "Invalid characters detected in order",
                        "SUSPICIOUS_CONTENT",
                        {"pattern": pattern}
                    )
    
    async def _validate_business_hours(self, tenant_id: UUID) -> None:
        """
        Validate that orders can be placed during current business hours
        """
        # This would typically check tenant-specific business hours
        # For now, implement basic hours (6 AM to 11 PM)
        
        current_hour = datetime.now().hour
        if current_hour < 6 or current_hour >= 23:
            raise OrderValidationError(
                "Orders can only be placed between 6:00 AM and 11:00 PM",
                "OUTSIDE_BUSINESS_HOURS",
                {"current_hour": current_hour, "business_hours": "6:00 AM - 11:00 PM"}
            )
    
    async def _check_rate_limits(self, tenant_id: UUID, room_id: UUID) -> None:
        """
        Check rate limits to prevent order spam
        In production, this would use Redis or similar for distributed rate limiting
        """
        # This is a placeholder - in production you'd implement proper rate limiting
        # with Redis or similar distributed cache
        
        # For now, just log the check
        logger.debug(f"Rate limit check for tenant {tenant_id}, room {room_id}")
        
        # Could implement simple in-memory rate limiting for demo
        # But in production, this needs to be distributed across app instances
    
    async def _verify_order_totals(
        self, 
        request: OrderSubmissionRequest, 
        menu_validation: Dict
    ) -> None:
        """
        Verify that client-calculated totals match server calculations
        
        Args:
            request: Order submission request
            menu_validation: Validated menu items with calculated totals
        """
        
        server_subtotal = Decimal(str(menu_validation['subtotal']))
        client_total = request.estimated_total
        
        # Calculate server-side totals
        tax_rate = Decimal('0.08')  # 8% tax rate
        server_tax = server_subtotal * tax_rate
        server_total = server_subtotal + server_tax
        
        # Allow small variance for rounding differences
        variance = abs(server_total - client_total)
        max_variance = Decimal('0.10')  # 10 cents tolerance
        
        if variance > max_variance:
            logger.warning(
                f"Total mismatch: server ${server_total:.2f}, client ${client_total:.2f}, "
                f"variance ${variance:.2f}"
            )
            raise OrderValidationError(
                f"Order total mismatch. Please refresh the menu and try again.",
                "TOTAL_MISMATCH",
                {
                    "server_total": str(server_total),
                    "client_total": str(client_total),
                    "variance": str(variance)
                }
            )
    
    async def _process_payment(self, payment_info, amount: Decimal) -> Dict:
        """
        Process payment for the order
        In production, this would integrate with payment processors like Stripe, Square, etc.
        
        Args:
            payment_info: Payment information from request
            amount: Order amount to charge
            
        Returns:
            Payment processing result
        """
        
        # This is a placeholder implementation
        # In production, you would:
        # 1. Validate payment method
        # 2. Call payment processor API
        # 3. Handle payment failures
        # 4. Store payment transaction details
        
        logger.info(f"Processing payment: {payment_info.payment_method} for ${amount:.2f}")
        
        # Simulate payment processing
        if payment_info.payment_method == "CASH":
            # Cash payments are processed on delivery
            return {
                "status": "pending",
                "payment_method": "CASH",
                "transaction_id": None,
                "amount": amount
            }
        elif payment_info.payment_method == "ROOM_CHARGE":
            # Room charges are processed by hotel billing
            return {
                "status": "authorized",
                "payment_method": "ROOM_CHARGE",
                "transaction_id": f"RC_{uuid4().hex[:8]}",
                "amount": amount
            }
        else:
            # Card or digital wallet payments would be processed here
            # For demo, simulate successful payment
            return {
                "status": "completed",
                "payment_method": payment_info.payment_method,
                "transaction_id": f"TXN_{uuid4().hex[:8]}",
                "amount": amount
            }
    
    async def _send_order_notifications(
        self, 
        order_result: Dict, 
        tenant_room_info: Dict
    ) -> None:
        """
        Send order notifications to kitchen, customer, and staff via WhatsApp
        This runs asynchronously and doesn't block order response
        
        Args:
            order_result: Created order information
            tenant_room_info: Tenant and room information
        """
        
        if not self.whatsapp_service:
            logger.warning("WhatsApp service not configured, skipping notifications")
            return
        
        try:
            order = order_result['order']
            tenant = tenant_room_info['tenant']
            room = tenant_room_info['room']
            
            logger.info(
                f"Sending WhatsApp notifications for order {order.orderNumber} "
                f"at {tenant['name']}, room {room['roomNumber']}"
            )
            
            # Prepare order data for notifications
            order_data = {
                "order_id": str(order.id),
                "order_number": order.orderNumber,
                "customer_name": order_result.get('customer_name', 'Guest'),
                "room_number": room['roomNumber'],
                "total_amount": float(order_result.get('total_amount', 0)),
                "item_count": len(order_result.get('order_items', [])),
                "order_notes": order_result.get('order_notes', ''),
                "items": self._format_items_for_notification(order_result.get('order_items', [])),
                "tenant_name": tenant['name']
            }
            
            # Get staff and kitchen phone numbers from tenant configuration
            # In production, this would be stored in the database
            staff_phones = self._get_tenant_staff_phones(tenant.get('id'))
            kitchen_phones = self._get_tenant_kitchen_phones(tenant.get('id'))
            
            # Send notifications in parallel (don't wait for completion)
            notification_tasks = []
            
            # 1. Send to general staff
            if staff_phones:
                notification_tasks.append(
                    self.whatsapp_service.send_new_order_notification(
                        order_data=order_data,
                        staff_phones=staff_phones,
                        tenant_info={"name": tenant['name']}
                    )
                )
            
            # 2. Send detailed notification to kitchen
            if kitchen_phones:
                notification_tasks.append(
                    self.whatsapp_service.send_kitchen_notification(
                        order_data=order_data,
                        kitchen_phones=kitchen_phones,
                        priority="normal"
                    )
                )
            
            # Execute all notifications concurrently
            if notification_tasks:
                notification_results = await asyncio.gather(*notification_tasks, return_exceptions=True)
                
                # Log results
                for i, result in enumerate(notification_results):
                    if isinstance(result, Exception):
                        logger.error(f"Notification task {i} failed: {result}")
                    else:
                        logger.info(f"Notification task {i} completed successfully")
            
            logger.info(f"All notifications sent for order {order.orderNumber}")
            
        except Exception as e:
            # Don't fail the order if notifications fail
            logger.error(f"Failed to send order notifications: {e}")
    
    def _format_items_for_notification(self, order_items: List[Dict]) -> List[Dict]:
        """
        Format order items for notification messages
        
        Args:
            order_items: Raw order items from database
            
        Returns:
            Formatted items for notifications
        """
        formatted_items = []
        
        for item in order_items:
            formatted_item = {
                "menu_item_name": item.get('menuItemName', 'Unknown Item'),
                "quantity": item.get('quantity', 1),
                "unit_price": float(item.get('unitPrice', 0)),
                "notes": item.get('notes', ''),
                "customizations": item.get('customizations', {})
            }
            formatted_items.append(formatted_item)
        
        return formatted_items
    
    def _get_tenant_staff_phones(self, tenant_id: str) -> List[str]:
        """
        Get staff phone numbers for a tenant
        
        Args:
            tenant_id: Tenant ID
            
        Returns:
            List of staff phone numbers
        """
        # In production, this would query the database for staff phone numbers
        # For now, return example phones or check environment variables
        
        import os
        staff_phones_env = os.getenv(f'TENANT_{tenant_id}_STAFF_PHONES', os.getenv('DEFAULT_STAFF_PHONES', ''))
        
        if staff_phones_env:
            return [phone.strip() for phone in staff_phones_env.split(',') if phone.strip()]
        
        # Fallback to default phones for demo
        default_phones = os.getenv('BHEEMDINE_STAFF_PHONES', '')
        if default_phones:
            return [phone.strip() for phone in default_phones.split(',') if phone.strip()]
        
        return []
    
    def _get_tenant_kitchen_phones(self, tenant_id: str) -> List[str]:
        """
        Get kitchen staff phone numbers for a tenant
        
        Args:
            tenant_id: Tenant ID
            
        Returns:
            List of kitchen staff phone numbers
        """
        # In production, this would query the database for kitchen staff phone numbers
        # For now, return example phones or check environment variables
        
        import os
        kitchen_phones_env = os.getenv(f'TENANT_{tenant_id}_KITCHEN_PHONES', os.getenv('DEFAULT_KITCHEN_PHONES', ''))
        
        if kitchen_phones_env:
            return [phone.strip() for phone in kitchen_phones_env.split(',') if phone.strip()]
        
        # Fallback to default phones for demo
        default_phones = os.getenv('BHEEMDINE_KITCHEN_PHONES', '')
        if default_phones:
            return [phone.strip() for phone in default_phones.split(',') if phone.strip()]
        
        return []
    
    async def _build_order_response(
        self, 
        order_result: Dict, 
        tenant_room_info: Dict,
        payment_result: Dict
    ) -> OrderSubmissionResponse:
        """
        Build the order submission response
        
        Args:
            order_result: Created order information
            tenant_room_info: Tenant and room information
            payment_result: Payment processing result
            
        Returns:
            OrderSubmissionResponse
        """
        
        order = order_result['order']
        tenant = tenant_room_info['tenant']
        room = tenant_room_info['room']
        
        # Calculate estimated ready time
        prep_time_minutes = order_result.get('estimated_prep_time', 30)
        estimated_ready_time = order.placedAt + timedelta(minutes=prep_time_minutes)
        
        # Build order items response
        order_items = []
        for item_data in order_result['order_items']:
            # Find corresponding menu item
            menu_item = next(
                (item['menu_item'] for item in order_result.get('validated_items', []) 
                 if item['menu_item'].id == item_data['menuItemId']),
                None
            )
            
            order_items.append(OrderItemResponse(
                id=UUID(item_data['orderId']),  # Using orderId as placeholder
                menu_item_id=UUID(item_data['menuItemId']),
                menu_item_name=menu_item.name if menu_item else "Unknown Item",
                quantity=item_data['quantity'],
                unit_price=Decimal(str(item_data['unitPrice'])),
                total_price=Decimal(str(item_data['unitPrice'])) * item_data['quantity'],
                status=OrderItemStatus.PENDING,
                customizations=None,  # Would include customizations if stored
                notes=item_data.get('notes')
            ))
        
        # Build confirmation message
        confirmation_message = (
            f"Your order #{order.orderNumber} has been received! "
            f"Estimated preparation time: {prep_time_minutes} minutes. "
            f"We'll notify you when it's ready."
        )
        
        return OrderSubmissionResponse(
            order_id=UUID(order.id),
            order_number=order.orderNumber,
            status=OrderStatus.PENDING,
            placed_at=order.placedAt,
            estimated_preparation_time=prep_time_minutes,
            estimated_ready_time=estimated_ready_time,
            subtotal=Decimal(str(order_result['subtotal'])),
            tax_amount=Decimal(str(order_result['tax_amount'])),
            tip_amount=payment_result.get('tip_amount'),
            total_amount=Decimal(str(order_result['total_amount'])),
            items=order_items,
            customer_id=UUID(order.userId) if order.userId else None,
            confirmation_message=confirmation_message,
            tracking_url=f"/orders/{order.id}/track",  # Would be full URL in production
            tenant_name=tenant['name'],
            room_number=room['roomNumber']
        )

# =====================================================
# SERVICE FACTORY
# =====================================================

async def create_order_service() -> OrderService:
    """
    Factory function to create OrderService with dependencies
    """
    repository = await create_order_repository()
    
    # Try to create WhatsApp service, but don't fail if not configured
    whatsapp_service = None
    try:
        whatsapp_service = create_whatsapp_service()
        logger.info("WhatsApp notification service enabled")
    except Exception as e:
        logger.warning(f"WhatsApp notification service not available: {e}")
    
    return OrderService(repository, whatsapp_service)

# =====================================================
# ORDER STATUS MANAGEMENT
# =====================================================

class OrderStatusService:
    """
    Service for managing order status updates
    Separate from order submission for better separation of concerns
    """
    
    def __init__(self, repository: OrderRepository):
        self.repository = repository
    
    async def update_order_status(
        self, 
        order_id: str, 
        new_status: OrderStatus,
        staff_id: str = None,
        notes: str = None
    ) -> Dict:
        """
        Update order status with audit trail
        
        Args:
            order_id: UUID string of the order
            new_status: New order status
            staff_id: ID of staff member making the change
            notes: Optional notes about the status change
            
        Returns:
            Updated order information
        """
        
        try:
            # This would implement status update logic
            # Including validation, audit trail, and notifications
            
            logger.info(f"Updating order {order_id} status to {new_status}")
            
            # In production, this would:
            # 1. Validate status transition is allowed
            # 2. Update order status in database
            # 3. Create audit event
            # 4. Send notifications
            # 5. Update real-time displays
            
            return {
                "order_id": order_id,
                "status": new_status,
                "updated_at": datetime.now(),
                "updated_by": staff_id
            }
            
        except Exception as e:
            logger.error(f"Failed to update order status: {e}")
            raise

# =====================================================
# UTILITY FUNCTIONS
# =====================================================

def validate_uuid(uuid_string: str, field_name: str) -> UUID:
    """
    Validate and convert UUID string
    
    Args:
        uuid_string: String representation of UUID
        field_name: Name of field for error reporting
        
    Returns:
        UUID object
        
    Raises:
        OrderValidationError: If UUID is invalid
    """
    try:
        return UUID(uuid_string)
    except (ValueError, TypeError) as e:
        raise OrderValidationError(
            f"Invalid {field_name} format",
            "INVALID_UUID",
            {"field": field_name, "value": uuid_string}
        )

def calculate_tax_amount(subtotal: Decimal, tax_rate: Decimal = Decimal('0.08')) -> Decimal:
    """
    Calculate tax amount for an order
    
    Args:
        subtotal: Order subtotal
        tax_rate: Tax rate (default 8%)
        
    Returns:
        Tax amount
    """
    return (subtotal * tax_rate).quantize(Decimal('0.01'))

def format_currency(amount: Decimal) -> str:
    """
    Format decimal amount as currency string
    
    Args:
        amount: Decimal amount
        
    Returns:
        Formatted currency string
    """
    return f"${amount:.2f}"
# BHEEMDINE Dynamic Pricing Engine
# Handles all dynamic pricing calculations with rule-based logic

import logging
from datetime import datetime, time
from decimal import Decimal, ROUND_HALF_UP
from typing import List, Dict, Any, Optional, Tuple
from uuid import UUID

from sqlalchemy.orm import Session

from ..models.menu import (
    MenuItem, PricingRule, PricingContext, PricingInfo,
    PricingRuleType, DayOfWeek
)

logger = logging.getLogger(__name__)

class PricingEngine:
    """
    Dynamic pricing engine that applies various pricing rules to menu items
    based on context like time, customer type, quantity, etc.
    """
    
    def __init__(self, db: Session):
        self.db = db
    
    def calculate_item_pricing(
        self, 
        menu_item: MenuItem, 
        context: Optional[PricingContext] = None
    ) -> PricingInfo:
        """
        Calculate the final price for a menu item based on active pricing rules
        
        Args:
            menu_item: The menu item to price
            context: Pricing context (customer, time, quantity, etc.)
            
        Returns:
            PricingInfo object with base price, final price, and applied rules
        """
        if context is None:
            context = PricingContext()
        
        logger.info(f"Calculating pricing for item {menu_item.id} with context: {context}")
        
        # Start with base price
        base_price = Decimal(str(menu_item.price))
        current_price = base_price
        applied_rules = []
        total_discount_percentage = Decimal('0')
        total_discount_amount = Decimal('0')
        
        # Get active pricing rules for this tenant
        active_rules = self._get_active_pricing_rules(
            menu_item.tenantId, 
            context.request_time
        )
        
        # Apply each applicable rule
        for rule in active_rules:
            if self._is_rule_applicable(rule, menu_item, context):
                price_adjustment = self._apply_pricing_rule(
                    rule, 
                    current_price, 
                    base_price, 
                    context
                )
                
                if price_adjustment:
                    old_price = current_price
                    current_price = price_adjustment['new_price']
                    applied_rules.append(rule.name)
                    
                    # Track discounts for display
                    if price_adjustment['discount_percentage']:
                        total_discount_percentage += price_adjustment['discount_percentage']
                    if price_adjustment['discount_amount']:
                        total_discount_amount += price_adjustment['discount_amount']
                    
                    logger.info(
                        f"Applied rule '{rule.name}': {old_price} -> {current_price}"
                    )
        
        # Ensure price doesn't go below minimum (e.g., cost price)
        min_price = base_price * Decimal('0.1')  # Minimum 10% of base price
        current_price = max(current_price, min_price)
        
        # Round to 2 decimal places
        current_price = current_price.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        
        return PricingInfo(
            basePrice=base_price,
            currentPrice=current_price,
            discountPercentage=total_discount_percentage if total_discount_percentage > 0 else None,
            discountAmount=total_discount_amount if total_discount_amount > 0 else None,
            appliedRules=applied_rules
        )
    
    def _get_active_pricing_rules(
        self, 
        tenant_id: UUID, 
        request_time: datetime
    ) -> List[PricingRule]:
        """
        Get all active pricing rules for a tenant at the given time
        
        Args:
            tenant_id: Tenant UUID
            request_time: Current request time
            
        Returns:
            List of active pricing rules sorted by priority
        """
        return (
            self.db.query(PricingRule)
            .filter(
                PricingRule.tenantId == tenant_id,
                PricingRule.isActive == True,
                # Rule is within valid date range
                (PricingRule.validFrom.is_(None) | (PricingRule.validFrom <= request_time)),
                (PricingRule.validTo.is_(None) | (PricingRule.validTo >= request_time))
            )
            .order_by(PricingRule.createdAt)  # Earlier rules have priority
            .all()
        )
    
    def _is_rule_applicable(
        self, 
        rule: PricingRule, 
        menu_item: MenuItem, 
        context: PricingContext
    ) -> bool:
        """
        Check if a pricing rule applies to the given menu item and context
        
        Args:
            rule: The pricing rule to check
            menu_item: The menu item
            context: Pricing context
            
        Returns:
            True if rule applies, False otherwise
        """
        # Check if rule applies to this specific item
        if rule.applicableItems:
            if str(menu_item.id) not in rule.applicableItems:
                return False
        
        # Check if rule applies to this item's category
        if rule.applicableCategories:
            if menu_item.category not in rule.applicableCategories:
                return False
        
        # Check rule-specific conditions
        conditions = rule.conditions or {}
        
        # Time-based rules (Happy Hour, etc.)
        if rule.ruleType == PricingRuleType.HAPPY_HOUR:
            return self._check_time_conditions(conditions, context.request_time)
        
        # Quantity-based rules (Bulk discounts)
        elif rule.ruleType == PricingRuleType.BULK_DISCOUNT:
            return self._check_quantity_conditions(conditions, context.order_quantity)
        
        # Room-type based rules (VIP pricing)
        elif rule.ruleType == PricingRuleType.ROOM_TYPE:
            return self._check_room_conditions(conditions, context.room_id)
        
        # Customer loyalty rules
        elif rule.ruleType == PricingRuleType.LOYALTY:
            return self._check_loyalty_conditions(conditions, context.customer_id)
        
        # Seasonal rules
        elif rule.ruleType == PricingRuleType.SEASONAL:
            return self._check_seasonal_conditions(conditions, context.request_time)
        
        return True
    
    def _check_time_conditions(self, conditions: Dict, request_time: datetime) -> bool:
        """Check if current time matches time-based conditions"""
        
        # Check day of week
        if 'days_of_week' in conditions:
            current_day = request_time.strftime('%A').lower()
            if current_day not in [day.lower() for day in conditions['days_of_week']]:
                return False
        
        # Check time range
        if 'start_time' in conditions and 'end_time' in conditions:
            current_time = request_time.time()
            start_time = time.fromisoformat(conditions['start_time'])
            end_time = time.fromisoformat(conditions['end_time'])
            
            if start_time <= end_time:
                # Same day range
                if not (start_time <= current_time <= end_time):
                    return False
            else:
                # Overnight range (e.g., 22:00 to 02:00)
                if not (current_time >= start_time or current_time <= end_time):
                    return False
        
        return True
    
    def _check_quantity_conditions(self, conditions: Dict, quantity: int) -> bool:
        """Check if quantity meets bulk discount conditions"""
        min_quantity = conditions.get('min_quantity', 1)
        max_quantity = conditions.get('max_quantity')
        
        if quantity < min_quantity:
            return False
        
        if max_quantity and quantity > max_quantity:
            return False
        
        return True
    
    def _check_room_conditions(self, conditions: Dict, room_id: Optional[UUID]) -> bool:
        """Check if room type meets pricing conditions"""
        if not room_id:
            return False
        
        # In a real implementation, you'd query room details
        # For now, we'll use simple logic
        room_types = conditions.get('room_types', [])
        
        if room_types:
            # This would query the Room table to get room type
            # room = self.db.query(Room).filter(Room.id == room_id).first()
            # return room.type in room_types
            return True  # Simplified for demo
        
        return True
    
    def _check_loyalty_conditions(self, conditions: Dict, customer_id: Optional[UUID]) -> bool:
        """Check if customer meets loyalty conditions"""
        if not customer_id:
            return False
        
        # In a real implementation, you'd check customer loyalty level
        # loyalty_level = get_customer_loyalty_level(customer_id)
        # required_level = conditions.get('min_loyalty_level')
        # return loyalty_level >= required_level
        
        return True  # Simplified for demo
    
    def _check_seasonal_conditions(self, conditions: Dict, request_time: datetime) -> bool:
        """Check if current date meets seasonal conditions"""
        
        # Check specific date range
        if 'start_date' in conditions and 'end_date' in conditions:
            start_date = datetime.fromisoformat(conditions['start_date']).date()
            end_date = datetime.fromisoformat(conditions['end_date']).date()
            current_date = request_time.date()
            
            if not (start_date <= current_date <= end_date):
                return False
        
        # Check month range (for recurring seasonal offers)
        if 'months' in conditions:
            current_month = request_time.month
            if current_month not in conditions['months']:
                return False
        
        return True
    
    def _apply_pricing_rule(
        self, 
        rule: PricingRule, 
        current_price: Decimal, 
        base_price: Decimal, 
        context: PricingContext
    ) -> Optional[Dict[str, Any]]:
        """
        Apply a specific pricing rule and return the adjustment
        
        Args:
            rule: The pricing rule to apply
            current_price: Current price before this rule
            base_price: Original base price
            context: Pricing context
            
        Returns:
            Dictionary with new_price and discount info, or None if no change
        """
        new_price = current_price
        discount_percentage = None
        discount_amount = None
        
        # Apply percentage discount
        if rule.discountPercentage:
            discount_percentage = Decimal(str(rule.discountPercentage))
            discount_amount = current_price * (discount_percentage / Decimal('100'))
            new_price = current_price - discount_amount
        
        # Apply fixed amount discount
        elif rule.discountAmount:
            discount_amount = Decimal(str(rule.discountAmount))
            new_price = current_price - discount_amount
        
        # Apply percentage markup
        elif rule.markupPercentage:
            markup_percentage = Decimal(str(rule.markupPercentage))
            markup_amount = current_price * (markup_percentage / Decimal('100'))
            new_price = current_price + markup_amount
        
        # Special quantity-based discounts
        elif rule.ruleType == PricingRuleType.BULK_DISCOUNT:
            new_price, discount_info = self._apply_bulk_discount(
                rule, current_price, context.order_quantity
            )
            discount_percentage = discount_info.get('discount_percentage')
            discount_amount = discount_info.get('discount_amount')
        
        # Only return if there's an actual change
        if new_price != current_price:
            return {
                'new_price': new_price,
                'discount_percentage': discount_percentage,
                'discount_amount': discount_amount
            }
        
        return None
    
    def _apply_bulk_discount(
        self, 
        rule: PricingRule, 
        current_price: Decimal, 
        quantity: int
    ) -> Tuple[Decimal, Dict[str, Any]]:
        """
        Apply quantity-based bulk discounts with tiered pricing
        
        Args:
            rule: The bulk discount rule
            current_price: Current item price
            quantity: Order quantity
            
        Returns:
            Tuple of (new_price, discount_info)
        """
        conditions = rule.conditions or {}
        tiers = conditions.get('tiers', [])
        
        # Default single-tier discount
        if not tiers and rule.discountPercentage:
            discount_percentage = Decimal(str(rule.discountPercentage))
            discount_amount = current_price * (discount_percentage / Decimal('100'))
            new_price = current_price - discount_amount
            
            return new_price, {
                'discount_percentage': discount_percentage,
                'discount_amount': discount_amount
            }
        
        # Multi-tier pricing (e.g., 5% off 3+, 10% off 5+, 15% off 10+)
        best_discount = Decimal('0')
        applied_tier = None
        
        for tier in tiers:
            min_qty = tier.get('min_quantity', 1)
            if quantity >= min_qty:
                tier_discount = Decimal(str(tier.get('discount_percentage', 0)))
                if tier_discount > best_discount:
                    best_discount = tier_discount
                    applied_tier = tier
        
        if applied_tier:
            discount_amount = current_price * (best_discount / Decimal('100'))
            new_price = current_price - discount_amount
            
            return new_price, {
                'discount_percentage': best_discount,
                'discount_amount': discount_amount
            }
        
        return current_price, {}

class PricingRuleManager:
    """
    Utility class for managing pricing rules
    """
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_happy_hour_rule(
        self,
        tenant_id: UUID,
        name: str,
        discount_percentage: Decimal,
        start_time: str,
        end_time: str,
        days_of_week: List[str],
        categories: Optional[List[str]] = None
    ) -> PricingRule:
        """
        Create a happy hour pricing rule
        
        Example:
            create_happy_hour_rule(
                tenant_id=uuid,
                name="Happy Hour Drinks",
                discount_percentage=25.0,
                start_time="17:00",
                end_time="19:00",
                days_of_week=["monday", "tuesday", "wednesday"],
                categories=["beverages"]
            )
        """
        conditions = {
            "start_time": start_time,
            "end_time": end_time,
            "days_of_week": days_of_week
        }
        
        rule = PricingRule(
            tenantId=tenant_id,
            name=name,
            ruleType=PricingRuleType.HAPPY_HOUR,
            discountPercentage=discount_percentage,
            conditions=conditions,
            applicableCategories=categories,
            isActive=True
        )
        
        self.db.add(rule)
        self.db.commit()
        return rule
    
    def create_bulk_discount_rule(
        self,
        tenant_id: UUID,
        name: str,
        tiers: List[Dict[str, Any]]
    ) -> PricingRule:
        """
        Create a bulk discount rule with multiple tiers
        
        Example:
            create_bulk_discount_rule(
                tenant_id=uuid,
                name="Bulk Food Discount",
                tiers=[
                    {"min_quantity": 3, "discount_percentage": 5.0},
                    {"min_quantity": 5, "discount_percentage": 10.0},
                    {"min_quantity": 10, "discount_percentage": 15.0}
                ]
            )
        """
        conditions = {"tiers": tiers}
        
        rule = PricingRule(
            tenantId=tenant_id,
            name=name,
            ruleType=PricingRuleType.BULK_DISCOUNT,
            conditions=conditions,
            isActive=True
        )
        
        self.db.add(rule)
        self.db.commit()
        return rule
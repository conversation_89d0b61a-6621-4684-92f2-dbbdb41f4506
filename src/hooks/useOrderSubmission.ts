// Custom hook for order submission logic and API integration
'use client';

import { useState, useCallback, useRef } from 'react';
import { useMenuStore } from '@/stores/menuStore';
import type { 
  OrderSubmissionRequest, 
  OrderSubmissionResponse, 
  OrderFormData, 
  OrderFormErrors,
  OrderSubmissionError,
  OrderValidationError 
} from '@/types/order';

interface UseOrderSubmissionConfig {
  tenantId: string;
  roomId?: string;
  onSuccess?: (response: OrderSubmissionResponse) => void;
  onError?: (error: OrderSubmissionError) => void;
  apiEndpoint?: string;
}

interface UseOrderSubmissionReturn {
  isSubmitting: boolean;
  submitError: OrderSubmissionError | null;
  submitOrder: (formData: OrderFormData) => Promise<OrderSubmissionResponse | null>;
  validateOrder: (formData: OrderFormData) => OrderFormErrors;
  clearError: () => void;
  lastSubmission: OrderSubmissionResponse | null;
}

export function useOrderSubmission({
  tenantId,
  roomId,
  onSuccess,
  onError,
  apiEndpoint = '/api/v1/orders/submit'
}: UseOrderSubmissionConfig): UseOrderSubmissionReturn {
  
  // =====================================================
  // STATE
  // =====================================================
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<OrderSubmissionError | null>(null);
  const [lastSubmission, setLastSubmission] = useState<OrderSubmissionResponse | null>(null);
  
  // Use ref to prevent stale closure issues
  const abortControllerRef = useRef<AbortController | null>(null);
  
  // Get cart items from store
  const { cartItems, getCartSubtotal, clearCart } = useMenuStore();
  
  // =====================================================
  // VALIDATION FUNCTIONS
  // =====================================================
  
  const validateCustomerInfo = (customerInfo: OrderFormData['customerInfo']): Record<string, string> => {
    const errors: Record<string, string> = {};
    
    if (!customerInfo.name?.trim()) {
      errors.name = 'Name is required';
    } else if (customerInfo.name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters';
    } else if (customerInfo.name.trim().length > 50) {
      errors.name = 'Name must be less than 50 characters';
    }
    
    if (customerInfo.email && customerInfo.email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(customerInfo.email.trim())) {
        errors.email = 'Please enter a valid email address';
      }
    }
    
    if (customerInfo.phone && customerInfo.phone.trim()) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      const cleanPhone = customerInfo.phone.replace(/[\s\-\(\)]/g, '');
      if (!phoneRegex.test(cleanPhone)) {
        errors.phone = 'Please enter a valid phone number';
      }
    }
    
    return errors;
  };
  
  const validateDeliveryInfo = (deliveryInfo: OrderFormData['deliveryInfo']): Record<string, string> => {
    const errors: Record<string, string> = {};
    
    if (!deliveryInfo.roomNumber?.trim()) {
      errors.roomNumber = 'Room number is required';
    } else if (deliveryInfo.roomNumber.trim().length > 20) {
      errors.roomNumber = 'Room number must be less than 20 characters';
    }
    
    if (deliveryInfo.deliveryInstructions && deliveryInfo.deliveryInstructions.length > 500) {
      errors.deliveryInstructions = 'Delivery instructions must be less than 500 characters';
    }
    
    if (deliveryInfo.preferredDeliveryTime) {
      const now = new Date();
      const deliveryTime = new Date(deliveryInfo.preferredDeliveryTime);
      
      if (deliveryTime <= now) {
        errors.preferredDeliveryTime = 'Delivery time must be in the future';
      }
      
      // Check if delivery time is within business hours (6 AM - 11 PM)
      const hour = deliveryTime.getHours();
      if (hour < 6 || hour >= 23) {
        errors.preferredDeliveryTime = 'Delivery time must be between 6:00 AM and 11:00 PM';
      }
    }
    
    return errors;
  };
  
  const validatePaymentInfo = (paymentInfo: OrderFormData['paymentInfo']): Record<string, string> => {
    const errors: Record<string, string> = {};
    
    if (!paymentInfo.paymentMethod) {
      errors.paymentMethod = 'Payment method is required';
    }
    
    if (paymentInfo.paymentMethod === 'CARD' && !paymentInfo.paymentToken) {
      errors.paymentToken = 'Payment information is required for card payments';
    }
    
    if (paymentInfo.billingName && paymentInfo.billingName.trim().length < 2) {
      errors.billingName = 'Billing name must be at least 2 characters';
    }
    
    if (paymentInfo.tipAmount !== undefined) {
      if (paymentInfo.tipAmount < 0) {
        errors.tipAmount = 'Tip amount cannot be negative';
      } else if (paymentInfo.tipAmount > 1000) {
        errors.tipAmount = 'Tip amount seems unusually high';
      }
    }
    
    return errors;
  };
  
  const validateOrder = useCallback((formData: OrderFormData): OrderFormErrors => {
    const errors: OrderFormErrors = {};
    
    // Validate customer info
    const customerErrors = validateCustomerInfo(formData.customerInfo);
    if (Object.keys(customerErrors).length > 0) {
      errors.customerInfo = customerErrors;
    }
    
    // Validate delivery info
    const deliveryErrors = validateDeliveryInfo(formData.deliveryInfo);
    if (Object.keys(deliveryErrors).length > 0) {
      errors.deliveryInfo = deliveryErrors;
    }
    
    // Validate payment info
    const paymentErrors = validatePaymentInfo(formData.paymentInfo);
    if (Object.keys(paymentErrors).length > 0) {
      errors.paymentInfo = paymentErrors;
    }
    
    // Validate order notes
    if (formData.orderNotes && formData.orderNotes.length > 1000) {
      errors.orderNotes = 'Order notes must be less than 1000 characters';
    }
    
    // Validate terms agreement
    if (!formData.agreeToTerms) {
      errors.agreeToTerms = 'You must agree to the terms and conditions';
    }
    
    // Validate cart
    if (cartItems.length === 0) {
      errors.general = 'Your cart is empty. Please add items before placing an order.';
    }
    
    // Validate order amount
    const subtotal = getCartSubtotal();
    if (subtotal <= 0) {
      errors.general = 'Order total must be greater than $0';
    } else if (subtotal > 500) {
      errors.general = 'Order total cannot exceed $500. Please contact us for large orders.';
    }
    
    return errors;
  }, [cartItems, getCartSubtotal]);
  
  // =====================================================
  // SUBMISSION FUNCTION
  // =====================================================
  
  const submitOrder = useCallback(async (formData: OrderFormData): Promise<OrderSubmissionResponse | null> => {
    // Validate form data
    const validationErrors = validateOrder(formData);
    if (Object.keys(validationErrors).length > 0) {
      const error: OrderSubmissionError = {
        errorCode: 'VALIDATION_ERROR',
        errorMessage: 'Please fix the validation errors and try again',
        fieldErrors: Object.entries(validationErrors).reduce((acc, [key, value]) => {
          if (typeof value === 'string') {
            acc[key] = [value];
          } else if (typeof value === 'object' && value !== null) {
            Object.entries(value).forEach(([subKey, subValue]) => {
              acc[`${key}.${subKey}`] = [subValue as string];
            });
          }
          return acc;
        }, {} as Record<string, string[]>)
      };
      
      setSubmitError(error);
      if (onError) onError(error);
      return null;
    }
    
    // Prepare submission
    setIsSubmitting(true);
    setSubmitError(null);
    
    // Create abort controller for this request
    abortControllerRef.current = new AbortController();
    
    try {
      // Calculate totals
      const subtotal = getCartSubtotal();
      const taxAmount = subtotal * 0.08; // 8% tax
      const tipAmount = formData.paymentInfo.tipAmount || 0;
      const totalAmount = subtotal + taxAmount + tipAmount;
      
      // Build order request
      const orderRequest: OrderSubmissionRequest = {
        tenantId,
        roomId,
        customerInfo: {
          name: formData.customerInfo.name.trim(),
          email: formData.customerInfo.email?.trim() || undefined,
          phone: formData.customerInfo.phone?.trim() || undefined,
        },
        items: cartItems.map(item => ({
          menuItemId: item.menuItemId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          customizations: item.customizations ? {
            modifications: item.customizations.modifications,
            specialInstructions: item.customizations.notes,
          } : undefined,
          notes: item.customizations?.notes,
        })),
        deliveryInfo: {
          roomNumber: formData.deliveryInfo.roomNumber.trim(),
          deliveryInstructions: formData.deliveryInfo.deliveryInstructions?.trim() || undefined,
          preferredDeliveryTime: formData.deliveryInfo.preferredDeliveryTime || undefined,
          isContactless: formData.deliveryInfo.isContactless || false,
        },
        paymentInfo: {
          paymentMethod: formData.paymentInfo.paymentMethod,
          paymentToken: formData.paymentInfo.paymentToken || undefined,
          billingName: formData.paymentInfo.billingName?.trim() || formData.customerInfo.name.trim(),
          tipAmount: tipAmount,
        },
        orderNotes: formData.orderNotes?.trim() || undefined,
        estimatedTotal: totalAmount,
        clientInfo: {
          appVersion: '1.0.0',
          platform: 'web',
          browser: navigator.userAgent.split(' ').pop()?.split('/')[0] || 'unknown',
        },
      };
      
      // Submit to API
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderRequest),
        signal: abortControllerRef.current.signal,
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        
        const error: OrderSubmissionError = {
          errorCode: errorData.error_code || `HTTP_${response.status}`,
          errorMessage: errorData.error_message || `Request failed with status ${response.status}`,
          details: errorData.details || { status: response.status },
          fieldErrors: errorData.field_errors || {},
          timestamp: new Date().toISOString(),
        };
        
        throw error;
      }
      
      const result: OrderSubmissionResponse = await response.json();
      
      // Success - clear cart and update state
      setLastSubmission(result);
      clearCart();
      
      if (onSuccess) onSuccess(result);
      
      return result;
      
    } catch (error) {
      // Handle different types of errors
      let submitError: OrderSubmissionError;
      
      if (error instanceof Error && error.name === 'AbortError') {
        // Request was cancelled
        return null;
      } else if (error && typeof error === 'object' && 'errorCode' in error) {
        // API error response
        submitError = error as OrderSubmissionError;
      } else if (error instanceof TypeError && error.message.includes('fetch')) {
        // Network error
        submitError = {
          errorCode: 'NETWORK_ERROR',
          errorMessage: 'Unable to connect to the server. Please check your internet connection and try again.',
          timestamp: new Date().toISOString(),
        };
      } else {
        // Unknown error
        submitError = {
          errorCode: 'UNKNOWN_ERROR',
          errorMessage: error instanceof Error ? error.message : 'An unexpected error occurred. Please try again.',
          timestamp: new Date().toISOString(),
        };
      }
      
      setSubmitError(submitError);
      if (onError) onError(submitError);
      
      return null;
      
    } finally {
      setIsSubmitting(false);
      abortControllerRef.current = null;
    }
  }, [
    tenantId,
    roomId,
    cartItems,
    getCartSubtotal,
    clearCart,
    validateOrder,
    onSuccess,
    onError,
    apiEndpoint,
  ]);
  
  // =====================================================
  // UTILITY FUNCTIONS
  // =====================================================
  
  const clearError = useCallback(() => {
    setSubmitError(null);
  }, []);
  
  // =====================================================
  // CLEANUP
  // =====================================================
  
  // Cancel any ongoing request when component unmounts
  const cancelRequest = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);
  
  // Return hook interface
  return {
    isSubmitting,
    submitError,
    submitOrder,
    validateOrder,
    clearError,
    lastSubmission,
  };
}
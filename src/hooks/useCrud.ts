// Reusable CRUD hook for data operations with optimistic updates and error handling
'use client';

import { useState, useCallback, useRef } from 'react';

export interface CrudItem {
  id: string;
  [key: string]: any;
}

export interface CrudOptions<T extends CrudItem> {
  apiEndpoint: string;
  onSuccess?: (action: CrudAction, item: T) => void;
  onError?: (action: CrudAction, error: Error) => void;
  optimisticUpdates?: boolean;
  initialData?: T[];
}

export type CrudAction = 'create' | 'read' | 'update' | 'delete' | 'bulkUpdate' | 'bulkDelete';

export interface CrudState<T extends CrudItem> {
  items: T[];
  loading: boolean;
  error: string | null;
  selectedItems: Set<string>;
  isSubmitting: boolean;
  lastAction: CrudAction | null;
}

export interface CrudActions<T extends CrudItem> {
  // Basic CRUD operations
  fetchItems: (params?: Record<string, any>) => Promise<T[]>;
  createItem: (data: Omit<T, 'id'>) => Promise<T>;
  updateItem: (id: string, data: Partial<T>) => Promise<T>;
  deleteItem: (id: string) => Promise<void>;
  
  // Bulk operations
  bulkUpdate: (ids: string[], data: Partial<T>) => Promise<T[]>;
  bulkDelete: (ids: string[]) => Promise<void>;
  
  // Selection management
  selectItem: (id: string) => void;
  selectAll: () => void;
  clearSelection: () => void;
  toggleSelection: (id: string) => void;
  
  // State management
  setItems: (items: T[]) => void;
  clearError: () => void;
  refresh: () => Promise<T[]>;
}

export function useCrud<T extends CrudItem>(
  options: CrudOptions<T>
): [CrudState<T>, CrudActions<T>] {
  
  // =====================================================
  // STATE MANAGEMENT
  // =====================================================
  
  const [state, setState] = useState<CrudState<T>>({
    items: options.initialData || [],
    loading: false,
    error: null,
    selectedItems: new Set(),
    isSubmitting: false,
    lastAction: null,
  });
  
  const abortControllerRef = useRef<AbortController | null>(null);
  
  // =====================================================
  // UTILITY FUNCTIONS
  // =====================================================
  
  const updateState = useCallback((updates: Partial<CrudState<T>>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);
  
  const handleError = useCallback((action: CrudAction, error: Error) => {
    console.error(`CRUD ${action} error:`, error);
    updateState({ 
      error: error.message, 
      loading: false, 
      isSubmitting: false,
      lastAction: action 
    });
    options.onError?.(action, error);
  }, [options, updateState]);
  
  const handleSuccess = useCallback((action: CrudAction, item?: T) => {
    updateState({ 
      error: null, 
      loading: false, 
      isSubmitting: false,
      lastAction: action 
    });
    if (item) {
      options.onSuccess?.(action, item);
    }
  }, [options, updateState]);
  
  const makeRequest = useCallback(async (
    url: string,
    options: RequestInit = {}
  ): Promise<Response> => {
    // Cancel previous request if still pending
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();
    
    const response = await fetch(url, {
      ...options,
      signal: abortControllerRef.current.signal,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }
    
    return response;
  }, []);
  
  // =====================================================
  // CRUD OPERATIONS
  // =====================================================
  
  const fetchItems = useCallback(async (params?: Record<string, any>): Promise<T[]> => {
    updateState({ loading: true, error: null, lastAction: 'read' });
    
    try {
      const url = new URL(options.apiEndpoint);
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            url.searchParams.append(key, String(value));
          }
        });
      }
      
      const response = await makeRequest(url.toString());
      const data = await response.json();
      const items = Array.isArray(data) ? data : data.items || data.data || [];
      
      updateState({ items, loading: false, lastAction: 'read' });
      handleSuccess('read');
      
      return items;
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        handleError('read', error);
      }
      return [];
    }
  }, [options.apiEndpoint, makeRequest, updateState, handleError, handleSuccess]);
  
  const createItem = useCallback(async (data: Omit<T, 'id'>): Promise<T> => {
    updateState({ isSubmitting: true, error: null, lastAction: 'create' });
    
    try {
      const response = await makeRequest(options.apiEndpoint, {
        method: 'POST',
        body: JSON.stringify(data),
      });
      
      const newItem = await response.json() as T;
      
      // Optimistic update
      if (options.optimisticUpdates !== false) {
        updateState({ 
          items: [...state.items, newItem],
          isSubmitting: false 
        });
      }
      
      handleSuccess('create', newItem);
      return newItem;
    } catch (error) {
      handleError('create', error as Error);
      throw error;
    }
  }, [options.apiEndpoint, options.optimisticUpdates, state.items, makeRequest, updateState, handleError, handleSuccess]);
  
  const updateItem = useCallback(async (id: string, data: Partial<T>): Promise<T> => {
    updateState({ isSubmitting: true, error: null, lastAction: 'update' });
    
    // Optimistic update
    let originalItems: T[] = [];
    if (options.optimisticUpdates !== false) {
      originalItems = [...state.items];
      const optimisticItems = state.items.map(item => 
        item.id === id ? { ...item, ...data } : item
      );
      updateState({ items: optimisticItems });
    }
    
    try {
      const response = await makeRequest(`${options.apiEndpoint}/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      });
      
      const updatedItem = await response.json() as T;
      
      // Update with server response
      updateState({ 
        items: state.items.map(item => 
          item.id === id ? updatedItem : item
        ),
        isSubmitting: false 
      });
      
      handleSuccess('update', updatedItem);
      return updatedItem;
    } catch (error) {
      // Revert optimistic update on error
      if (options.optimisticUpdates !== false) {
        updateState({ items: originalItems });
      }
      handleError('update', error as Error);
      throw error;
    }
  }, [options.apiEndpoint, options.optimisticUpdates, state.items, makeRequest, updateState, handleError, handleSuccess]);
  
  const deleteItem = useCallback(async (id: string): Promise<void> => {
    updateState({ isSubmitting: true, error: null, lastAction: 'delete' });
    
    // Optimistic update
    let originalItems: T[] = [];
    if (options.optimisticUpdates !== false) {
      originalItems = [...state.items];
      updateState({ items: state.items.filter(item => item.id !== id) });
    }
    
    try {
      await makeRequest(`${options.apiEndpoint}/${id}`, {
        method: 'DELETE',
      });
      
      // Confirm deletion
      if (options.optimisticUpdates === false) {
        updateState({ 
          items: state.items.filter(item => item.id !== id),
          isSubmitting: false 
        });
      } else {
        updateState({ isSubmitting: false });
      }
      
      handleSuccess('delete');
    } catch (error) {
      // Revert optimistic update on error
      if (options.optimisticUpdates !== false) {
        updateState({ items: originalItems });
      }
      handleError('delete', error as Error);
      throw error;
    }
  }, [options.apiEndpoint, options.optimisticUpdates, state.items, makeRequest, updateState, handleError, handleSuccess]);
  
  // =====================================================
  // BULK OPERATIONS
  // =====================================================
  
  const bulkUpdate = useCallback(async (ids: string[], data: Partial<T>): Promise<T[]> => {
    updateState({ isSubmitting: true, error: null, lastAction: 'bulkUpdate' });
    
    try {
      const response = await makeRequest(`${options.apiEndpoint}/bulk`, {
        method: 'PUT',
        body: JSON.stringify({ ids, data }),
      });
      
      const updatedItems = await response.json() as T[];
      
      // Update items in state
      updateState({ 
        items: state.items.map(item => {
          const updated = updatedItems.find(u => u.id === item.id);
          return updated || item;
        }),
        isSubmitting: false,
        selectedItems: new Set() // Clear selection after bulk operation
      });
      
      handleSuccess('bulkUpdate');
      return updatedItems;
    } catch (error) {
      handleError('bulkUpdate', error as Error);
      throw error;
    }
  }, [options.apiEndpoint, state.items, makeRequest, updateState, handleError, handleSuccess]);
  
  const bulkDelete = useCallback(async (ids: string[]): Promise<void> => {
    updateState({ isSubmitting: true, error: null, lastAction: 'bulkDelete' });
    
    try {
      await makeRequest(`${options.apiEndpoint}/bulk`, {
        method: 'DELETE',
        body: JSON.stringify({ ids }),
      });
      
      // Remove deleted items
      updateState({ 
        items: state.items.filter(item => !ids.includes(item.id)),
        isSubmitting: false,
        selectedItems: new Set() // Clear selection after bulk operation
      });
      
      handleSuccess('bulkDelete');
    } catch (error) {
      handleError('bulkDelete', error as Error);
      throw error;
    }
  }, [options.apiEndpoint, state.items, makeRequest, updateState, handleError, handleSuccess]);
  
  // =====================================================
  // SELECTION MANAGEMENT
  // =====================================================
  
  const selectItem = useCallback((id: string) => {
    updateState({ 
      selectedItems: new Set([...state.selectedItems, id]) 
    });
  }, [state.selectedItems, updateState]);
  
  const selectAll = useCallback(() => {
    updateState({ 
      selectedItems: new Set(state.items.map(item => item.id)) 
    });
  }, [state.items, updateState]);
  
  const clearSelection = useCallback(() => {
    updateState({ selectedItems: new Set() });
  }, [updateState]);
  
  const toggleSelection = useCallback((id: string) => {
    const newSelection = new Set(state.selectedItems);
    if (newSelection.has(id)) {
      newSelection.delete(id);
    } else {
      newSelection.add(id);
    }
    updateState({ selectedItems: newSelection });
  }, [state.selectedItems, updateState]);
  
  // =====================================================
  // UTILITY ACTIONS
  // =====================================================
  
  const setItems = useCallback((items: T[]) => {
    updateState({ items });
  }, [updateState]);
  
  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);
  
  const refresh = useCallback(() => {
    return fetchItems();
  }, [fetchItems]);
  
  // =====================================================
  // RETURN STATE AND ACTIONS
  // =====================================================
  
  return [
    state,
    {
      fetchItems,
      createItem,
      updateItem,
      deleteItem,
      bulkUpdate,
      bulkDelete,
      selectItem,
      selectAll,
      clearSelection,
      toggleSelection,
      setItems,
      clearError,
      refresh,
    }
  ];
}
// Reusable form validation hook with real-time feedback and advanced validation rules
'use client';

import { useState, useCallback, useEffect } from 'react';

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any, formData: any) => string | null;
  message?: string;
}

export interface ValidationSchema {
  [fieldName: string]: ValidationRule;
}

export interface ValidationErrors {
  [fieldName: string]: string;
}

export interface FormValidationOptions {
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  debounceMs?: number;
}

export interface FormValidationState {
  errors: ValidationErrors;
  touched: Set<string>;
  isValid: boolean;
  isSubmitting: boolean;
  submitCount: number;
}

export interface FormValidationActions {
  validateField: (fieldName: string, value: any, formData?: any) => string | null;
  validateForm: (formData: any) => ValidationErrors;
  setFieldError: (fieldName: string, error: string) => void;
  clearFieldError: (fieldName: string) => void;
  clearAllErrors: () => void;
  markFieldTouched: (fieldName: string) => void;
  markFormTouched: () => void;
  setSubmitting: (isSubmitting: boolean) => void;
  reset: () => void;
}

export function useFormValidation(
  schema: ValidationSchema,
  options: FormValidationOptions = {}
): [FormValidationState, FormValidationActions] {
  
  const {
    validateOnChange = true,
    validateOnBlur = true,
    debounceMs = 300,
  } = options;
  
  // =====================================================
  // STATE MANAGEMENT
  // =====================================================
  
  const [state, setState] = useState<FormValidationState>({
    errors: {},
    touched: new Set(),
    isValid: false,
    isSubmitting: false,
    submitCount: 0,
  });
  
  // =====================================================
  // VALIDATION LOGIC
  // =====================================================
  
  const validateField = useCallback((
    fieldName: string, 
    value: any, 
    formData: any = {}
  ): string | null => {
    const rule = schema[fieldName];
    if (!rule) return null;
    
    // Check required
    if (rule.required && (value === null || value === undefined || value === '')) {
      return rule.message || `${fieldName} is required`;
    }
    
    // Skip other validations if value is empty and not required
    if (!rule.required && (value === null || value === undefined || value === '')) {
      return null;
    }
    
    // String validations
    if (typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        return rule.message || `${fieldName} must be at least ${rule.minLength} characters`;
      }
      
      if (rule.maxLength && value.length > rule.maxLength) {
        return rule.message || `${fieldName} must be no more than ${rule.maxLength} characters`;
      }
      
      if (rule.pattern && !rule.pattern.test(value)) {
        return rule.message || `${fieldName} format is invalid`;
      }
    }
    
    // Number validations
    if (typeof value === 'number' || !isNaN(Number(value))) {
      const numValue = Number(value);
      
      if (rule.min !== undefined && numValue < rule.min) {
        return rule.message || `${fieldName} must be at least ${rule.min}`;
      }
      
      if (rule.max !== undefined && numValue > rule.max) {
        return rule.message || `${fieldName} must be no more than ${rule.max}`;
      }
    }
    
    // Custom validation
    if (rule.custom) {
      return rule.custom(value, formData);
    }
    
    return null;
  }, [schema]);
  
  const validateForm = useCallback((formData: any): ValidationErrors => {
    const errors: ValidationErrors = {};
    
    Object.keys(schema).forEach(fieldName => {
      const error = validateField(fieldName, formData[fieldName], formData);
      if (error) {
        errors[fieldName] = error;
      }
    });
    
    return errors;
  }, [schema, validateField]);
  
  // =====================================================
  // STATE ACTIONS
  // =====================================================
  
  const updateState = useCallback((updates: Partial<FormValidationState>) => {
    setState(prev => {
      const newState = { ...prev, ...updates };
      // Update isValid based on errors
      if ('errors' in updates) {
        newState.isValid = Object.keys(updates.errors || {}).length === 0;
      }
      return newState;
    });
  }, []);
  
  const setFieldError = useCallback((fieldName: string, error: string) => {
    updateState({
      errors: { ...state.errors, [fieldName]: error }
    });
  }, [state.errors, updateState]);
  
  const clearFieldError = useCallback((fieldName: string) => {
    const newErrors = { ...state.errors };
    delete newErrors[fieldName];
    updateState({ errors: newErrors });
  }, [state.errors, updateState]);
  
  const clearAllErrors = useCallback(() => {
    updateState({ errors: {} });
  }, [updateState]);
  
  const markFieldTouched = useCallback((fieldName: string) => {
    const newTouched = new Set(state.touched);
    newTouched.add(fieldName);
    updateState({ touched: newTouched });
  }, [state.touched, updateState]);
  
  const markFormTouched = useCallback(() => {
    const newTouched = new Set(Object.keys(schema));
    updateState({ touched: newTouched });
  }, [schema, updateState]);
  
  const setSubmitting = useCallback((isSubmitting: boolean) => {
    updateState({ 
      isSubmitting,
      submitCount: isSubmitting ? state.submitCount : state.submitCount + 1
    });
  }, [state.submitCount, updateState]);
  
  const reset = useCallback(() => {
    setState({
      errors: {},
      touched: new Set(),
      isValid: false,
      isSubmitting: false,
      submitCount: 0,
    });
  }, []);
  
  // =====================================================
  // HELPER FUNCTIONS FOR COMPONENTS
  // =====================================================
  
  const getFieldProps = useCallback((fieldName: string) => {
    const hasError = !!state.errors[fieldName];
    const isTouched = state.touched.has(fieldName);
    
    return {
      error: hasError ? state.errors[fieldName] : undefined,
      hasError,
      isTouched,
      showError: hasError && (isTouched || state.submitCount > 0),
    };
  }, [state.errors, state.touched, state.submitCount]);
  
  const createFieldValidator = useCallback((fieldName: string) => {
    let debounceTimer: NodeJS.Timeout;
    
    return {
      onChange: (value: any, formData?: any) => {
        if (!validateOnChange) return;
        
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
          const error = validateField(fieldName, value, formData);
          if (error) {
            setFieldError(fieldName, error);
          } else {
            clearFieldError(fieldName);
          }
        }, debounceMs);
      },
      
      onBlur: (value: any, formData?: any) => {
        markFieldTouched(fieldName);
        
        if (!validateOnBlur) return;
        
        const error = validateField(fieldName, value, formData);
        if (error) {
          setFieldError(fieldName, error);
        } else {
          clearFieldError(fieldName);
        }
      },
    };
  }, [
    validateOnChange, 
    validateOnBlur, 
    debounceMs, 
    validateField, 
    setFieldError, 
    clearFieldError, 
    markFieldTouched
  ]);
  
  // =====================================================
  // RETURN STATE AND ACTIONS
  // =====================================================
  
  return [
    {
      ...state,
      // Helper methods
      getFieldProps,
      createFieldValidator,
    } as FormValidationState & {
      getFieldProps: (fieldName: string) => {
        error?: string;
        hasError: boolean;
        isTouched: boolean;
        showError: boolean;
      };
      createFieldValidator: (fieldName: string) => {
        onChange: (value: any, formData?: any) => void;
        onBlur: (value: any, formData?: any) => void;
      };
    },
    {
      validateField,
      validateForm,
      setFieldError,
      clearFieldError,
      clearAllErrors,
      markFieldTouched,
      markFormTouched,
      setSubmitting,
      reset,
    }
  ];
}

// =====================================================
// COMMON VALIDATION HELPERS
// =====================================================

export const createEmailValidation = (): ValidationRule => ({
  required: true,
  pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  message: 'Please enter a valid email address',
});

export const createPasswordValidation = (minLength = 8): ValidationRule => ({
  required: true,
  minLength,
  pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]/,
  message: `Password must be at least ${minLength} characters with uppercase, lowercase, and number`,
});

export const createPriceValidation = (min = 0.01, max = 999.99): ValidationRule => ({
  required: true,
  min,
  max,
  custom: (value: any) => {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) {
      return 'Please enter a valid price';
    }
    return null;
  },
});

export const createUrlValidation = (required = false): ValidationRule => ({
  required,
  pattern: /^https?:\/\/.+/,
  message: 'Please enter a valid URL starting with http:// or https://',
});

export const createPhoneValidation = (): ValidationRule => ({
  required: true,
  pattern: /^\+?[\d\s\-\(\)]+$/,
  minLength: 10,
  message: 'Please enter a valid phone number',
});

export const createRequiredValidation = (fieldName: string): ValidationRule => ({
  required: true,
  message: `${fieldName} is required`,
});
// React Query hooks for order management with optimistic updates and real-time integration
// This provides server state management that works seamlessly with Supabase Realtime

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';
import type { 
  RealtimeOrder, 
  OrderEvent, 
  OrderStatus, 
  OrderDashboardFilters,
  StaffRole,
  RealtimeOrderItem 
} from '@/types/realtime-orders';

// API endpoints - these would be implemented in your backend
const API_BASE = '/api/v1/orders';

/**
 * Order API service with tenant isolation
 * All requests include tenant context from auth state
 */
class OrderAPIService {
  private tenantId: string;
  private authHeaders: Record<string, string>;
  
  constructor(tenantId: string, authToken: string) {
    this.tenantId = tenantId;
    this.authHeaders = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json',
    };
  }
  
  /**
   * Fetch orders with filters and pagination
   * Supports server-side filtering for performance
   */
  async fetchOrders(filters: OrderDashboardFilters = {}, page = 1, limit = 50): Promise<{
    orders: RealtimeOrder[];
    total: number;
    hasMore: boolean;
  }> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      tenant_id: this.tenantId,
    });
    
    // Add filter parameters
    if (filters.statuses?.length) {
      params.append('statuses', filters.statuses.join(','));
    }
    
    if (filters.staff_assigned?.length) {
      params.append('staff_assigned', filters.staff_assigned.join(','));
    }
    
    if (filters.room_numbers?.length) {
      params.append('room_numbers', filters.room_numbers.join(','));
    }
    
    if (filters.order_types?.length) {
      params.append('order_types', filters.order_types.join(','));
    }
    
    if (filters.date_range) {
      params.append('date_start', filters.date_range.start);
      params.append('date_end', filters.date_range.end);
    }
    
    if (filters.is_urgent_only) {
      params.append('urgent_only', 'true');
    }
    
    if (filters.search_query) {
      params.append('search', filters.search_query);
    }
    
    const response = await fetch(`${API_BASE}?${params}`, {
      headers: this.authHeaders,
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch orders: ${response.statusText}`);
    }
    
    return await response.json();
  }
  
  /**
   * Fetch single order with full details
   */
  async fetchOrder(orderId: string): Promise<RealtimeOrder> {
    const response = await fetch(`${API_BASE}/${orderId}?tenant_id=${this.tenantId}`, {
      headers: this.authHeaders,
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch order: ${response.statusText}`);
    }
    
    return await response.json();
  }
  
  /**
   * Update order status with audit trail
   */
  async updateOrderStatus(
    orderId: string, 
    newStatus: OrderStatus, 
    notes?: string,
    estimatedCompletionTime?: string
  ): Promise<RealtimeOrder> {
    const response = await fetch(`${API_BASE}/${orderId}/status`, {
      method: 'PATCH',
      headers: this.authHeaders,
      body: JSON.stringify({
        status: newStatus,
        notes,
        estimated_completion_time: estimatedCompletionTime,
        tenant_id: this.tenantId,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to update order status: ${response.statusText}`);
    }
    
    return await response.json();
  }
  
  /**
   * Assign order to staff member
   */
  async assignOrderToStaff(orderId: string, staffId: string): Promise<RealtimeOrder> {
    const response = await fetch(`${API_BASE}/${orderId}/assign`, {
      method: 'PATCH',
      headers: this.authHeaders,
      body: JSON.stringify({
        staff_assigned_id: staffId,
        tenant_id: this.tenantId,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to assign order: ${response.statusText}`);
    }
    
    return await response.json();
  }
  
  /**
   * Add notes to an order
   */
  async addOrderNotes(orderId: string, notes: string): Promise<OrderEvent> {
    const response = await fetch(`${API_BASE}/${orderId}/notes`, {
      method: 'POST',
      headers: this.authHeaders,
      body: JSON.stringify({
        notes,
        tenant_id: this.tenantId,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to add notes: ${response.statusText}`);
    }
    
    return await response.json();
  }
  
  /**
   * Fetch order events for activity timeline
   */
  async fetchOrderEvents(orderId: string): Promise<OrderEvent[]> {
    const response = await fetch(`${API_BASE}/${orderId}/events?tenant_id=${this.tenantId}`, {
      headers: this.authHeaders,
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch order events: ${response.statusText}`);
    }
    
    return await response.json();
  }
  
  /**
   * Bulk update multiple orders
   */
  async bulkUpdateOrders(orderIds: string[], updates: Partial<RealtimeOrder>): Promise<RealtimeOrder[]> {
    const response = await fetch(`${API_BASE}/bulk-update`, {
      method: 'PATCH',
      headers: this.authHeaders,
      body: JSON.stringify({
        order_ids: orderIds,
        updates: {
          ...updates,
          tenant_id: this.tenantId,
        },
      }),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to bulk update orders: ${response.statusText}`);
    }
    
    return await response.json();
  }
  
  /**
   * Update individual order item status (for kitchen workflow)
   */
  async updateOrderItemStatus(
    orderId: string, 
    itemId: string, 
    status: string
  ): Promise<RealtimeOrderItem> {
    const response = await fetch(`${API_BASE}/${orderId}/items/${itemId}/status`, {
      method: 'PATCH',
      headers: this.authHeaders,
      body: JSON.stringify({
        item_status: status,
        tenant_id: this.tenantId,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to update item status: ${response.statusText}`);
    }
    
    return await response.json();
  }
}

/**
 * React Query configuration for orders
 * Defines cache behavior and stale times optimized for real-time updates
 */
export const orderQueryConfig = {
  // Orders list query
  orders: {
    queryKey: (tenantId: string, filters: OrderDashboardFilters) => 
      ['orders', tenantId, filters],
    staleTime: 30 * 1000, // 30 seconds - short since we have real-time updates
    cacheTime: 5 * 60 * 1000, // 5 minutes
  },
  
  // Single order query
  order: {
    queryKey: (tenantId: string, orderId: string) => 
      ['order', tenantId, orderId],
    staleTime: 15 * 1000, // 15 seconds - very fresh for real-time
    cacheTime: 2 * 60 * 1000, // 2 minutes
  },
  
  // Order events timeline
  orderEvents: {
    queryKey: (tenantId: string, orderId: string) => 
      ['order-events', tenantId, orderId],
    staleTime: 10 * 1000, // 10 seconds - events should be very fresh
    cacheTime: 60 * 1000, // 1 minute
  },
};

/**
 * Custom hook for fetching orders with filters
 * Integrates with real-time updates and provides optimistic UI
 */
export function useOrders(
  tenantId: string, 
  authToken: string,
  filters: OrderDashboardFilters = {}
) {
  // Memoize API service to prevent recreation on every render
  const apiService = useMemo(() => 
    new OrderAPIService(tenantId, authToken), 
    [tenantId, authToken]
  );
  
  // Use infinite query for pagination support
  const query = useInfiniteQuery({
    queryKey: orderQueryConfig.orders.queryKey(tenantId, filters),
    queryFn: ({ pageParam = 1 }) => apiService.fetchOrders(filters, pageParam),
    getNextPageParam: (lastPage, allPages) => {
      return lastPage.hasMore ? allPages.length + 1 : undefined;
    },
    staleTime: orderQueryConfig.orders.staleTime,
    cacheTime: orderQueryConfig.orders.cacheTime,
    // Refetch when browser tab becomes visible
    refetchOnWindowFocus: true,
    // Keep previous data while loading new filters
    keepPreviousData: true,
  });
  
  // Flatten paginated data for easier consumption
  const orders = useMemo(() => {
    return query.data?.pages.flatMap(page => page.orders) || [];
  }, [query.data]);
  
  const totalOrders = query.data?.pages[0]?.total || 0;
  
  return {
    orders,
    totalOrders,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    fetchNextPage: query.fetchNextPage,
    hasNextPage: query.hasNextPage,
    isFetchingNextPage: query.isFetchingNextPage,
    refetch: query.refetch,
  };
}

/**
 * Custom hook for fetching a single order
 * Automatically refetches when order ID changes
 */
export function useOrder(tenantId: string, authToken: string, orderId: string | null) {
  const apiService = useMemo(() => 
    new OrderAPIService(tenantId, authToken), 
    [tenantId, authToken]
  );
  
  const query = useQuery({
    queryKey: orderQueryConfig.order.queryKey(tenantId, orderId || ''),
    queryFn: () => apiService.fetchOrder(orderId!),
    enabled: !!orderId, // Only fetch when orderId is provided
    staleTime: orderQueryConfig.order.staleTime,
    cacheTime: orderQueryConfig.order.cacheTime,
    refetchOnWindowFocus: true,
  });
  
  return {
    order: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
}

/**
 * Custom hook for order events timeline
 * Provides activity history for an order
 */
export function useOrderEvents(tenantId: string, authToken: string, orderId: string | null) {
  const apiService = useMemo(() => 
    new OrderAPIService(tenantId, authToken), 
    [tenantId, authToken]
  );
  
  const query = useQuery({
    queryKey: orderQueryConfig.orderEvents.queryKey(tenantId, orderId || ''),
    queryFn: () => apiService.fetchOrderEvents(orderId!),
    enabled: !!orderId,
    staleTime: orderQueryConfig.orderEvents.staleTime,
    cacheTime: orderQueryConfig.orderEvents.cacheTime,
  });
  
  return {
    events: query.data || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
}

/**
 * Mutation hook for updating order status
 * Provides optimistic updates and error rollback
 */
export function useUpdateOrderStatus(tenantId: string, authToken: string) {
  const queryClient = useQueryClient();
  const apiService = useMemo(() => 
    new OrderAPIService(tenantId, authToken), 
    [tenantId, authToken]
  );
  
  return useMutation({
    mutationFn: ({ 
      orderId, 
      newStatus, 
      notes, 
      estimatedCompletionTime 
    }: {
      orderId: string;
      newStatus: OrderStatus;
      notes?: string;
      estimatedCompletionTime?: string;
    }) => apiService.updateOrderStatus(orderId, newStatus, notes, estimatedCompletionTime),
    
    // Optimistic update - immediately update UI before server response
    onMutate: async ({ orderId, newStatus }) => {
      // Cancel any outgoing refetches for this order
      await queryClient.cancelQueries(['order', tenantId, orderId]);
      
      // Snapshot the previous value
      const previousOrder = queryClient.getQueryData(['order', tenantId, orderId]);
      
      // Optimistically update the order
      queryClient.setQueryData(['order', tenantId, orderId], (old: RealtimeOrder | undefined) => {
        if (!old) return old;
        return {
          ...old,
          status: newStatus,
          // Add optimistic timestamp
          updated_at: new Date().toISOString(),
        };
      });
      
      // Also update the orders list
      queryClient.setQueryData(
        ['orders', tenantId], 
        (old: any) => {
          if (!old) return old;
          return {
            ...old,
            pages: old.pages.map((page: any) => ({
              ...page,
              orders: page.orders.map((order: RealtimeOrder) => 
                order.id === orderId 
                  ? { ...order, status: newStatus, updated_at: new Date().toISOString() }
                  : order
              ),
            })),
          };
        }
      );
      
      // Return context for rollback
      return { previousOrder, orderId };
    },
    
    // Rollback on error
    onError: (err, variables, context) => {
      if (context?.previousOrder) {
        queryClient.setQueryData(['order', tenantId, context.orderId], context.previousOrder);
      }
      console.error('Failed to update order status:', err);
    },
    
    // Always refetch after mutation (success or error)
    onSettled: (data, error, variables) => {
      queryClient.invalidateQueries(['order', tenantId, variables.orderId]);
      queryClient.invalidateQueries(['orders', tenantId]);
    },
  });
}

/**
 * Mutation hook for assigning orders to staff
 */
export function useAssignOrderToStaff(tenantId: string, authToken: string) {
  const queryClient = useQueryClient();
  const apiService = useMemo(() => 
    new OrderAPIService(tenantId, authToken), 
    [tenantId, authToken]
  );
  
  return useMutation({
    mutationFn: ({ orderId, staffId }: { orderId: string; staffId: string }) => 
      apiService.assignOrderToStaff(orderId, staffId),
    
    onSuccess: (data, variables) => {
      // Update cached order
      queryClient.setQueryData(['order', tenantId, variables.orderId], data);
      
      // Invalidate orders list to reflect assignment
      queryClient.invalidateQueries(['orders', tenantId]);
    },
    
    onError: (error) => {
      console.error('Failed to assign order:', error);
    },
  });
}

/**
 * Mutation hook for adding notes to orders
 */
export function useAddOrderNotes(tenantId: string, authToken: string) {
  const queryClient = useQueryClient();
  const apiService = useMemo(() => 
    new OrderAPIService(tenantId, authToken), 
    [tenantId, authToken]
  );
  
  return useMutation({
    mutationFn: ({ orderId, notes }: { orderId: string; notes: string }) => 
      apiService.addOrderNotes(orderId, notes),
    
    onSuccess: (data, variables) => {
      // Invalidate order events to show new note
      queryClient.invalidateQueries(['order-events', tenantId, variables.orderId]);
      
      // Optionally update the main order query
      queryClient.invalidateQueries(['order', tenantId, variables.orderId]);
    },
    
    onError: (error) => {
      console.error('Failed to add notes:', error);
    },
  });
}

/**
 * Mutation hook for bulk operations
 */
export function useBulkUpdateOrders(tenantId: string, authToken: string) {
  const queryClient = useQueryClient();
  const apiService = useMemo(() => 
    new OrderAPIService(tenantId, authToken), 
    [tenantId, authToken]
  );
  
  return useMutation({
    mutationFn: ({ 
      orderIds, 
      updates 
    }: { 
      orderIds: string[]; 
      updates: Partial<RealtimeOrder> 
    }) => apiService.bulkUpdateOrders(orderIds, updates),
    
    onSuccess: (data) => {
      // Invalidate all relevant queries
      queryClient.invalidateQueries(['orders', tenantId]);
      
      // Update individual order caches
      data.forEach(order => {
        queryClient.setQueryData(['order', tenantId, order.id], order);
      });
    },
    
    onError: (error) => {
      console.error('Failed to bulk update orders:', error);
    },
  });
}

/**
 * Hook to integrate React Query with real-time updates
 * This keeps the cache in sync with Supabase Realtime
 */
export function useOrderRealtimeSync(
  tenantId: string,
  realtimeManager: any // SupabaseRealtimeManager
) {
  const queryClient = useQueryClient();
  
  const handleOrderChanged = useCallback((data: any) => {
    const { order, eventType } = data;
    
    // Update individual order cache
    if (order) {
      queryClient.setQueryData(['order', tenantId, order.id], order);
    }
    
    // Invalidate orders list to trigger refetch with new data
    queryClient.invalidateQueries(['orders', tenantId]);
    
    console.log(`🔄 Real-time order ${eventType}:`, order?.order_number);
  }, [queryClient, tenantId]);
  
  const handleOrderEvent = useCallback((data: any) => {
    const { event } = data;
    
    // Invalidate order events cache to show new event
    queryClient.invalidateQueries(['order-events', tenantId, event.order_id]);
    
    console.log(`📝 Real-time order event:`, event.event_type);
  }, [queryClient, tenantId]);
  
  // Set up real-time listeners
  React.useEffect(() => {
    if (!realtimeManager) return;
    
    const unsubscribeOrderChanged = realtimeManager.on('order_changed', handleOrderChanged);
    const unsubscribeOrderEvent = realtimeManager.on('order_event', handleOrderEvent);
    
    return () => {
      unsubscribeOrderChanged();
      unsubscribeOrderEvent();
    };
  }, [realtimeManager, handleOrderChanged, handleOrderEvent]);
}
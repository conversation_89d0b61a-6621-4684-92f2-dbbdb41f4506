// Supabase Realtime configuration for order management
// This handles real-time subscriptions with proper error handling and reconnection logic

import { createClient, RealtimeChannel, RealtimeChannelSendResponse } from '@supabase/supabase-js';
import type { 
  RealtimeSubscriptionConfig,
  OrderRealtimePayload,
  OrderEventRealtimePayload,
  RealtimeError,
  RealtimeMessage,
  StaffRole,
  OrderStatus
} from '@/types/realtime-orders';

/**
 * Enhanced Supabase client specifically configured for real-time order management
 * Extends the existing Supabase setup with real-time optimizations
 */
class SupabaseRealtimeManager {
  private client: any; // Will be the Supabase client
  private channels: Map<string, RealtimeChannel> = new Map();
  private config: RealtimeSubscriptionConfig | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second, exponential backoff
  private heartbeatInterval: NodeJS.Timeout | null = null;
  
  // Event listeners for real-time events
  private eventListeners: Map<string, Function[]> = new Map();
  
  constructor(supabaseClient: any) {
    this.client = supabaseClient;
    
    // Set up global real-time event handlers
    this.setupGlobalEventHandlers();
  }
  
  /**
   * Initialize real-time subscriptions for order management
   * This creates tenant-scoped subscriptions based on staff role
   */
  async initialize(config: RealtimeSubscriptionConfig): Promise<void> {
    try {
      this.config = config;
      
      // Clean up existing subscriptions
      await this.cleanup();
      
      console.log(`🔄 Initializing real-time subscriptions for tenant: ${config.tenant_id}, role: ${config.staff_role}`);
      
      // Subscribe to orders table changes
      if (config.subscribeToOrders) {
        await this.subscribeToOrders();
      }
      
      // Subscribe to order events (status changes, notes, etc.)
      if (config.subscribeToOrderEvents) {
        await this.subscribeToOrderEvents();
      }
      
      // Subscribe to order items for kitchen workflow
      if (config.subscribeToOrderItems) {
        await this.subscribeToOrderItems();
      }
      
      // Set up presence tracking if enabled
      if (config.enablePresence) {
        await this.setupPresenceTracking();
      }
      
      // Start heartbeat for connection monitoring
      this.startHeartbeat();
      
      console.log(`✅ Real-time subscriptions initialized successfully`);
      this.emit('connected', { config });
      
    } catch (error) {
      console.error('❌ Failed to initialize real-time subscriptions:', error);
      this.emit('error', { 
        code: 'INIT_FAILED', 
        message: 'Failed to initialize real-time subscriptions',
        details: error,
        recoverable: true 
      });
      
      // Attempt to reconnect after delay
      setTimeout(() => this.attemptReconnect(), this.reconnectDelay);
    }
  }
  
  /**
   * Subscribe to orders table changes
   * Filters based on tenant and role-specific status access
   */
  private async subscribeToOrders(): Promise<void> {
    if (!this.config) throw new Error('Realtime not initialized');
    
    const channelName = `orders:${this.config.tenant_id}`;
    
    // Create channel with tenant filtering
    const channel = this.client
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*', // Listen to INSERT, UPDATE, DELETE
          schema: 'public',
          table: 'Order',
          filter: `tenant_id=eq.${this.config.tenant_id}`,
        },
        (payload: OrderRealtimePayload) => {
          console.log('📦 Order change received:', payload);
          this.handleOrderChange(payload);
        }
      );
    
    // Subscribe and handle connection states
    const subscriptionResponse = await channel.subscribe((status) => {
      console.log(`🔗 Orders subscription status: ${status}`);
      
      if (status === 'SUBSCRIBED') {
        this.emit('subscription_success', { channel: channelName, table: 'Order' });
      } else if (status === 'CHANNEL_ERROR') {
        this.emit('subscription_error', { 
          channel: channelName, 
          table: 'Order',
          error: 'Channel connection failed'
        });
      } else if (status === 'TIMED_OUT') {
        this.emit('subscription_timeout', { channel: channelName });
        this.attemptReconnect();
      }
    });
    
    this.channels.set(channelName, channel);
  }
  
  /**
   * Subscribe to order events (status changes, notes, assignments)
   * This provides detailed audit trail for real-time updates
   */
  private async subscribeToOrderEvents(): Promise<void> {
    if (!this.config) throw new Error('Realtime not initialized');
    
    const channelName = `order_events:${this.config.tenant_id}`;
    
    const channel = this.client
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT', // Only new events, no updates/deletes
          schema: 'public',
          table: 'OrderEvent',
          filter: `tenant_id=eq.${this.config.tenant_id}`,
        },
        (payload: OrderEventRealtimePayload) => {
          console.log('📝 Order event received:', payload);
          this.handleOrderEvent(payload);
        }
      );
    
    await channel.subscribe((status) => {
      console.log(`🔗 Order events subscription status: ${status}`);
      
      if (status === 'SUBSCRIBED') {
        this.emit('subscription_success', { channel: channelName, table: 'OrderEvent' });
      }
    });
    
    this.channels.set(channelName, channel);
  }
  
  /**
   * Subscribe to order items for detailed kitchen workflow
   * Allows tracking individual item preparation status
   */
  private async subscribeToOrderItems(): Promise<void> {
    if (!this.config) throw new Error('Realtime not initialized');
    
    const channelName = `order_items:${this.config.tenant_id}`;
    
    const channel = this.client
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE', // Focus on status updates for items
          schema: 'public',
          table: 'OrderItem',
          filter: `tenant_id=eq.${this.config.tenant_id}`,
        },
        (payload: any) => {
          console.log('🍽️ Order item change received:', payload);
          this.handleOrderItemChange(payload);
        }
      );
    
    await channel.subscribe((status) => {
      console.log(`🔗 Order items subscription status: ${status}`);
    });
    
    this.channels.set(channelName, channel);
  }
  
  /**
   * Set up presence tracking to see who's online
   * Useful for collaborative order management
   */
  private async setupPresenceTracking(): Promise<void> {
    if (!this.config) throw new Error('Realtime not initialized');
    
    const channelName = `presence:staff:${this.config.tenant_id}`;
    
    const channel = this.client
      .channel(channelName)
      .on('presence', { event: 'sync' }, () => {
        const state = channel.presenceState();
        console.log('👥 Staff presence sync:', state);
        this.emit('presence_sync', { onlineStaff: state });
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        console.log('✅ Staff joined:', key, newPresences);
        this.emit('staff_joined', { staffId: key, presence: newPresences });
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        console.log('❌ Staff left:', key, leftPresences);
        this.emit('staff_left', { staffId: key, presence: leftPresences });
      });
    
    await channel.subscribe(async (status) => {
      if (status === 'SUBSCRIBED') {
        // Track current user's presence
        await channel.track({
          staff_id: this.config!.staff_id,
          staff_role: this.config!.staff_role,
          online_at: new Date().toISOString(),
        });
      }
    });
    
    this.channels.set(channelName, channel);
  }
  
  /**
   * Handle order table changes with role-based filtering
   * Only processes orders that the current user should see
   */
  private handleOrderChange(payload: OrderRealtimePayload): void {
    if (!this.config) return;
    
    try {
      // Apply role-based filtering
      if (payload.new && !this.canAccessOrder(payload.new.status)) {
        console.log(`🚫 Order access denied for role ${this.config.staff_role}, status: ${payload.new.status}`);
        return;
      }
      
      // Emit the order change to subscribers
      this.emit('order_changed', {
        eventType: payload.eventType,
        order: payload.new,
        previousOrder: payload.old,
      });
      
      // Handle specific event types
      switch (payload.eventType) {
        case 'INSERT':
          console.log('🆕 New order created:', payload.new?.order_number);
          this.emit('new_order', { order: payload.new });
          break;
          
        case 'UPDATE':
          console.log('🔄 Order updated:', payload.new?.order_number);
          this.emit('order_updated', { 
            order: payload.new, 
            previousOrder: payload.old 
          });
          
          // Check for status changes
          if (payload.old?.status !== payload.new?.status) {
            this.emit('order_status_changed', {
              order: payload.new,
              oldStatus: payload.old?.status,
              newStatus: payload.new?.status,
            });
          }
          break;
          
        case 'DELETE':
          console.log('🗑️ Order deleted:', payload.old?.order_number);
          this.emit('order_deleted', { order: payload.old });
          break;
      }
      
    } catch (error) {
      console.error('❌ Error handling order change:', error);
      this.emit('error', {
        code: 'ORDER_CHANGE_ERROR',
        message: 'Failed to process order change',
        details: error,
        recoverable: true,
      });
    }
  }
  
  /**
   * Handle order events (status changes, notes, assignments)
   * Provides detailed activity feed for orders
   */
  private handleOrderEvent(payload: OrderEventRealtimePayload): void {
    if (!payload.new) return;
    
    console.log(`📋 Order event: ${payload.new.event_type} for order ${payload.new.order_id}`);
    
    this.emit('order_event', { event: payload.new });
    
    // Handle specific event types
    switch (payload.new.event_type) {
      case 'STATUS_CHANGED':
        this.emit('status_change_event', { event: payload.new });
        break;
        
      case 'ASSIGNED_TO_STAFF':
        this.emit('assignment_event', { event: payload.new });
        break;
        
      case 'NOTES_ADDED':
        this.emit('notes_event', { event: payload.new });
        break;
        
      case 'CUSTOMER_NOTIFIED':
        this.emit('notification_event', { event: payload.new });
        break;
    }
  }
  
  /**
   * Handle order item changes for kitchen workflow
   */
  private handleOrderItemChange(payload: any): void {
    console.log('🔄 Order item status changed:', payload);
    this.emit('order_item_changed', { 
      item: payload.new, 
      previousItem: payload.old 
    });
  }
  
  /**
   * Check if current user can access an order based on its status
   * Implements role-based access control for real-time updates
   */
  private canAccessOrder(status: OrderStatus): boolean {
    if (!this.config) return false;
    
    // Import the role access rules
    const ROLE_ORDER_ACCESS = {
      ADMIN: {
        canView: ['PENDING', 'CONFIRMED', 'PREPARING', 'READY', 'DELIVERED', 'CANCELLED'],
      },
      MANAGER: {
        canView: ['PENDING', 'CONFIRMED', 'PREPARING', 'READY', 'DELIVERED', 'CANCELLED'],
      },
      CHEF: {
        canView: ['CONFIRMED', 'PREPARING', 'READY'],
      },
      WAITER: {
        canView: ['READY', 'DELIVERED'],
      },
      RECEPTIONIST: {
        canView: ['PENDING', 'CONFIRMED', 'DELIVERED'],
      },
    };
    
    const roleAccess = ROLE_ORDER_ACCESS[this.config.staff_role];
    return roleAccess?.canView.includes(status) || false;
  }
  
  /**
   * Start heartbeat monitoring for connection health
   */
  private startHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    
    const interval = this.config?.heartbeatInterval || 30000; // 30 seconds default
    
    this.heartbeatInterval = setInterval(() => {
      this.checkConnectionHealth();
    }, interval);
  }
  
  /**
   * Check connection health and emit status
   */
  private checkConnectionHealth(): void {
    const channelCount = this.channels.size;
    const isConnected = channelCount > 0 && Array.from(this.channels.values())
      .every(channel => channel.state === 'joined');
    
    this.emit('heartbeat', {
      isConnected,
      channelCount,
      timestamp: new Date().toISOString(),
    });
    
    if (!isConnected && this.config) {
      console.warn('⚠️ Connection health check failed, attempting reconnect...');
      this.attemptReconnect();
    }
  }
  
  /**
   * Attempt to reconnect with exponential backoff
   */
  private async attemptReconnect(): Promise<void> {
    if (!this.config || this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Max reconnection attempts reached');
      this.emit('reconnect_failed', { attempts: this.reconnectAttempts });
      return;
    }
    
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`🔄 Reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
    
    setTimeout(async () => {
      try {
        await this.initialize(this.config!);
        this.reconnectAttempts = 0; // Reset on successful reconnection
        this.emit('reconnected', { attempts: this.reconnectAttempts });
      } catch (error) {
        console.error('❌ Reconnection failed:', error);
        this.attemptReconnect(); // Try again
      }
    }, delay);
  }
  
  /**
   * Set up global event handlers for connection state
   */
  private setupGlobalEventHandlers(): void {
    // Handle browser visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible' && this.config) {
        console.log('👁️ Browser tab became visible, checking connection...');
        this.checkConnectionHealth();
      }
    });
    
    // Handle network status changes
    window.addEventListener('online', () => {
      console.log('🌐 Network connection restored');
      if (this.config) {
        this.attemptReconnect();
      }
    });
    
    window.addEventListener('offline', () => {
      console.log('📡 Network connection lost');
      this.emit('network_offline', {});
    });
  }
  
  /**
   * Send a message through real-time channel
   * Useful for staff coordination and system alerts
   */
  async sendMessage(channelName: string, message: RealtimeMessage): Promise<RealtimeChannelSendResponse> {
    const channel = this.channels.get(channelName);
    if (!channel) {
      throw new Error(`Channel ${channelName} not found`);
    }
    
    return await channel.send({
      type: 'broadcast',
      event: 'message',
      payload: message,
    });
  }
  
  /**
   * Update order status through real-time channel
   * Provides immediate feedback while API call is in progress
   */
  async updateOrderStatus(orderId: string, newStatus: OrderStatus, notes?: string): Promise<void> {
    if (!this.config) throw new Error('Realtime not initialized');
    
    // Send optimistic update through real-time
    const message: RealtimeMessage = {
      type: 'ORDER_UPDATE',
      payload: {
        orderId,
        newStatus,
        notes,
        updatedBy: this.config.staff_id,
        updatedByRole: this.config.staff_role,
        timestamp: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
      sender_id: this.config.staff_id,
    };
    
    const channelName = `orders:${this.config.tenant_id}`;
    await this.sendMessage(channelName, message);
  }
  
  /**
   * Event subscription system for components to listen to real-time events
   */
  on(eventName: string, callback: Function): () => void {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, []);
    }
    
    this.eventListeners.get(eventName)!.push(callback);
    
    // Return unsubscribe function
    return () => {
      const listeners = this.eventListeners.get(eventName);
      if (listeners) {
        const index = listeners.indexOf(callback);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    };
  }
  
  /**
   * Emit events to all subscribers
   */
  private emit(eventName: string, data: any): void {
    const listeners = this.eventListeners.get(eventName);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${eventName}:`, error);
        }
      });
    }
  }
  
  /**
   * Clean up all subscriptions and connections
   */
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up real-time subscriptions...');
    
    // Clear heartbeat
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    // Unsubscribe from all channels
    for (const [name, channel] of this.channels) {
      try {
        await channel.unsubscribe();
        console.log(`✅ Unsubscribed from ${name}`);
      } catch (error) {
        console.error(`❌ Error unsubscribing from ${name}:`, error);
      }
    }
    
    this.channels.clear();
    this.eventListeners.clear();
    this.reconnectAttempts = 0;
    
    this.emit('disconnected', {});
  }
  
  /**
   * Get current connection status and metrics
   */
  getStatus() {
    return {
      isInitialized: !!this.config,
      channelCount: this.channels.size,
      channels: Array.from(this.channels.keys()),
      reconnectAttempts: this.reconnectAttempts,
      config: this.config,
    };
  }
}

// Export singleton instance
let realtimeManager: SupabaseRealtimeManager | null = null;

export const createRealtimeManager = (supabaseClient: any): SupabaseRealtimeManager => {
  if (!realtimeManager) {
    realtimeManager = new SupabaseRealtimeManager(supabaseClient);
  }
  return realtimeManager;
};

export const getRealtimeManager = (): SupabaseRealtimeManager | null => {
  return realtimeManager;
};

export type { SupabaseRealtimeManager };
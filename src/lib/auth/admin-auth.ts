// Enhanced Supabase authentication for tenant admin dashboard
import { createClient } from '@supabase/supabase-js';
import type { 
  SupabaseClient, 
  Session, 
  User,
  AuthError as SupabaseAuthError 
} from '@supabase/supabase-js';
import type { 
  AuthUser, 
  AuthResponse, 
  LoginCredentials, 
  CustomClaims, 
  TenantInfo,
  AdminRole,
  SecuritySettings,
  AuditLogEntry,
  LoginAttempt,
  SessionInfo,
  TwoFactorAuth,
  AdminPermission,
  ROLE_PERMISSIONS
} from '@/types/auth';

export class TenantAdminAuth {
  private supabase: SupabaseClient;
  private securitySettings: SecuritySettings;

  constructor(supabaseUrl: string, supabaseAnonKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce'
      }
    });

    // Default security settings
    this.securitySettings = {
      requireTwoFactor: false,
      sessionTimeout: 480, // 8 hours
      passwordMinLength: 8,
      passwordRequireSpecialChars: true,
      maxLoginAttempts: 5,
      lockoutDuration: 30,
      requireReauthForSensitiveActions: true,
      allowMultipleSessions: false,
    };
  }

  // =====================================================
  // CORE AUTHENTICATION METHODS
  // =====================================================

  /**
   * Admin login with tenant validation
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse<AuthUser>> {
    try {
      // Step 1: Check for account lockout
      const lockoutCheck = await this.checkAccountLockout(credentials.email);
      if (lockoutCheck.error) {
        return { error: lockoutCheck.error };
      }

      // Step 2: Validate tenant access
      if (credentials.tenantSlug) {
        const tenantCheck = await this.validateTenantAccess(
          credentials.email, 
          credentials.tenantSlug
        );
        if (tenantCheck.error) {
          await this.logLoginAttempt(credentials.email, false, tenantCheck.error);
          return { error: tenantCheck.error };
        }
      }

      // Step 3: Authenticate with Supabase
      const { data: authData, error: authError } = await this.supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (authError) {
        await this.logLoginAttempt(credentials.email, false, authError.message);
        return { error: this.formatAuthError(authError) };
      }

      if (!authData.user || !authData.session) {
        await this.logLoginAttempt(credentials.email, false, 'No session created');
        return { error: 'Authentication failed' };
      }

      // Step 4: Verify admin permissions
      const user = await this.buildAuthUser(authData.user, authData.session);
      const hasAdminAccess = await this.verifyAdminAccess(user);
      
      if (!hasAdminAccess) {
        await this.supabase.auth.signOut();
        await this.logLoginAttempt(credentials.email, false, 'Insufficient permissions');
        return { error: 'Access denied: Admin permissions required' };
      }

      // Step 5: Set tenant context if specified
      if (credentials.tenantSlug && user.claims.tenant_slug !== credentials.tenantSlug) {
        const switchResult = await this.switchTenantContext(user.id, credentials.tenantSlug);
        if (switchResult.error) {
          await this.supabase.auth.signOut();
          return { error: switchResult.error };
        }
        
        // Refresh user with new context
        const refreshedSession = await this.getSession();
        if (refreshedSession) {
          const refreshedUser = await this.buildAuthUser(authData.user, refreshedSession);
          user.claims = refreshedUser.claims;
        }
      }

      // Step 6: Handle session persistence
      if (!credentials.rememberMe) {
        // Set session to expire when browser closes
        await this.setSessionExpiry(24); // 24 hours for non-persistent
      }

      // Step 7: Log successful login
      await this.logLoginAttempt(credentials.email, true);
      await this.createAuditLog(user.id, 'user:login', 'session', null, {
        tenant_slug: credentials.tenantSlug,
        remember_me: credentials.rememberMe
      });

      return { data: user };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      await this.logLoginAttempt(credentials.email, false, errorMessage);
      return { error: errorMessage };
    }
  }

  /**
   * Logout with cleanup
   */
  async logout(): Promise<void> {
    try {
      const session = await this.getSession();
      const user = session ? await this.getCurrentUser() : null;

      if (user) {
        await this.createAuditLog(user.id, 'user:logout', 'session');
        await this.invalidateSession(session.access_token);
      }

      await this.supabase.auth.signOut();
    } catch (error) {
      console.error('Logout error:', error);
      // Force signout even if audit logging fails
      await this.supabase.auth.signOut();
    }
  }

  /**
   * Refresh session and validate permissions
   */
  async refreshSession(): Promise<AuthResponse<AuthUser>> {
    try {
      const { data, error } = await this.supabase.auth.refreshSession();
      
      if (error || !data.session || !data.user) {
        return { error: 'Session refresh failed' };
      }

      const user = await this.buildAuthUser(data.user, data.session);
      
      // Re-verify admin access on refresh
      const hasAdminAccess = await this.verifyAdminAccess(user);
      if (!hasAdminAccess) {
        await this.supabase.auth.signOut();
        return { error: 'Admin access revoked' };
      }

      return { data: user };

    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Session refresh failed' };
    }
  }

  // =====================================================
  // USER AND SESSION MANAGEMENT
  // =====================================================

  /**
   * Get current session
   */
  async getSession(): Promise<Session | null> {
    const { data: { session } } = await this.supabase.auth.getSession();
    return session;
  }

  /**
   * Get current authenticated user
   */
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      const session = await this.getSession();
      
      if (!user || !session) return null;
      
      return await this.buildAuthUser(user, session);
    } catch (error) {
      return null;
    }
  }

  /**
   * Build AuthUser from Supabase user and session
   */
  private async buildAuthUser(user: User, session: Session): Promise<AuthUser> {
    const claims = await this.extractCustomClaims(user);
    
    return {
      id: user.id,
      email: user.email!,
      name: user.user_metadata?.name || user.user_metadata?.full_name,
      avatar_url: user.user_metadata?.avatar_url,
      phone: user.phone,
      last_sign_in_at: user.last_sign_in_at,
      email_confirmed_at: user.email_confirmed_at,
      created_at: user.created_at!,
      updated_at: user.updated_at!,
      claims,
      raw_user_meta_data: user.user_metadata,
    };
  }

  /**
   * Extract custom claims from user metadata
   */
  private async extractCustomClaims(user: User): Promise<CustomClaims> {
    // In production, these would come from JWT claims or database lookup
    const metadata = user.app_metadata || {};
    const userMetadata = user.user_metadata || {};
    
    // Get user's tenant and role information from database
    const { data: userProfile } = await this.supabase
      .from('AdminUsers')
      .select(`
        id,
        tenant_id,
        admin_role,
        permissions,
        is_active,
        Tenant:tenant_id (
          id,
          name,
          slug,
          isActive
        )
      `)
      .eq('auth_user_id', user.id)
      .eq('is_active', true)
      .single();

    const claims: CustomClaims = {
      user_id: user.id,
      user_type: 'staff',
      role: 'staff',
      admin_role: userProfile?.admin_role || 'MANAGER',
      tenant_id: userProfile?.tenant_id,
      tenant_name: userProfile?.Tenant?.name,
      tenant_slug: userProfile?.Tenant?.slug,
      staff_id: userProfile?.id,
      permissions: userProfile?.permissions || [],
      tenants: userProfile?.tenants || [userProfile?.tenant_id].filter(Boolean),
    };

    return claims;
  }

  // =====================================================
  // ROLE AND PERMISSION MANAGEMENT
  // =====================================================

  /**
   * Check if user has specific role
   */
  async hasRole(user: AuthUser, role: string): Promise<boolean> {
    const { claims } = user;
    
    if (role === 'admin' || role === 'tenant_admin') {
      return claims.admin_role === 'TENANT_ADMIN' || claims.admin_role === 'SUPER_ADMIN';
    }
    
    if (role === 'super_admin') {
      return claims.admin_role === 'SUPER_ADMIN';
    }
    
    return claims.admin_role === role || claims.staff_role === role || claims.role === role;
  }

  /**
   * Check if user has specific permission
   */
  async hasPermission(user: AuthUser, permission: AdminPermission): Promise<boolean> {
    const { claims } = user;
    
    // Super admin has all permissions
    if (claims.admin_role === 'SUPER_ADMIN') {
      return true;
    }
    
    // Check role-based permissions
    const rolePermissions = claims.admin_role ? ROLE_PERMISSIONS[claims.admin_role] : [];
    if (rolePermissions.includes(permission)) {
      return true;
    }
    
    // Check explicit permissions
    return claims.permissions?.includes(permission) || false;
  }

  /**
   * Check if user has any of the specified permissions
   */
  async hasAnyPermission(user: AuthUser, permissions: AdminPermission[]): Promise<boolean> {
    for (const permission of permissions) {
      if (await this.hasPermission(user, permission)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Check if user has all specified permissions
   */
  async hasAllPermissions(user: AuthUser, permissions: AdminPermission[]): Promise<boolean> {
    for (const permission of permissions) {
      if (!(await this.hasPermission(user, permission))) {
        return false;
      }
    }
    return true;
  }

  /**
   * Verify user has admin access
   */
  private async verifyAdminAccess(user: AuthUser): Promise<boolean> {
    const { claims } = user;
    
    // Must be staff with admin role
    if (claims.role !== 'staff') return false;
    
    // Must have admin role
    const validAdminRoles: AdminRole[] = ['SUPER_ADMIN', 'TENANT_ADMIN', 'MANAGER'];
    if (!claims.admin_role || !validAdminRoles.includes(claims.admin_role)) {
      return false;
    }
    
    // Verify user is active in database
    const { data: adminUser } = await this.supabase
      .from('AdminUsers')
      .select('is_active, Tenant!inner(isActive)')
      .eq('auth_user_id', user.id)
      .eq('is_active', true)
      .single();

    return adminUser?.is_active && adminUser.Tenant?.isActive;
  }

  // =====================================================
  // TENANT MANAGEMENT
  // =====================================================

  /**
   * Switch tenant context
   */
  async switchTenantContext(userId: string, tenantSlug: string): Promise<AuthResponse<boolean>> {
    try {
      const { data, error } = await this.supabase.rpc('switch_admin_tenant_context', {
        p_user_id: userId,
        p_tenant_slug: tenantSlug
      });

      if (error) throw error;

      if (!data) {
        return { error: 'Access denied to tenant' };
      }

      // Refresh session to get new claims
      await this.supabase.auth.refreshSession();

      return { data: true };
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Tenant switch failed' };
    }
  }

  /**
   * Get accessible tenants for user
   */
  async getAccessibleTenants(userId: string): Promise<TenantInfo[]> {
    try {
      const { data, error } = await this.supabase
        .from('AdminUsers')
        .select(`
          Tenant (
            id,
            name,
            slug,
            logoUrl,
            settings,
            isActive
          )
        `)
        .eq('auth_user_id', userId)
        .eq('is_active', true);

      if (error) throw error;

      return data?.map(item => item.Tenant).filter(Boolean) || [];
    } catch (error) {
      console.error('Error fetching accessible tenants:', error);
      return [];
    }
  }

  /**
   * Validate tenant access for user
   */
  private async validateTenantAccess(email: string, tenantSlug: string): Promise<AuthResponse<boolean>> {
    try {
      const { data, error } = await this.supabase.rpc('validate_admin_tenant_access', {
        p_email: email,
        p_tenant_slug: tenantSlug
      });

      if (error) throw error;

      if (!data) {
        return { error: 'Access denied to tenant' };
      }

      return { data: true };
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Tenant validation failed' };
    }
  }

  // =====================================================
  // SECURITY AND AUDIT
  // =====================================================

  /**
   * Check for account lockout
   */
  private async checkAccountLockout(email: string): Promise<AuthResponse<boolean>> {
    try {
      const { data, error } = await this.supabase
        .from('LoginAttempts')
        .select('*')
        .eq('email', email)
        .eq('success', false)
        .gte('created_at', new Date(Date.now() - this.securitySettings.lockoutDuration * 60000).toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;

      const recentFailures = data?.length || 0;

      if (recentFailures >= this.securitySettings.maxLoginAttempts) {
        return { error: `Account locked due to too many failed attempts. Try again in ${this.securitySettings.lockoutDuration} minutes.` };
      }

      return { data: true };
    } catch (error) {
      return { data: true }; // Allow login if lockout check fails
    }
  }

  /**
   * Log login attempt
   */
  private async logLoginAttempt(email: string, success: boolean, errorMessage?: string): Promise<void> {
    try {
      const attempt: Omit<LoginAttempt, 'id' | 'created_at'> = {
        email,
        success,
        error_message: errorMessage,
        ip_address: await this.getClientIP(),
      };

      await this.supabase.from('LoginAttempts').insert(attempt);
    } catch (error) {
      console.error('Failed to log login attempt:', error);
    }
  }

  /**
   * Create audit log entry
   */
  async createAuditLog(
    userId: string,
    action: string,
    resource: string,
    resourceId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      const entry: Omit<AuditLogEntry, 'id' | 'created_at'> = {
        user_id: userId,
        tenant_id: (await this.getCurrentUser())?.claims.tenant_id || '',
        action,
        resource,
        resource_id: resourceId,
        ip_address: await this.getClientIP(),
        user_agent: navigator.userAgent,
        metadata,
      };

      await this.supabase.from('AuditLogs').insert(entry);
    } catch (error) {
      console.error('Failed to create audit log:', error);
    }
  }

  /**
   * Set session expiry
   */
  private async setSessionExpiry(hours: number): Promise<void> {
    // This would typically be handled by configuring Supabase JWT expiry
    // For now, we'll store it in session metadata
    try {
      await this.supabase.auth.updateUser({
        data: {
          session_expires_at: new Date(Date.now() + hours * 60 * 60 * 1000).toISOString()
        }
      });
    } catch (error) {
      console.error('Failed to set session expiry:', error);
    }
  }

  /**
   * Invalidate session
   */
  private async invalidateSession(accessToken: string): Promise<void> {
    try {
      // In production, you'd invalidate the token server-side
      await this.supabase.from('InvalidatedTokens').insert({
        token_hash: await this.hashToken(accessToken),
        invalidated_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to invalidate session:', error);
    }
  }

  /**
   * Get client IP address
   */
  private async getClientIP(): Promise<string> {
    try {
      // In production, this would be handled server-side
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip || 'unknown';
    } catch {
      return 'unknown';
    }
  }

  /**
   * Hash token for security
   */
  private async hashToken(token: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(token);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Format authentication error
   */
  private formatAuthError(error: SupabaseAuthError): string {
    switch (error.message) {
      case 'Invalid login credentials':
        return 'Invalid email or password';
      case 'Email not confirmed':
        return 'Please check your email and click the confirmation link';
      case 'Too many requests':
        return 'Too many login attempts. Please try again later';
      default:
        return error.message;
    }
  }

  // =====================================================
  // AUTH STATE SUBSCRIPTION
  // =====================================================

  /**
   * Subscribe to auth state changes
   */
  onAuthStateChange(callback: (event: string, session: Session | null) => void) {
    return this.supabase.auth.onAuthStateChange((event, session) => {
      callback(event, session);
    });
  }
}

// Export singleton instance
export const tenantAdminAuth = new TenantAdminAuth(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);
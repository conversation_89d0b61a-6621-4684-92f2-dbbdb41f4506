// BHEEMDINE Supabase Authentication Implementation
// Handles Guest, Customer, and Staff authentication flows

import { createClient } from '@supabase/supabase-js';
import type { SupabaseClient, Session, User } from '@supabase/supabase-js';

// Types for our auth system
export interface CustomClaims {
  user_id: string;
  tenant_id?: string;
  tenant_slug?: string;
  tenant_name?: string;
  user_type?: 'guest' | 'customer' | 'staff';
  role?: 'guest' | 'customer' | 'staff' | 'anonymous';
  staff_role?: 'ADMIN' | 'MANAGER' | 'CHEF' | 'WAITER' | 'RECEPTIONIST';
  staff_id?: string;
  customer_id?: string;
  room_id?: string;
}

export interface AuthResponse<T = any> {
  data?: T;
  error?: string;
}

export class BheemdineAuth {
  private supabase: SupabaseClient;

  constructor(supabaseUrl: string, supabaseAnonKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
      }
    });
  }

  // =====================================================
  // GUEST AUTHENTICATION FLOW
  // =====================================================

  /**
   * Validate QR code and get room/tenant information
   */
  async validateQRCode(qrCode: string): Promise<AuthResponse<{
    valid: boolean;
    tenant_id: string;
    room_id: string;
    room_number: string;
    tenant_name: string;
  }>> {
    try {
      const { data, error } = await this.supabase.rpc('validate_qr_access', {
        p_qr_code: qrCode
      });

      if (error) throw error;

      if (!data || data.length === 0 || !data[0].valid) {
        return { error: 'Invalid QR code' };
      }

      return { data: data[0] };
    } catch (error) {
      return { error: error.message };
    }
  }

  /**
   * Create guest user session after QR scan
   */
  async createGuestSession(
    tenantId: string, 
    roomId: string, 
    guestName: string = 'Guest'
  ): Promise<AuthResponse<{
    session: Session;
    customerId: string;
  }>> {
    try {
      // Create guest user in database
      const { data: guestData, error: guestError } = await this.supabase.rpc(
        'create_guest_user',
        {
          p_tenant_id: tenantId,
          p_room_id: roomId,
          p_name: guestName
        }
      );

      if (guestError) throw guestError;

      const { auth_user_id, customer_id, access_token } = guestData[0];

      // Create anonymous session with custom claims
      const { data: session, error: sessionError } = await this.supabase.auth.signInAnonymously({
        options: {
          data: {
            tenant_id: tenantId,
            user_type: 'guest',
            customer_id: customer_id,
            room_id: roomId
          }
        }
      });

      if (sessionError) throw sessionError;

      return {
        data: {
          session,
          customerId: customer_id
        }
      };
    } catch (error) {
      return { error: error.message };
    }
  }

  // =====================================================
  // STAFF AUTHENTICATION FLOW
  // =====================================================

  /**
   * Authenticate staff with email and PIN
   */
  async authenticateStaffPIN(
    tenantId: string,
    email: string,
    pin: string
  ): Promise<AuthResponse<{
    session: Session;
    staffId: string;
    staffRole: string;
  }>> {
    try {
      // Validate PIN
      const { data: authData, error: authError } = await this.supabase.rpc(
        'authenticate_staff_pin',
        {
          p_tenant_id: tenantId,
          p_email: email,
          p_pin: pin
        }
      );

      if (authError) throw authError;

      const authResult = authData[0];
      if (!authResult.success) {
        return { error: authResult.message };
      }

      // Sign in with email (you'd need to implement proper auth flow)
      // For demo, we'll use a service role to create session
      const { data: staffData, error: staffError } = await this.supabase
        .from('Staff')
        .select('*')
        .eq('id', authResult.staff_id)
        .single();

      if (staffError) throw staffError;

      // In production, use proper Supabase Auth flow
      // This is a simplified example
      const { data: session, error: sessionError } = await this.supabase.auth.signInWithPassword({
        email: email,
        password: pin, // In real app, use proper password
      });

      if (sessionError) {
        // If no account exists, create one
        const { data: newUser, error: signUpError } = await this.supabase.auth.signUp({
          email: email,
          password: pin,
          options: {
            data: {
              tenant_id: tenantId,
              user_type: 'staff',
              staff_id: authResult.staff_id,
              staff_role: staffData.role
            }
          }
        });

        if (signUpError) throw signUpError;
        
        return {
          data: {
            session: newUser.session,
            staffId: authResult.staff_id,
            staffRole: staffData.role
          }
        };
      }

      return {
        data: {
          session,
          staffId: authResult.staff_id,
          staffRole: staffData.role
        }
      };
    } catch (error) {
      return { error: error.message };
    }
  }

  /**
   * Quick staff re-authentication with PIN only
   */
  async quickStaffAuth(pin: string): Promise<AuthResponse<boolean>> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      if (!user) return { error: 'No active session' };

      const claims = await this.getCustomClaims();
      if (claims.role !== 'staff' || !claims.staff_id) {
        return { error: 'Not a staff member' };
      }

      // Verify PIN
      const { data, error } = await this.supabase
        .from('Staff')
        .select('pin')
        .eq('id', claims.staff_id)
        .single();

      if (error) throw error;
      
      return { data: data.pin === pin };
    } catch (error) {
      return { error: error.message };
    }
  }

  // =====================================================
  // CUSTOMER AUTHENTICATION FLOW
  // =====================================================

  /**
   * Register new customer
   */
  async registerCustomer(
    email: string,
    password: string,
    name: string,
    tenantId: string
  ): Promise<AuthResponse<Session>> {
    try {
      const { data, error } = await this.supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
            tenant_id: tenantId,
            user_type: 'customer'
          }
        }
      });

      if (error) throw error;
      return { data: data.session };
    } catch (error) {
      return { error: error.message };
    }
  }

  /**
   * Sign in customer
   */
  async signInCustomer(
    email: string,
    password: string
  ): Promise<AuthResponse<Session>> {
    try {
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;
      return { data: data.session };
    } catch (error) {
      return { error: error.message };
    }
  }

  /**
   * Promote guest to registered customer
   */
  async promoteGuestToCustomer(
    email: string,
    password: string
  ): Promise<AuthResponse<boolean>> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      if (!user) return { error: 'No active session' };

      // Call the promotion function
      const { data, error } = await this.supabase.rpc(
        'promote_guest_to_customer',
        {
          p_guest_id: user.id,
          p_email: email,
          p_password: password
        }
      );

      if (error) throw error;

      // Update the auth user
      const { error: updateError } = await this.supabase.auth.updateUser({
        email,
        password
      });

      if (updateError) throw updateError;

      return { data: true };
    } catch (error) {
      return { error: error.message };
    }
  }

  // =====================================================
  // COMMON AUTH UTILITIES
  // =====================================================

  /**
   * Get current session
   */
  async getSession(): Promise<Session | null> {
    const { data: { session } } = await this.supabase.auth.getSession();
    return session;
  }

  /**
   * Get custom claims from JWT
   */
  async getCustomClaims(): Promise<CustomClaims> {
    const { data: { user } } = await this.supabase.auth.getUser();
    
    if (!user) {
      return { user_id: '', role: 'anonymous' };
    }

    // Custom claims are stored in app_metadata
    const customClaims = user.app_metadata?.custom_claims || {};
    
    return customClaims as CustomClaims;
  }

  /**
   * Check if user has specific role
   */
  async hasRole(role: string): Promise<boolean> {
    const claims = await this.getCustomClaims();
    
    if (role === 'staff') {
      return claims.role === 'staff';
    }
    
    if (['ADMIN', 'MANAGER', 'CHEF', 'WAITER', 'RECEPTIONIST'].includes(role)) {
      return claims.role === 'staff' && claims.staff_role === role;
    }
    
    return claims.role === role;
  }

  /**
   * Switch tenant context (for multi-tenant staff)
   */
  async switchTenant(newTenantId: string): Promise<AuthResponse<boolean>> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      if (!user) return { error: 'No active session' };

      const { data, error } = await this.supabase.rpc(
        'switch_tenant_context',
        {
          p_user_id: user.id,
          p_new_tenant_id: newTenantId
        }
      );

      if (error) throw error;

      // Refresh session to get new claims
      const { error: refreshError } = await this.supabase.auth.refreshSession();
      if (refreshError) throw refreshError;

      return { data: data };
    } catch (error) {
      return { error: error.message };
    }
  }

  /**
   * Sign out
   */
  async signOut(): Promise<void> {
    await this.supabase.auth.signOut();
  }

  /**
   * Subscribe to auth state changes
   */
  onAuthStateChange(callback: (event: string, session: Session | null) => void) {
    return this.supabase.auth.onAuthStateChange((event, session) => {
      callback(event, session);
    });
  }
}

// Export singleton instance
export const bheemdineAuth = new BheemdineAuth(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);
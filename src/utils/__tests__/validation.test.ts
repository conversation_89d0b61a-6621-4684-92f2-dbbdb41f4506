import { describe, it, expect } from 'vitest'

// Simple validation utilities for testing
export const validateEmail = (email: string): boolean => {
  // More strict email validation
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
  return emailRegex.test(email) && !email.includes('..')
}

export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/
  return phoneRegex.test(phone)
}

export const validateRequired = (value: string | undefined | null): boolean => {
  return value !== undefined && value !== null && value.trim().length > 0
}

export const validateMinLength = (value: string, minLength: number): boolean => {
  return value.length >= minLength
}

export const validateMaxLength = (value: string, maxLength: number): boolean => {
  return value.length <= maxLength
}

export const validateNumericRange = (value: number, min: number, max: number): boolean => {
  return value >= min && value <= max
}

export const sanitizeInput = (input: string): string => {
  // Remove potentially dangerous characters
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=\s*[^>\s]+/gi, '')
    .trim()
}

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

export const formatPhoneNumber = (phone: string): string => {
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '')
  
  // Format as (XXX) XXX-XXXX for US numbers
  if (digits.length === 10) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`
  }
  
  // Return original if not a standard US number
  return phone
}

describe('Validation Utilities', () => {
  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true)
      expect(validateEmail('<EMAIL>')).toBe(true)
      expect(validateEmail('<EMAIL>')).toBe(true)
    })

    it('should reject invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false)
      expect(validateEmail('test@')).toBe(false)
      expect(validateEmail('@example.com')).toBe(false)
      expect(validateEmail('<EMAIL>')).toBe(false)
      expect(validateEmail('')).toBe(false)
    })
  })

  describe('validatePhone', () => {
    it('should validate correct phone numbers', () => {
      expect(validatePhone('+1234567890')).toBe(true)
      expect(validatePhone('(*************')).toBe(true)
      expect(validatePhone('************')).toBe(true)
      expect(validatePhone('1234567890')).toBe(true)
    })

    it('should reject invalid phone numbers', () => {
      expect(validatePhone('123')).toBe(false)
      expect(validatePhone('abc-def-ghij')).toBe(false)
      expect(validatePhone('')).toBe(false)
    })
  })

  describe('validateRequired', () => {
    it('should validate required fields', () => {
      expect(validateRequired('valid input')).toBe(true)
      expect(validateRequired('a')).toBe(true)
    })

    it('should reject empty or null values', () => {
      expect(validateRequired('')).toBe(false)
      expect(validateRequired('   ')).toBe(false)
      expect(validateRequired(null)).toBe(false)
      expect(validateRequired(undefined)).toBe(false)
    })
  })

  describe('validateMinLength', () => {
    it('should validate minimum length', () => {
      expect(validateMinLength('password123', 8)).toBe(true)
      expect(validateMinLength('exactly8', 8)).toBe(true)
    })

    it('should reject strings that are too short', () => {
      expect(validateMinLength('short', 8)).toBe(false)
      expect(validateMinLength('', 1)).toBe(false)
    })
  })

  describe('validateMaxLength', () => {
    it('should validate maximum length', () => {
      expect(validateMaxLength('short', 10)).toBe(true)
      expect(validateMaxLength('exactly10c', 10)).toBe(true)
    })

    it('should reject strings that are too long', () => {
      expect(validateMaxLength('this is way too long for the limit', 10)).toBe(false)
    })
  })

  describe('validateNumericRange', () => {
    it('should validate numbers within range', () => {
      expect(validateNumericRange(25, 18, 65)).toBe(true)
      expect(validateNumericRange(18, 18, 65)).toBe(true)
      expect(validateNumericRange(65, 18, 65)).toBe(true)
    })

    it('should reject numbers outside range', () => {
      expect(validateNumericRange(17, 18, 65)).toBe(false)
      expect(validateNumericRange(66, 18, 65)).toBe(false)
    })
  })

  describe('sanitizeInput', () => {
    it('should remove script tags', () => {
      const malicious = '<script>alert("xss")</script>Hello'
      expect(sanitizeInput(malicious)).toBe('Hello')
    })

    it('should remove javascript: protocols', () => {
      const malicious = 'javascript:alert("xss")'
      expect(sanitizeInput(malicious)).toBe('alert("xss")')
    })

    it('should remove event handlers', () => {
      const malicious = 'onclick=alert("xss") Hello'
      expect(sanitizeInput(malicious)).toBe('Hello')
    })

    it('should preserve safe content', () => {
      const safe = 'This is safe content with numbers 123'
      expect(sanitizeInput(safe)).toBe(safe)
    })
  })

  describe('formatCurrency', () => {
    it('should format currency correctly', () => {
      expect(formatCurrency(15.99)).toBe('$15.99')
      expect(formatCurrency(1000)).toBe('$1,000.00')
      expect(formatCurrency(0)).toBe('$0.00')
    })

    it('should handle decimal places', () => {
      expect(formatCurrency(15.5)).toBe('$15.50')
      expect(formatCurrency(15.999)).toBe('$16.00')
    })
  })

  describe('formatPhoneNumber', () => {
    it('should format US phone numbers', () => {
      expect(formatPhoneNumber('1234567890')).toBe('(*************')
      expect(formatPhoneNumber('************')).toBe('(*************')
      expect(formatPhoneNumber('(*************')).toBe('(*************')
    })

    it('should preserve non-US format numbers', () => {
      expect(formatPhoneNumber('+44123456789')).toBe('+44123456789')
      expect(formatPhoneNumber('123')).toBe('123')
    })
  })
})

// Types for menu administration and CRUD operations
export interface MenuItemAdmin {
  id: string;
  tenantId: string;
  name: string;
  description: string;
  category: string;
  basePrice: number;
  imageUrl?: string;
  isAvailable: boolean;
  isVegetarian: boolean;
  isVegan: boolean;
  isGlutenFree: boolean;
  preparationTime: number; // in minutes
  calories?: number;
  sortOrder: number;
  allergens: string[];
  tags: string[];
  ingredients: string[];
  createdAt: string;
  updatedAt: string;
}

export interface MenuCategoryAdmin {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  imageUrl?: string;
  isActive: boolean;
  sortOrder: number;
  itemCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface MenuItemFormData {
  name: string;
  description: string;
  category: string;
  basePrice: number;
  imageUrl?: string;
  isAvailable: boolean;
  isVegetarian: boolean;
  isVegan: boolean;
  isGlutenFree: boolean;
  preparationTime: number;
  calories?: number;
  allergens: string[];
  tags: string[];
  ingredients: string[];
}

export interface MenuCategoryFormData {
  name: string;
  description?: string;
  imageUrl?: string;
  isActive: boolean;
}

export interface MenuItemFormErrors {
  name?: string;
  description?: string;
  category?: string;
  basePrice?: string;
  preparationTime?: string;
  calories?: string;
  general?: string;
}

export interface MenuCategoryFormErrors {
  name?: string;
  description?: string;
  general?: string;
}

export interface MenuFilters {
  search?: string;
  category?: string;
  isAvailable?: boolean;
  isVegetarian?: boolean;
  isVegan?: boolean;
  isGlutenFree?: boolean;
  priceMin?: number;
  priceMax?: number;
  tags?: string[];
  allergens?: string[];
}

export interface MenuSortOption {
  field: keyof MenuItemAdmin;
  direction: 'asc' | 'desc';
  label: string;
}

export interface BulkAction {
  id: string;
  label: string;
  icon: React.ElementType;
  action: (selectedIds: string[]) => Promise<void>;
  confirmMessage?: string;
  requiresConfirmation?: boolean;
  destructive?: boolean;
}

export interface TableColumn<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (value: any, item: T) => React.ReactNode;
}

export interface GridViewSettings {
  itemsPerPage: number;
  showImages: boolean;
  cardSize: 'small' | 'medium' | 'large';
}

export interface TableViewSettings {
  itemsPerPage: number;
  visibleColumns: string[];
  density: 'compact' | 'comfortable' | 'spacious';
}

// API Response types
export interface MenuItemsResponse {
  items: MenuItemAdmin[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface MenuCategoriesResponse {
  categories: MenuCategoryAdmin[];
  total: number;
}

// Form validation schemas
export const MENU_ITEM_VALIDATION = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    pattern: /^[a-zA-Z0-9\s\-'.&]+$/,
  },
  description: {
    required: true,
    minLength: 10,
    maxLength: 500,
  },
  basePrice: {
    required: true,
    min: 0.01,
    max: 999.99,
  },
  preparationTime: {
    required: true,
    min: 1,
    max: 180,
  },
  calories: {
    required: false,
    min: 1,
    max: 5000,
  },
} as const;

export const MENU_CATEGORY_VALIDATION = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 50,
    pattern: /^[a-zA-Z0-9\s\-'&]+$/,
  },
  description: {
    required: false,
    maxLength: 200,
  },
} as const;

// Common constants
export const DIETARY_OPTIONS = [
  { id: 'isVegetarian', label: 'Vegetarian', icon: '🌱' },
  { id: 'isVegan', label: 'Vegan', icon: '🌿' },
  { id: 'isGlutenFree', label: 'Gluten Free', icon: '🌾' },
] as const;

export const COMMON_ALLERGENS = [
  'Milk', 'Eggs', 'Fish', 'Shellfish', 'Tree Nuts', 
  'Peanuts', 'Wheat', 'Soybeans', 'Sesame'
] as const;

export const COMMON_TAGS = [
  'Popular', 'New', 'Spicy', 'Chef Special', 'Healthy',
  'Comfort Food', 'Light', 'Hearty', 'Sweet', 'Savory'
] as const;

export const PRICE_RANGES = [
  { label: 'Under $10', min: 0, max: 10 },
  { label: '$10 - $20', min: 10, max: 20 },
  { label: '$20 - $30', min: 20, max: 30 },
  { label: 'Over $30', min: 30, max: 999 },
] as const;
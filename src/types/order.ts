// Order-related types for TapDine order submission system

export interface CustomerInfo {
  name: string;
  email?: string;
  phone?: string;
}

export interface DeliveryInfo {
  roomNumber: string;
  deliveryInstructions?: string;
  preferredDeliveryTime?: string;
  isContactless?: boolean;
}

export interface PaymentInfo {
  paymentMethod: 'CASH' | 'CARD' | 'ROOM_CHARGE' | 'DIGITAL_WALLET';
  paymentToken?: string;
  billingName?: string;
  tipAmount?: number;
}

export interface OrderItem {
  menuItemId: string;
  quantity: number;
  unitPrice: number;
  customizations?: {
    modifications?: string[];
    specialInstructions?: string;
    spiceLevel?: string;
  };
  notes?: string;
}

export interface OrderSubmissionRequest {
  tenantId: string;
  roomId?: string;
  customerInfo: CustomerInfo;
  items: OrderItem[];
  deliveryInfo?: DeliveryInfo;
  paymentInfo: PaymentInfo;
  orderNotes?: string;
  estimatedTotal: number;
  clientInfo?: {
    appVersion?: string;
    platform?: string;
    browser?: string;
  };
}

export interface OrderSubmissionResponse {
  orderId: string;
  orderNumber: string;
  status: 'PENDING' | 'CONFIRMED' | 'PREPARING' | 'READY' | 'DELIVERED' | 'CANCELLED';
  placedAt: string;
  estimatedPreparationTime: number;
  estimatedReadyTime: string;
  subtotal: number;
  taxAmount: number;
  tipAmount?: number;
  totalAmount: number;
  items: OrderItemResponse[];
  customerId?: string;
  confirmationMessage: string;
  trackingUrl: string;
  tenantName: string;
  roomNumber?: string;
}

export interface OrderItemResponse {
  id: string;
  menuItemId: string;
  menuItemName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  status: 'PENDING' | 'CONFIRMED' | 'PREPARING' | 'READY' | 'DELIVERED';
  customizations?: any;
  notes?: string;
}

export interface OrderValidationError {
  field: string;
  message: string;
  code?: string;
}

export interface OrderSubmissionError {
  errorCode: string;
  errorMessage: string;
  details?: Record<string, any>;
  fieldErrors?: Record<string, string[]>;
  timestamp?: string;
  requestId?: string;
}

export interface OrderFormData {
  customerInfo: CustomerInfo;
  deliveryInfo: DeliveryInfo;
  paymentInfo: PaymentInfo;
  orderNotes: string;
  agreeToTerms: boolean;
}

export interface OrderFormErrors {
  customerInfo?: Partial<Record<keyof CustomerInfo, string>>;
  deliveryInfo?: Partial<Record<keyof DeliveryInfo, string>>;
  paymentInfo?: Partial<Record<keyof PaymentInfo, string>>;
  orderNotes?: string;
  agreeToTerms?: string;
  general?: string;
}

export interface OrderFormState {
  data: OrderFormData;
  errors: OrderFormErrors;
  isValid: boolean;
  isSubmitting: boolean;
  submitAttempted: boolean;
}

export type OrderStep = 'review' | 'customer' | 'delivery' | 'payment' | 'confirmation';

export interface OrderProgress {
  currentStep: OrderStep;
  completedSteps: OrderStep[];
  totalSteps: number;
}
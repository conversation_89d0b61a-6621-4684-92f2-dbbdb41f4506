// Types and interfaces for QR code management system
// Extends the existing room management with QR code generation and export functionality

export interface Room {
  id: string;
  tenantId: string;
  roomNumber: string;
  floor?: string;
  qrCode: string; // Unique QR code identifier
  capacity: number;
  status: RoomStatus;
  description?: string;
  amenities?: string[];
  createdAt: string;
  updatedAt: string;
}

export type RoomStatus = 'AVAILABLE' | 'OCCUPIED' | 'RESERVED' | 'MAINTENANCE' | 'OUT_OF_ORDER';

// QR Code generation and customization options
export interface QRCodeOptions {
  // Content and URL
  baseUrl: string; // e.g., "https://tapdine.com/menu"
  qrCode: string; // The unique QR code identifier
  
  // Visual customization
  size: number; // Size in pixels (default: 256)
  foregroundColor: string; // QR code color (default: "#000000")
  backgroundColor: string; // Background color (default: "#FFFFFF")
  
  // Error correction level
  errorCorrectionLevel: 'L' | 'M' | 'Q' | 'H'; // Low, Medium, Quartile, High
  
  // Logo/branding
  includelogo: boolean;
  logoUrl?: string;
  logoSize?: number; // Percentage of QR code size (default: 20)
  
  // Border and margin
  margin: number; // Quiet zone around QR code (default: 4)
  border: boolean; // Add decorative border
  borderWidth?: number;
  borderColor?: string;
}

// QR Code display data with metadata
export interface QRCodeData {
  id: string;
  roomId: string;
  qrCode: string;
  roomNumber: string;
  floor?: string;
  capacity: number;
  url: string; // Generated URL for QR code
  
  // Generation metadata
  generatedAt: string;
  generatedBy: string;
  options: QRCodeOptions;
  
  // Export tracking
  lastExported?: string;
  exportCount: number;
  
  // Preview data
  dataUrl?: string; // Base64 encoded QR code image
  svgData?: string; // SVG representation
}

// Bulk operations interface
export interface BulkQROperation {
  type: 'generate' | 'regenerate' | 'export' | 'download';
  roomIds: string[];
  options?: QRCodeOptions;
  format?: 'png' | 'svg' | 'pdf';
  layout?: BulkLayoutOptions;
}

// Layout options for bulk exports
export interface BulkLayoutOptions {
  // Page settings
  pageSize: 'A4' | 'A3' | 'Letter' | 'Legal';
  orientation: 'portrait' | 'landscape';
  margin: number; // Margin in mm
  
  // Grid layout
  columns: number;
  rows: number;
  spacing: number; // Space between QR codes in mm
  
  // Content options
  includeRoomNumber: boolean;
  includeFloor: boolean;
  includeQRCodeText: boolean;
  includeInstructions: boolean;
  
  // Styling
  fontSize: number;
  fontFamily: string;
  titleFontSize: number;
  
  // Headers and footers
  title?: string;
  subtitle?: string;
  footer?: string;
  includeGenerationDate: boolean;
  includeTenantInfo: boolean;
}

// Form data interfaces
export interface RoomFormData {
  roomNumber: string;
  floor?: string;
  capacity: number;
  status: RoomStatus;
  description?: string;
  amenities: string[];
  regenerateQR?: boolean; // Option to generate new QR code
}

export interface QRCustomizationFormData {
  // Visual options
  size: number;
  foregroundColor: string;
  backgroundColor: string;
  errorCorrectionLevel: 'L' | 'M' | 'Q' | 'H';
  
  // Branding
  includelogo: boolean;
  logoSize: number;
  
  // Layout
  margin: number;
  border: boolean;
  borderWidth: number;
  borderColor: string;
}

// Export format options
export interface ExportOptions {
  format: 'png' | 'svg' | 'pdf';
  quality: number; // For image formats (1-100)
  size: number; // Export size in pixels
  
  // PDF specific options
  pdfOptions?: {
    layout: BulkLayoutOptions;
    includeMetadata: boolean;
    compression: boolean;
  };
}

// Generation result interface
export interface QRGenerationResult {
  success: boolean;
  qrCode: QRCodeData;
  dataUrl?: string;
  svgData?: string;
  error?: string;
  generationTime: number; // Time taken in milliseconds
}

// Bulk generation result
export interface BulkQRGenerationResult {
  success: boolean;
  results: QRGenerationResult[];
  failed: Array<{
    roomId: string;
    error: string;
  }>;
  totalProcessed: number;
  successCount: number;
  failureCount: number;
  processingTime: number;
}

// Export result interface
export interface ExportResult {
  success: boolean;
  format: string;
  size: number;
  downloadUrl?: string;
  blob?: Blob;
  filename: string;
  error?: string;
}

// Print template interface
export interface PrintTemplate {
  id: string;
  name: string;
  description: string;
  layout: BulkLayoutOptions;
  isDefault: boolean;
  createdBy: string;
  createdAt: string;
  preview?: string; // Base64 preview image
}

// QR code validation result
export interface QRValidationResult {
  isValid: boolean;
  qrCode?: string;
  roomId?: string;
  roomNumber?: string;
  tenantId?: string;
  tenantName?: string;
  error?: string;
}

// Statistics and analytics
export interface QRCodeStats {
  totalRooms: number;
  totalQRCodes: number;
  qrCodesGenerated: number;
  lastGenerated?: string;
  exportStats: {
    totalExports: number;
    exportsByFormat: Record<string, number>;
    lastExported?: string;
  };
  scanStats?: {
    totalScans: number;
    uniqueScans: number;
    scansByDay: Array<{
      date: string;
      scans: number;
    }>;
  };
}

// Room management filters
export interface RoomFilters {
  search?: string;
  floor?: string;
  status?: RoomStatus[];
  capacity?: {
    min?: number;
    max?: number;
  };
  amenities?: string[];
  hasQRCode?: boolean;
  recentlyGenerated?: boolean; // QR codes generated in last 7 days
}

// Sorting options
export interface RoomSortOptions {
  field: 'roomNumber' | 'floor' | 'capacity' | 'status' | 'createdAt' | 'updatedAt';
  direction: 'asc' | 'desc';
}

// API response interfaces
export interface RoomsResponse {
  rooms: Room[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface QRCodesResponse {
  qrCodes: QRCodeData[];
  total: number;
  stats: QRCodeStats;
}

// Notification types
export interface QRNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  timestamp: string;
  autoClose?: boolean;
  duration?: number; // Duration in milliseconds
}

// Component state interfaces
export interface QRManagementState {
  // Data
  rooms: Room[];
  qrCodes: Map<string, QRCodeData>;
  selectedRooms: Set<string>;
  
  // UI state
  isLoading: boolean;
  isGenerating: boolean;
  isExporting: boolean;
  error: string | null;
  
  // Filters and view
  filters: RoomFilters;
  sortOptions: RoomSortOptions;
  viewMode: 'grid' | 'list' | 'preview';
  
  // Selection and bulk operations
  bulkOperation: BulkQROperation | null;
  
  // Generation options
  defaultQROptions: QRCodeOptions;
  customizationOptions: QRCustomizationFormData;
  
  // Export state
  exportProgress: {
    current: number;
    total: number;
    isActive: boolean;
  };
  
  // Notifications
  notifications: QRNotification[];
}

// Action types for state management
export interface QRManagementActions {
  // Data operations
  loadRooms: (filters?: RoomFilters) => Promise<void>;
  createRoom: (data: RoomFormData) => Promise<Room>;
  updateRoom: (roomId: string, data: Partial<RoomFormData>) => Promise<Room>;
  deleteRoom: (roomId: string) => Promise<void>;
  
  // QR operations
  generateQRCode: (roomId: string, options?: QRCodeOptions) => Promise<QRGenerationResult>;
  regenerateQRCode: (roomId: string) => Promise<QRGenerationResult>;
  bulkGenerateQRCodes: (roomIds: string[], options?: QRCodeOptions) => Promise<BulkQRGenerationResult>;
  
  // Export operations
  exportQRCode: (qrCodeId: string, options: ExportOptions) => Promise<ExportResult>;
  bulkExportQRCodes: (qrCodeIds: string[], options: ExportOptions) => Promise<ExportResult>;
  
  // Selection management
  selectRoom: (roomId: string) => void;
  selectAllRooms: () => void;
  clearSelection: () => void;
  toggleRoomSelection: (roomId: string) => void;
  
  // UI state
  setFilters: (filters: Partial<RoomFilters>) => void;
  setSortOptions: (options: RoomSortOptions) => void;
  setViewMode: (mode: 'grid' | 'list' | 'preview') => void;
  
  // Notifications
  addNotification: (notification: Omit<QRNotification, 'id' | 'timestamp'>) => void;
  removeNotification: (notificationId: string) => void;
  clearNotifications: () => void;
}

// Constants and defaults
export const DEFAULT_QR_OPTIONS: QRCodeOptions = {
  baseUrl: 'https://app.tapdine.com/menu',
  qrCode: '',
  size: 256,
  foregroundColor: '#000000',
  backgroundColor: '#FFFFFF',
  errorCorrectionLevel: 'M',
  includelogo: true,
  logoSize: 20,
  margin: 4,
  border: false,
  borderWidth: 1,
  borderColor: '#000000',
};

export const DEFAULT_LAYOUT_OPTIONS: BulkLayoutOptions = {
  pageSize: 'A4',
  orientation: 'portrait',
  margin: 20,
  columns: 3,
  rows: 4,
  spacing: 10,
  includeRoomNumber: true,
  includeFloor: true,
  includeQRCodeText: true,
  includeInstructions: true,
  fontSize: 12,
  fontFamily: 'Arial, sans-serif',
  titleFontSize: 16,
  includeGenerationDate: true,
  includeTenantInfo: true,
};

export const ROOM_STATUSES: Array<{ value: RoomStatus; label: string; color: string }> = [
  { value: 'AVAILABLE', label: 'Available', color: 'bg-green-100 text-green-800' },
  { value: 'OCCUPIED', label: 'Occupied', color: 'bg-blue-100 text-blue-800' },
  { value: 'RESERVED', label: 'Reserved', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'MAINTENANCE', label: 'Maintenance', color: 'bg-orange-100 text-orange-800' },
  { value: 'OUT_OF_ORDER', label: 'Out of Order', color: 'bg-red-100 text-red-800' },
];

export const COMMON_AMENITIES = [
  'WiFi',
  'TV',
  'Air Conditioning',
  'Balcony',
  'Mini Bar',
  'Room Service',
  'Ocean View',
  'City View',
  'Bathtub',
  'Shower',
  'Safe',
  'Refrigerator',
] as const;

// Error types
export interface QRCodeError {
  code: string;
  message: string;
  field?: string;
  details?: any;
}

// Event types for analytics
export type QRCodeEvent = 
  | 'qr_generated'
  | 'qr_regenerated'
  | 'qr_exported'
  | 'qr_printed'
  | 'room_created'
  | 'room_updated'
  | 'room_deleted'
  | 'bulk_operation';
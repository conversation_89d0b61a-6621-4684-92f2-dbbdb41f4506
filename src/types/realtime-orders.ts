// Enhanced types for real-time order management with Supabase Realtime
// This extends the existing order types with real-time specific functionality

import type { OrderStatus, OrderItem } from './order';

// Real-time order event types based on OrderEvent table structure
export interface OrderEvent {
  id: string;
  order_id: string;
  tenant_id: string;
  event_type: OrderEventType;
  old_status?: OrderStatus;
  new_status?: OrderStatus;
  changed_by: string; // staff member ID
  changed_by_role: StaffRole;
  notes?: string;
  metadata?: Record<string, any>;
  created_at: string;
}

export type OrderEventType = 
  | 'ORDER_CREATED'
  | 'STATUS_CHANGED' 
  | 'ITEM_UPDATED'
  | 'PAYMENT_PROCESSED'
  | 'ASSIGNED_TO_STAFF'
  | 'NOTES_ADDED'
  | 'CUSTOMER_NOTIFIED';

export type StaffRole = 'ADMIN' | 'MANAGER' | 'CHEF' | 'WAITER' | 'RECEPTIONIST';

// Enhanced order interface for real-time dashboard
export interface RealtimeOrder {
  // Core order data (from existing Order table)
  id: string;
  tenant_id: string;
  customer_id?: string;
  room_id?: string;
  staff_assigned_id?: string;
  
  // Order details
  status: OrderStatus;
  order_number: string; // Human-readable order number like "ORD-001"
  total_amount: number;
  tax_amount: number;
  delivery_fee?: number;
  
  // Customer information
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  room_number?: string;
  
  // Timing information
  created_at: string;
  estimated_completion_time?: string;
  actual_completion_time?: string;
  
  // Order items with real-time status
  items: RealtimeOrderItem[];
  
  // Real-time specific fields
  is_urgent?: boolean; // Orders that are taking too long
  staff_assigned?: {
    id: string;
    name: string;
    role: StaffRole;
  };
  
  // Latest activity
  latest_event?: OrderEvent;
  
  // Computed fields for dashboard display
  time_since_created: number; // milliseconds
  estimated_time_remaining?: number; // milliseconds
  is_overdue?: boolean;
}

export interface RealtimeOrderItem extends OrderItem {
  // Individual item status for kitchen workflow
  item_status: OrderItemStatus;
  assigned_chef_id?: string;
  started_preparation_at?: string;
  completed_preparation_at?: string;
  estimated_prep_time?: number; // minutes
}

export type OrderItemStatus = 
  | 'PENDING'     // Not started
  | 'PREPARING'   // Being cooked
  | 'READY'       // Finished cooking
  | 'SERVED';     // Delivered to customer

// Supabase real-time subscription payload types
export interface OrderRealtimePayload {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new?: RealtimeOrder;
  old?: RealtimeOrder;
  errors?: string[];
}

export interface OrderEventRealtimePayload {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new?: OrderEvent;
  old?: OrderEvent;
  errors?: string[];
}

// Dashboard filter and grouping options
export interface OrderDashboardFilters {
  statuses: OrderStatus[];
  staff_assigned?: string[];
  room_numbers?: string[];
  order_types?: ('DINE_IN' | 'ROOM_SERVICE' | 'TAKEOUT')[];
  date_range?: {
    start: string;
    end: string;
  };
  is_urgent_only?: boolean;
  search_query?: string; // Search order number, customer name, etc.
}

// Dashboard grouping and sorting
export type OrderGroupBy = 'status' | 'staff' | 'room' | 'time' | 'none';
export type OrderSortBy = 'created_at' | 'estimated_completion' | 'total_amount' | 'urgency';
export type SortDirection = 'asc' | 'desc';

// Real-time dashboard state
export interface OrderDashboardState {
  // Orders data
  orders: Map<string, RealtimeOrder>; // Using Map for O(1) lookups
  ordersById: Record<string, RealtimeOrder>; // Backup lookup
  ordersByStatus: Record<OrderStatus, string[]>; // Order IDs grouped by status
  
  // Real-time connection status
  isConnected: boolean;
  connectionError?: string;
  lastUpdated?: string;
  
  // UI state
  filters: OrderDashboardFilters;
  groupBy: OrderGroupBy;
  sortBy: OrderSortBy;
  sortDirection: SortDirection;
  selectedOrderId?: string;
  
  // Performance tracking
  updateCount: number; // Number of real-time updates received
  subscriptionChannels: string[]; // Active subscription channels
}

// Sound notification types
export type NotificationSound = 
  | 'NEW_ORDER'      // New order received
  | 'STATUS_CHANGE'  // Status updated
  | 'URGENT_ORDER'   // Order is taking too long
  | 'READY_FOR_PICKUP' // Order ready for delivery
  | 'ERROR';         // System error

export interface NotificationSettings {
  enableSounds: boolean;
  enableBrowserNotifications: boolean;
  enableUrgentAlerts: boolean;
  soundVolume: number; // 0-1
  urgentThresholdMinutes: number; // Minutes before order is considered urgent
  
  // Role-specific notification preferences
  roleSettings: Record<StaffRole, {
    notifyOnStatuses: OrderStatus[];
    notifyOnEvents: OrderEventType[];
  }>;
}

// Real-time subscription configuration
export interface RealtimeSubscriptionConfig {
  tenant_id: string;
  staff_role: StaffRole;
  staff_id: string;
  
  // Subscription filters based on role
  // Chefs only see PENDING -> PREPARING orders
  // Waiters see READY -> DELIVERED orders
  // Managers see everything
  statusFilters?: OrderStatus[];
  
  // Table subscriptions
  subscribeToOrders: boolean;
  subscribeToOrderEvents: boolean;
  subscribeToOrderItems: boolean;
  
  // Performance optimization
  enablePresence?: boolean; // Track who's online
  heartbeatInterval?: number; // Connection health check interval
}

// WebSocket message types for enhanced real-time features
export interface RealtimeMessage {
  type: RealtimeMessageType;
  payload: any;
  timestamp: string;
  sender_id?: string;
}

export type RealtimeMessageType =
  | 'ORDER_UPDATE'
  | 'BULK_STATUS_UPDATE'
  | 'STAFF_TYPING'      // Staff is updating an order
  | 'STAFF_PRESENCE'    // Staff online/offline
  | 'SYSTEM_ALERT'      // System-wide notifications
  | 'PERFORMANCE_METRICS'; // Dashboard performance data

// Performance monitoring for real-time dashboard
export interface DashboardMetrics {
  ordersPerMinute: number;
  averageOrderTime: number; // minutes
  statusDistribution: Record<OrderStatus, number>;
  staffWorkload: Record<string, number>; // staff_id -> number of active orders
  connectionLatency: number; // milliseconds
  updateFrequency: number; // updates per second
}

// Error types for better error handling
export interface RealtimeError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
  recoverable: boolean;
}

// Kitchen display specific types
export interface KitchenDisplayItem {
  order_id: string;
  order_number: string;
  item_name: string;
  quantity: number;
  modifications?: string[];
  special_instructions?: string;
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
  time_since_ordered: number; // minutes
  estimated_completion: number; // minutes from now
}

// Customer notification preferences
export interface CustomerNotificationPreferences {
  sms_enabled: boolean;
  email_enabled: boolean;
  whatsapp_enabled: boolean;
  push_notifications_enabled: boolean;
  
  // Notification triggers
  notify_on_confirmation: boolean;
  notify_on_preparation_start: boolean;
  notify_on_ready: boolean;
  notify_on_delivery: boolean;
  notify_on_delays: boolean;
}

// Export utility type helpers
export type OrderStatusFlow = {
  [K in OrderStatus]: OrderStatus[];
}; // Valid status transitions

export const ORDER_STATUS_FLOW: OrderStatusFlow = {
  PENDING: ['CONFIRMED', 'CANCELLED'],
  CONFIRMED: ['PREPARING', 'CANCELLED'],
  PREPARING: ['READY', 'CANCELLED'],
  READY: ['DELIVERED'],
  DELIVERED: [], // Terminal state
  CANCELLED: [], // Terminal state
};

// Role-based order visibility rules
export const ROLE_ORDER_ACCESS: Record<StaffRole, {
  canView: OrderStatus[];
  canUpdate: OrderStatus[];
  canCancel: OrderStatus[];
}> = {
  ADMIN: {
    canView: ['PENDING', 'CONFIRMED', 'PREPARING', 'READY', 'DELIVERED', 'CANCELLED'],
    canUpdate: ['PENDING', 'CONFIRMED', 'PREPARING', 'READY'],
    canCancel: ['PENDING', 'CONFIRMED', 'PREPARING'],
  },
  MANAGER: {
    canView: ['PENDING', 'CONFIRMED', 'PREPARING', 'READY', 'DELIVERED', 'CANCELLED'],
    canUpdate: ['PENDING', 'CONFIRMED', 'PREPARING', 'READY'],
    canCancel: ['PENDING', 'CONFIRMED'],
  },
  CHEF: {
    canView: ['CONFIRMED', 'PREPARING', 'READY'],
    canUpdate: ['CONFIRMED', 'PREPARING', 'READY'],
    canCancel: [],
  },
  WAITER: {
    canView: ['READY', 'DELIVERED'],
    canUpdate: ['READY', 'DELIVERED'],
    canCancel: [],
  },
  RECEPTIONIST: {
    canView: ['PENDING', 'CONFIRMED', 'DELIVERED'],
    canUpdate: ['PENDING'],
    canCancel: ['PENDING'],
  },
};
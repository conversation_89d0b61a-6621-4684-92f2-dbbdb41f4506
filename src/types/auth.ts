// Enhanced authentication types for TapDine tenant admin dashboard
import type { Session, User } from '@supabase/supabase-js';

export type UserRole = 'guest' | 'customer' | 'staff' | 'anonymous';

export type StaffRole = 'ADMIN' | 'MANAGER' | 'CHEF' | 'WAITER' | 'RECEPTIONIST';

export type AdminRole = 'SUPER_ADMIN' | 'TENANT_ADMIN' | 'MANAGER';

export interface TenantInfo {
  id: string;
  name: string;
  slug: string;
  logoUrl?: string;
  settings?: Record<string, any>;
  isActive: boolean;
}

export interface CustomClaims {
  user_id: string;
  tenant_id?: string;
  tenant_slug?: string;
  tenant_name?: string;
  user_type?: UserRole;
  role?: UserRole;
  staff_role?: StaffRole;
  admin_role?: AdminRole;
  staff_id?: string;
  customer_id?: string;
  room_id?: string;
  permissions?: string[];
  tenants?: string[]; // For multi-tenant access
}

export interface AuthUser {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  phone?: string;
  last_sign_in_at?: string;
  email_confirmed_at?: string;
  created_at: string;
  updated_at: string;
  claims: CustomClaims;
  raw_user_meta_data?: Record<string, any>;
}

export interface AuthState {
  user: AuthUser | null;
  session: Session | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isTenantAdmin: boolean;
  currentTenant: TenantInfo | null;
  permissions: string[];
  adminRole: AdminRole | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  tenantSlug?: string;
  rememberMe?: boolean;
}

export interface AuthContextType {
  // State
  authState: AuthState;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<AuthResponse<AuthUser>>;
  logout: () => Promise<void>;
  refreshSession: () => Promise<void>;
  updateProfile: (updates: Partial<AuthUser>) => Promise<AuthResponse<AuthUser>>;
  
  // Role & Permission checks
  hasRole: (role: UserRole | StaffRole | AdminRole) => boolean;
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  
  // Tenant management
  switchTenant: (tenantId: string) => Promise<AuthResponse<boolean>>;
  getAccessibleTenants: () => Promise<TenantInfo[]>;
  
  // Security
  requireReauth: () => Promise<boolean>;
  verifyPassword: (password: string) => Promise<boolean>;
  
  // Session management
  extendSession: () => Promise<void>;
  checkSessionExpiry: () => Promise<boolean>;
}

export interface AuthResponse<T = any> {
  data?: T;
  error?: string;
}

export interface AuthError {
  message: string;
  code: string;
  details?: Record<string, any>;
}

export interface LoginFormData {
  email: string;
  password: string;
  tenantSlug: string;
  rememberMe: boolean;
}

export interface LoginFormErrors {
  email?: string;
  password?: string;
  tenantSlug?: string;
  general?: string;
}

export interface PasswordResetData {
  email: string;
  tenantSlug?: string;
}

export interface PasswordUpdateData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ProfileUpdateData {
  name?: string;
  email?: string;
  phone?: string;
  avatar_url?: string;
}

// Permission definitions for tenant admin
export const ADMIN_PERMISSIONS = {
  // Dashboard access
  DASHBOARD_VIEW: 'dashboard:view',
  DASHBOARD_ANALYTICS: 'dashboard:analytics',
  
  // Menu management
  MENU_VIEW: 'menu:view',
  MENU_CREATE: 'menu:create',
  MENU_EDIT: 'menu:edit',
  MENU_DELETE: 'menu:delete',
  MENU_PRICING: 'menu:pricing',
  
  // Order management
  ORDERS_VIEW: 'orders:view',
  ORDERS_EDIT: 'orders:edit',
  ORDERS_CANCEL: 'orders:cancel',
  ORDERS_REFUND: 'orders:refund',
  
  // Staff management
  STAFF_VIEW: 'staff:view',
  STAFF_CREATE: 'staff:create',
  STAFF_EDIT: 'staff:edit',
  STAFF_DELETE: 'staff:delete',
  STAFF_PERMISSIONS: 'staff:permissions',
  
  // Customer management
  CUSTOMERS_VIEW: 'customers:view',
  CUSTOMERS_EDIT: 'customers:edit',
  CUSTOMERS_DELETE: 'customers:delete',
  
  // Tenant settings
  TENANT_SETTINGS: 'tenant:settings',
  TENANT_BILLING: 'tenant:billing',
  TENANT_INTEGRATIONS: 'tenant:integrations',
  
  // Reports and analytics
  REPORTS_VIEW: 'reports:view',
  REPORTS_EXPORT: 'reports:export',
  ANALYTICS_VIEW: 'analytics:view',
  
  // System administration (super admin only)
  SYSTEM_ADMIN: 'system:admin',
  TENANTS_MANAGE: 'tenants:manage',
  USERS_IMPERSONATE: 'users:impersonate',
} as const;

export type AdminPermission = typeof ADMIN_PERMISSIONS[keyof typeof ADMIN_PERMISSIONS];

// Role-based permission mapping
export const ROLE_PERMISSIONS: Record<AdminRole, AdminPermission[]> = {
  SUPER_ADMIN: Object.values(ADMIN_PERMISSIONS),
  TENANT_ADMIN: [
    ADMIN_PERMISSIONS.DASHBOARD_VIEW,
    ADMIN_PERMISSIONS.DASHBOARD_ANALYTICS,
    ADMIN_PERMISSIONS.MENU_VIEW,
    ADMIN_PERMISSIONS.MENU_CREATE,
    ADMIN_PERMISSIONS.MENU_EDIT,
    ADMIN_PERMISSIONS.MENU_DELETE,
    ADMIN_PERMISSIONS.MENU_PRICING,
    ADMIN_PERMISSIONS.ORDERS_VIEW,
    ADMIN_PERMISSIONS.ORDERS_EDIT,
    ADMIN_PERMISSIONS.ORDERS_CANCEL,
    ADMIN_PERMISSIONS.ORDERS_REFUND,
    ADMIN_PERMISSIONS.STAFF_VIEW,
    ADMIN_PERMISSIONS.STAFF_CREATE,
    ADMIN_PERMISSIONS.STAFF_EDIT,
    ADMIN_PERMISSIONS.STAFF_DELETE,
    ADMIN_PERMISSIONS.CUSTOMERS_VIEW,
    ADMIN_PERMISSIONS.CUSTOMERS_EDIT,
    ADMIN_PERMISSIONS.TENANT_SETTINGS,
    ADMIN_PERMISSIONS.TENANT_BILLING,
    ADMIN_PERMISSIONS.TENANT_INTEGRATIONS,
    ADMIN_PERMISSIONS.REPORTS_VIEW,
    ADMIN_PERMISSIONS.REPORTS_EXPORT,
    ADMIN_PERMISSIONS.ANALYTICS_VIEW,
  ],
  MANAGER: [
    ADMIN_PERMISSIONS.DASHBOARD_VIEW,
    ADMIN_PERMISSIONS.MENU_VIEW,
    ADMIN_PERMISSIONS.MENU_EDIT,
    ADMIN_PERMISSIONS.ORDERS_VIEW,
    ADMIN_PERMISSIONS.ORDERS_EDIT,
    ADMIN_PERMISSIONS.STAFF_VIEW,
    ADMIN_PERMISSIONS.CUSTOMERS_VIEW,
    ADMIN_PERMISSIONS.REPORTS_VIEW,
    ADMIN_PERMISSIONS.ANALYTICS_VIEW,
  ],
};

// Security settings
export interface SecuritySettings {
  requireTwoFactor: boolean;
  sessionTimeout: number; // in minutes
  passwordMinLength: number;
  passwordRequireSpecialChars: boolean;
  maxLoginAttempts: number;
  lockoutDuration: number; // in minutes
  requireReauthForSensitiveActions: boolean;
  allowMultipleSessions: boolean;
}

// Audit log entry
export interface AuditLogEntry {
  id: string;
  user_id: string;
  tenant_id: string;
  action: string;
  resource: string;
  resource_id?: string;
  ip_address: string;
  user_agent: string;
  metadata?: Record<string, any>;
  created_at: string;
}

// Login attempt tracking
export interface LoginAttempt {
  id: string;
  email: string;
  ip_address: string;
  success: boolean;
  error_message?: string;
  tenant_slug?: string;
  created_at: string;
}

// Session information
export interface SessionInfo {
  id: string;
  user_id: string;
  ip_address: string;
  user_agent: string;
  created_at: string;
  last_activity: string;
  expires_at: string;
  is_active: boolean;
}

// Two-factor authentication
export interface TwoFactorAuth {
  enabled: boolean;
  method: 'sms' | 'email' | 'authenticator';
  backup_codes?: string[];
  last_used?: string;
}

export interface TwoFactorSetup {
  secret: string;
  qr_code: string;
  backup_codes: string[];
}

export interface TwoFactorVerification {
  code: string;
  method: 'sms' | 'email' | 'authenticator' | 'backup_code';
}
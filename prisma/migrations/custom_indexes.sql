-- Custom PostgreSQL indexes for BHEEMDINE
-- Run this after Prisma migrations

-- Partial unique indexes for nullable fields
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_email_per_tenant 
ON "User"("tenantId", email) 
WHERE email != 'guest@local';

CREATE UNIQUE INDEX IF NOT EXISTS idx_user_phone_per_tenant 
ON "User"("tenantId", phone) 
WHERE phone != '0000000000';

CREATE UNIQUE INDEX IF NOT EXISTS idx_staff_pin_per_tenant 
ON "Staff"("tenantId", pin) 
WHERE pin != '0000';

-- Partial indexes for hot query paths
CREATE INDEX IF NOT EXISTS idx_order_open_per_tenant 
ON "Order"("tenantId", status) 
WHERE status IN ('PENDING', 'CONFIRMED', 'PREPARING', 'READY');

CREATE INDEX IF NOT EXISTS idx_order_active_per_tenant 
ON "Order"("tenantId", status, "placedAt" DESC) 
WHERE status NOT IN ('COMPLETED', 'CANCELLED');

CREATE INDEX IF NOT EXISTS idx_room_available_per_tenant 
ON "Room"("tenantId", status) 
WHERE status = 'AVAILABLE';

CREATE INDEX IF NOT EXISTS idx_menu_available_per_tenant 
ON "MenuItem"("tenantId", category, "sortOrder") 
WHERE "isAvailable" = true;

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_order_kitchen_view 
ON "Order"("tenantId", status, "placedAt" DESC) 
WHERE status IN ('CONFIRMED', 'PREPARING');

CREATE INDEX IF NOT EXISTS idx_order_delivery_view 
ON "Order"("tenantId", "roomId", status) 
WHERE status = 'READY';

-- Indexes for allergen searches
CREATE INDEX IF NOT EXISTS idx_allergen_search 
ON "MenuItemAllergen"("allergenId", "menuItemId");

-- Index for soft-deleted tenants
CREATE INDEX IF NOT EXISTS idx_tenant_active 
ON "Tenant"("isActive", "isDeleted") 
WHERE "isDeleted" = false;

-- Function to calculate order total (instead of storing it)
CREATE OR REPLACE FUNCTION calculate_order_total(order_id UUID)
RETURNS DECIMAL(10,2) AS $$
BEGIN
    RETURN COALESCE(
        (SELECT SUM(quantity * "unitPrice") 
         FROM "OrderItem" 
         WHERE "orderId" = order_id 
         AND status != 'CANCELLED'),
        0
    );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- View for order totals
CREATE OR REPLACE VIEW order_totals AS
SELECT 
    o.id,
    o."tenantId",
    o."orderNumber",
    calculate_order_total(o.id) as calculated_total,
    o."totalAmount" as stored_total
FROM "Order" o;

-- Trigger to prevent direct tenant deletion
CREATE OR REPLACE FUNCTION prevent_tenant_hard_delete()
RETURNS TRIGGER AS $$
BEGIN
    RAISE EXCEPTION 'Direct deletion of tenants is not allowed. Use soft delete by setting isDeleted = true';
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_prevent_tenant_delete
BEFORE DELETE ON "Tenant"
FOR EACH ROW
EXECUTE FUNCTION prevent_tenant_hard_delete();
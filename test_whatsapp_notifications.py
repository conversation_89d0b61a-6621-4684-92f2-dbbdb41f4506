#!/usr/bin/env python3
"""
Test script for WhatsApp notifications in BHEEMDINE/TapDine
Run this script to test the WhatsApp notification functionality
"""

import asyncio
import os
import sys
from datetime import datetime
from uuid import uuid4

# Add the src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from api.services.whatsapp_notification import WhatsAppNotificationService, WhatsAppNotificationError

def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_result(success, message, details=None):
    """Print formatted result"""
    status = "✅ SUCCESS" if success else "❌ FAILED"
    print(f"{status}: {message}")
    if details:
        print(f"Details: {details}")
    print()

async def test_service_initialization():
    """Test WhatsApp service initialization"""
    print_header("Testing Service Initialization")
    
    try:
        service = WhatsAppNotificationService()
        print_result(True, "WhatsApp service initialized successfully")
        return service
    except WhatsAppNotificationError as e:
        print_result(False, f"Service initialization failed: {e.message}", e.details)
        print("\n📋 Setup Instructions:")
        print("1. Set up Twilio WhatsApp Business API account")
        print("2. Set environment variables:")
        print("   export TWILIO_ACCOUNT_SID='your_account_sid'")
        print("   export TWILIO_AUTH_TOKEN='your_auth_token'")
        print("   export TWILIO_WHATSAPP_FROM='whatsapp:+***********'")
        return None
    except Exception as e:
        print_result(False, f"Unexpected error: {str(e)}")
        return None

async def test_service_health(service):
    """Test service health check"""
    print_header("Testing Service Health")
    
    try:
        health_result = await service.test_connection()
        success = health_result.get("status") == "success"
        message = "Service health check passed" if success else f"Health check failed: {health_result.get('error')}"
        print_result(success, message, health_result)
        return success
    except Exception as e:
        print_result(False, f"Health check failed: {str(e)}")
        return False

async def test_phone_validation(service):
    """Test phone number validation"""
    print_header("Testing Phone Number Validation")
    
    test_numbers = [
        "+**********",  # Valid US number
        "**********",   # US number without +
        "+44123456789", # Valid UK number
        "invalid",      # Invalid number
        "******-567-890" # US number with dashes
    ]
    
    for phone in test_numbers:
        try:
            from api.services.whatsapp_notification import validate_phone_number, format_phone_number
            is_valid = validate_phone_number(phone)
            formatted = format_phone_number(phone) if is_valid else "N/A"
            print(f"📞 {phone:15} -> Valid: {is_valid:5} -> Formatted: {formatted}")
        except Exception as e:
            print(f"📞 {phone:15} -> Error: {str(e)}")
    
    print()

async def test_new_order_notification(service, test_phone):
    """Test new order notification"""
    print_header("Testing New Order Notification")
    
    if not test_phone:
        print_result(False, "No test phone number provided")
        return False
    
    # Sample order data
    order_data = {
        "order_id": str(uuid4()),
        "order_number": f"TEST-{datetime.now().strftime('%H%M%S')}",
        "customer_name": "Test Customer",
        "room_number": "101A",
        "total_amount": 45.99,
        "item_count": 3,
        "order_notes": "Test order - please ignore",
        "tenant_name": "Test Restaurant",
        "items": [
            {
                "menu_item_name": "Grilled Salmon",
                "quantity": 2,
                "unit_price": 18.99,
                "notes": "Medium rare",
                "customizations": {"modifications": ["No onions"]}
            },
            {
                "menu_item_name": "Caesar Salad",
                "quantity": 1,
                "unit_price": 8.99
            }
        ]
    }
    
    try:
        result = await service.send_new_order_notification(
            order_data=order_data,
            staff_phones=[test_phone],
            tenant_info={"name": "Test Restaurant"}
        )
        
        success = result.get("successful_sends", 0) > 0
        message = f"Order notification sent to {result.get('successful_sends')}/{result.get('total_recipients')} recipients"
        print_result(success, message, result)
        return success
        
    except WhatsAppNotificationError as e:
        print_result(False, f"Order notification failed: {e.message}", e.details)
        return False
    except Exception as e:
        print_result(False, f"Unexpected error: {str(e)}")
        return False

async def test_status_update(service, test_phone):
    """Test order status update notification"""
    print_header("Testing Status Update Notification")
    
    if not test_phone:
        print_result(False, "No test phone number provided")
        return False
    
    # Sample status update data
    order_data = {
        "order_id": str(uuid4()),
        "order_number": f"TEST-{datetime.now().strftime('%H%M%S')}",
        "tenant_name": "Test Restaurant"
    }
    
    try:
        result = await service.send_order_status_update(
            order_data=order_data,
            customer_phone=test_phone,
            new_status="PREPARING",
            estimated_time="25 minutes"
        )
        
        success = result.get("message_sid") is not None
        message = f"Status update sent successfully" if success else "Status update failed"
        print_result(success, message, result)
        return success
        
    except WhatsAppNotificationError as e:
        print_result(False, f"Status update failed: {e.message}", e.details)
        return False
    except Exception as e:
        print_result(False, f"Unexpected error: {str(e)}")
        return False

async def test_kitchen_notification(service, test_phone):
    """Test kitchen notification"""
    print_header("Testing Kitchen Notification")
    
    if not test_phone:
        print_result(False, "No test phone number provided")
        return False
    
    # Sample kitchen order data
    order_data = {
        "order_id": str(uuid4()),
        "order_number": f"KITCHEN-{datetime.now().strftime('%H%M%S')}",
        "room_number": "205B",
        "order_notes": "Test kitchen order",
        "delivery_instructions": "Knock softly",
        "items": [
            {
                "menu_item_name": "Beef Wellington",
                "quantity": 1,
                "notes": "Well done",
                "customizations": {
                    "modifications": ["Extra sauce", "No mushrooms"],
                    "special_instructions": "Priority order"
                }
            },
            {
                "menu_item_name": "Chocolate Soufflé",
                "quantity": 2,
                "notes": "Serve warm"
            }
        ]
    }
    
    try:
        result = await service.send_kitchen_notification(
            order_data=order_data,
            kitchen_phones=[test_phone],
            priority="urgent"
        )
        
        success = result.get("successful_sends", 0) > 0
        message = f"Kitchen notification sent to {result.get('successful_sends')}/{result.get('total_recipients')} recipients"
        print_result(success, message, result)
        return success
        
    except WhatsAppNotificationError as e:
        print_result(False, f"Kitchen notification failed: {e.message}", e.details)
        return False
    except Exception as e:
        print_result(False, f"Unexpected error: {str(e)}")
        return False

async def main():
    """Main test function"""
    print("🚀 BHEEMDINE WhatsApp Notification System Test")
    print("=" * 60)
    
    # Get test phone number from user
    test_phone = os.getenv('TEST_PHONE_NUMBER')
    if not test_phone:
        print("📱 Please enter your WhatsApp phone number for testing (include country code):")
        print("Example: +**********")
        test_phone = input("Phone number: ").strip()
        
        if not test_phone:
            print("❌ No phone number provided. Some tests will be skipped.")
            test_phone = None
        else:
            from api.services.whatsapp_notification import validate_phone_number, format_phone_number
            if validate_phone_number(test_phone):
                test_phone = format_phone_number(test_phone)
                print(f"✅ Using phone number: {test_phone}")
            else:
                print(f"❌ Invalid phone number format: {test_phone}")
                test_phone = None
    
    # Track test results
    results = []
    
    # Test 1: Service initialization
    service = await test_service_initialization()
    results.append(("Service Initialization", service is not None))
    
    if not service:
        print_header("Test Summary")
        print("❌ Cannot continue tests without working service")
        return
    
    # Test 2: Service health
    health_ok = await test_service_health(service)
    results.append(("Service Health", health_ok))
    
    # Test 3: Phone validation
    await test_phone_validation(service)
    results.append(("Phone Validation", True))  # Always passes if we get here
    
    if not health_ok:
        print_header("Test Summary")
        print("❌ Service health check failed - skipping message tests")
        print("Check your Twilio configuration and try again")
        return
    
    # Test 4: New order notification
    if test_phone:
        order_ok = await test_new_order_notification(service, test_phone)
        results.append(("New Order Notification", order_ok))
        
        # Wait between tests to avoid rate limiting
        await asyncio.sleep(2)
        
        # Test 5: Status update
        status_ok = await test_status_update(service, test_phone)
        results.append(("Status Update Notification", status_ok))
        
        await asyncio.sleep(2)
        
        # Test 6: Kitchen notification
        kitchen_ok = await test_kitchen_notification(service, test_phone)
        results.append(("Kitchen Notification", kitchen_ok))
    else:
        print("⚠️  Skipping message tests - no valid phone number provided")
    
    # Print test summary
    print_header("Test Summary")
    total_tests = len(results)
    passed_tests = sum(1 for _, passed in results if passed)
    
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResult: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! WhatsApp notification system is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the error messages above for troubleshooting.")
    
    print("\n📋 Next Steps:")
    print("1. Configure staff phone numbers in environment variables or database")
    print("2. Test with real orders in your application")
    print("3. Monitor delivery rates and adjust settings as needed")
    print("4. Set up proper error monitoring and alerting")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n❌ Test interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Test script failed: {str(e)}")
        import traceback
        traceback.print_exc()
["tests/test_order_service.py::TestOrderService::test_check_suspicious_patterns_high_quantity", "tests/test_order_service.py::TestOrderService::test_check_suspicious_patterns_sql_injection", "tests/test_order_service.py::TestOrderService::test_order_service_configuration", "tests/test_order_service.py::TestOrderService::test_process_payment_card_failure", "tests/test_order_service.py::TestOrderService::test_process_payment_card_success", "tests/test_order_service.py::TestOrderService::test_process_payment_cash", "tests/test_order_service.py::TestOrderService::test_repository_error_handling", "tests/test_order_service.py::TestOrderService::test_submit_order_amount_exceeded", "tests/test_order_service.py::TestOrderService::test_submit_order_success", "tests/test_order_service.py::TestOrderService::test_submit_order_suspicious_content", "tests/test_order_service.py::TestOrderService::test_submit_order_suspicious_pricing", "tests/test_order_service.py::TestOrderService::test_submit_order_too_many_items", "tests/test_order_service.py::TestOrderService::test_validate_business_hours_outside_hours", "tests/test_order_service.py::TestOrderService::test_validate_business_hours_within_hours", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_error_handling_edge_cases", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_format_phone_number", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_send_kitchen_notification_success", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_send_new_order_notification_no_recipients", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_send_new_order_notification_partial_failure", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_send_new_order_notification_success", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_send_order_status_update_failure", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_send_order_status_update_success", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_test_connection_success", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_validate_phone_number"]
{"tests/test_order_service.py::TestOrderService::test_submit_order_success": true, "tests/test_order_service.py::TestOrderService::test_submit_order_amount_exceeded": true, "tests/test_order_service.py::TestOrderService::test_submit_order_too_many_items": true, "tests/test_order_service.py::TestOrderService::test_submit_order_suspicious_pricing": true, "tests/test_order_service.py::TestOrderService::test_submit_order_suspicious_content": true, "tests/test_order_service.py::TestOrderService::test_validate_business_hours_outside_hours": true, "tests/test_order_service.py::TestOrderService::test_validate_business_hours_within_hours": true, "tests/test_order_service.py::TestOrderService::test_check_suspicious_patterns_high_quantity": true, "tests/test_order_service.py::TestOrderService::test_check_suspicious_patterns_sql_injection": true, "tests/test_order_service.py::TestOrderService::test_process_payment_cash": true, "tests/test_order_service.py::TestOrderService::test_process_payment_card_success": true, "tests/test_order_service.py::TestOrderService::test_process_payment_card_failure": true, "tests/test_order_service.py::TestOrderService::test_repository_error_handling": true, "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_send_new_order_notification_success": true, "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_send_kitchen_notification_success": true, "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_error_handling_edge_cases": true}
# BHEEMDINE Menu API Examples

## Overview

This document provides comprehensive examples for using the BHEEMDINE Menu API, including sample requests, responses, and common use cases.

## Base URL

```
https://api.bheemdine.com/api/v1/menu
```

## Authentication

The API supports multiple authentication methods:
- **Anonymous**: For QR code menu access
- **JWT Bearer Token**: For authenticated customers and staff
- **API Key**: For partner integrations

```bash
# Authenticated request
curl -H "Authorization: Bearer <jwt_token>" \
     -H "Content-Type: application/json" \
     "https://api.bheemdine.com/api/v1/menu/{tenant_id}"
```

## API Endpoints

### 1. Get Menu Items (POST /api/v1/menu/{tenant_id})

#### Basic Menu Request

```bash
curl -X POST "https://api.bheemdine.com/api/v1/menu/11111111-1111-1111-1111-111111111111" \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": "11111111-1111-1111-1111-111111111111",
    "filters": {
      "available_only": true
    },
    "page": 1,
    "page_size": 20
  }'
```

#### Request Body Schema

```json
{
  "tenant_id": "uuid",           // Required: Restaurant/Hotel ID
  "filters": {                   // Optional: Menu filters
    "categories": ["mains", "appetizers"],
    "vegetarian_only": true,
    "vegan_only": false,
    "exclude_allergens": ["peanuts", "dairy"],
    "min_price": 10.00,
    "max_price": 50.00,
    "available_only": true,
    "max_prep_time": 30,
    "search_query": "pasta",
    "tags": ["spicy", "chef-special"]
  },
  "pricing_context": {           // Optional: Dynamic pricing context
    "room_id": "uuid",
    "customer_type": "vip",
    "customer_id": "uuid",
    "order_quantity": 2,
    "current_order_total": 45.50,
    "request_time": "2024-01-15T18:30:00Z",
    "special_event": "happy_hour",
    "promo_code": "WELCOME10"
  },
  "page": 1,                     // Page number (1-based)
  "page_size": 50,               // Items per page (max 100)
  "sort_by": "name",             // Sort field
  "sort_order": "asc"            // Sort direction
}
```

#### Response Schema

```json
{
  "items": [
    {
      "id": "*************-2222-2222-************",
      "name": "Grilled Salmon",
      "description": "Fresh Atlantic salmon with herbs",
      "category": "mains",
      "pricing": {
        "basePrice": 28.00,
        "currentPrice": 25.20,
        "discountPercentage": 10.0,
        "discountAmount": 2.80,
        "appliedRules": ["Happy Hour Mains"]
      },
      "availability": {
        "isAvailable": true,
        "remainingQuantity": 15,
        "estimatedWaitTime": 20,
        "unavailableReason": null,
        "availableAgainAt": null
      },
      "imageUrl": "https://images.bheemdine.com/salmon.jpg",
      "isVegetarian": false,
      "isVegan": false,
      "preparationTime": 20,
      "tags": ["gluten-free", "healthy"],
      "allergens": [
        {
          "id": "33333333-3333-3333-3333-333333333333",
          "name": "Fish",
          "icon": "fish",
          "severity": "CONTAINS"
        }
      ],
      "sortOrder": 10,
      "updatedAt": "2024-01-15T12:00:00Z"
    }
  ],
  "total_items": 45,
  "total_pages": 3,
  "current_page": 1,
  "page_size": 20,
  "applied_filters": {
    "available_only": true,
    "categories": ["mains"]
  },
  "pricing_context": {
    "room_id": "44444444-4444-4444-4444-444444444444",
    "order_quantity": 1
  },
  "request_time": "2024-01-15T18:30:00Z",
  "response_time_ms": 125.5,
  "available_categories": ["appetizers", "mains", "desserts", "beverages"],
  "active_pricing_rules": ["Happy Hour Mains", "Bulk Discount"]
}
```

## Common Use Cases

### 1. QR Code Menu Access (Anonymous)

When a customer scans a QR code, they get the basic menu without authentication:

```bash
curl -X POST "https://api.bheemdine.com/api/v1/menu/11111111-1111-1111-1111-111111111111" \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": "11111111-1111-1111-1111-111111111111",
    "pricing_context": {
      "room_id": "r1111111-1111-1111-1111-111111111111"
    },
    "filters": {
      "available_only": true
    }
  }'
```

### 2. Filtered Menu Request

Customer looking for vegetarian appetizers under $15:

```bash
curl -X POST "https://api.bheemdine.com/api/v1/menu/11111111-1111-1111-1111-111111111111" \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": "11111111-1111-1111-1111-111111111111",
    "filters": {
      "categories": ["appetizers"],
      "vegetarian_only": true,
      "max_price": 15.00,
      "available_only": true
    },
    "sort_by": "price",
    "sort_order": "asc"
  }'
```

### 3. Allergen-Aware Menu Request

Customer excluding peanuts and dairy:

```bash
curl -X POST "https://api.bheemdine.com/api/v1/menu/11111111-1111-1111-1111-111111111111" \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": "11111111-1111-1111-1111-111111111111",
    "filters": {
      "exclude_allergens": ["peanuts", "dairy"],
      "available_only": true
    }
  }'
```

### 4. Bulk Order Pricing

Customer ordering multiple items to check bulk discounts:

```bash
curl -X POST "https://api.bheemdine.com/api/v1/menu/11111111-1111-1111-1111-111111111111" \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": "11111111-1111-1111-1111-111111111111",
    "pricing_context": {
      "order_quantity": 5,
      "current_order_total": 120.00
    },
    "filters": {
      "categories": ["mains"]
    }
  }'
```

### 5. Happy Hour Menu

Menu during happy hour times:

```bash
curl -X POST "https://api.bheemdine.com/api/v1/menu/11111111-1111-1111-1111-111111111111" \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": "11111111-1111-1111-1111-111111111111",
    "pricing_context": {
      "request_time": "2024-01-15T18:00:00Z"
    },
    "filters": {
      "categories": ["beverages", "appetizers"]
    }
  }'
```

### 6. Search Menu Items

Customer searching for "pasta" dishes:

```bash
curl -X POST "https://api.bheemdine.com/api/v1/menu/11111111-1111-1111-1111-111111111111" \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": "11111111-1111-1111-1111-111111111111",
    "filters": {
      "search_query": "pasta",
      "available_only": true
    }
  }'
```

## Additional Endpoints

### Get Menu Categories

```bash
curl "https://api.bheemdine.com/api/v1/menu/11111111-1111-1111-1111-111111111111/categories"
```

**Response:**
```json
["appetizers", "mains", "desserts", "beverages", "sides"]
```

### Get Single Menu Item

```bash
curl "https://api.bheemdine.com/api/v1/menu/11111111-1111-1111-1111-111111111111/item/*************-2222-2222-************?quantity=3&room_id=r1111111-1111-1111-1111-111111111111"
```

**Response:**
```json
{
  "id": "*************-2222-2222-************",
  "name": "Grilled Salmon",
  "description": "Fresh Atlantic salmon with herbs",
  "category": "mains",
  "pricing": {
    "basePrice": 28.00,
    "currentPrice": 25.20,
    "discountPercentage": 10.0,
    "appliedRules": ["Bulk Discount - 3+ items"]
  },
  "availability": {
    "isAvailable": true,
    "remainingQuantity": 15,
    "estimatedWaitTime": 20
  },
  "imageUrl": "https://images.bheemdine.com/salmon.jpg",
  "isVegetarian": false,
  "isVegan": false,
  "preparationTime": 20,
  "tags": ["gluten-free", "healthy"],
  "allergens": [
    {
      "id": "33333333-3333-3333-3333-333333333333",
      "name": "Fish",
      "icon": "fish",
      "severity": "CONTAINS"
    }
  ],
  "sortOrder": 10,
  "updatedAt": "2024-01-15T12:00:00Z"
}
```

## Error Responses

### 404 - Tenant Not Found

```json
{
  "error": "Tenant not found or inactive",
  "error_code": "TENANT_NOT_FOUND",
  "details": {
    "tenant_id": "11111111-1111-1111-1111-111111111111"
  },
  "timestamp": "2024-01-15T18:30:00Z"
}
```

### 400 - Invalid Filters

```json
{
  "error": "Validation error",
  "error_code": "INVALID_FILTERS", 
  "details": {
    "field": "max_price",
    "message": "max_price must be greater than min_price"
  },
  "timestamp": "2024-01-15T18:30:00Z"
}
```

### 403 - Access Denied

```json
{
  "error": "Access denied to tenant",
  "error_code": "TENANT_ACCESS_DENIED",
  "details": {
    "requested_tenant": "11111111-1111-1111-1111-111111111111",
    "user_tenant": "*************-2222-2222-************"
  },
  "timestamp": "2024-01-15T18:30:00Z"
}
```

## Dynamic Pricing Examples

### Time-Based Pricing (Happy Hour)

**Request during happy hour (5-7 PM):**
```json
{
  "pricing_context": {
    "request_time": "2024-01-15T18:00:00Z"
  }
}
```

**Response shows discounted prices:**
```json
{
  "pricing": {
    "basePrice": 12.00,
    "currentPrice": 9.00,
    "discountPercentage": 25.0,
    "appliedRules": ["Happy Hour Beverages"]
  }
}
```

### Quantity-Based Pricing

**Request for bulk order:**
```json
{
  "pricing_context": {
    "order_quantity": 5
  }
}
```

**Response with bulk discount:**
```json
{
  "pricing": {
    "basePrice": 15.00,
    "currentPrice": 13.50,
    "discountPercentage": 10.0,
    "appliedRules": ["Bulk Discount - 5+ items"]
  }
}
```

### VIP Room Pricing

**Request from VIP room:**
```json
{
  "pricing_context": {
    "room_id": "vip-room-uuid",
    "customer_type": "vip"
  }
}
```

**Response with premium pricing:**
```json
{
  "pricing": {
    "basePrice": 25.00,
    "currentPrice": 30.00,
    "markupPercentage": 20.0,
    "appliedRules": ["VIP Room Premium"]
  }
}
```

## Availability Examples

### Out of Stock Item

```json
{
  "availability": {
    "isAvailable": false,
    "remainingQuantity": 0,
    "unavailableReason": "Out of stock",
    "availableAgainAt": "2024-01-16T06:00:00Z"
  }
}
```

### Time-Restricted Item (Breakfast)

```json
{
  "availability": {
    "isAvailable": false,
    "unavailableReason": "Available only from 06:00 to 11:00",
    "availableAgainAt": "2024-01-16T06:00:00Z"
  }
}
```

### High Kitchen Load

```json
{
  "availability": {
    "isAvailable": true,
    "remainingQuantity": 10,
    "estimatedWaitTime": 35
  }
}
```

## Performance Considerations

- **Response Times**: Typically 50-200ms for menu requests
- **Caching**: Menu items cached for 5 minutes, pricing calculated real-time
- **Pagination**: Maximum 100 items per page
- **Rate Limiting**: 1000 requests per hour per IP
- **Database Optimization**: Queries use indexes on tenant_id, category, and availability

## SDK Examples

### JavaScript/TypeScript

```typescript
import { BheemdineMenuAPI } from '@bheemdine/api-client';

const api = new BheemdineMenuAPI({
  baseURL: 'https://api.bheemdine.com',
  apiKey: 'your-api-key'
});

// Get menu with filters
const menu = await api.getMenu({
  tenantId: '11111111-1111-1111-1111-111111111111',
  filters: {
    categories: ['mains'],
    vegetarianOnly: true,
    maxPrice: 25.00
  },
  pricingContext: {
    roomId: 'room-uuid',
    orderQuantity: 2
  }
});

console.log(`Found ${menu.totalItems} items`);
menu.items.forEach(item => {
  console.log(`${item.name}: $${item.pricing.currentPrice}`);
});
```

### Python

```python
from bheemdine_api import MenuAPI

api = MenuAPI(
    base_url='https://api.bheemdine.com',
    api_key='your-api-key'
)

# Get menu with dynamic pricing
menu = api.get_menu(
    tenant_id='11111111-1111-1111-1111-111111111111',
    filters={
        'categories': ['mains'],
        'available_only': True
    },
    pricing_context={
        'room_id': 'room-uuid',
        'order_quantity': 3
    }
)

for item in menu.items:
    print(f"{item.name}: ${item.pricing.current_price}")
    if item.pricing.applied_rules:
        print(f"  Discounts: {', '.join(item.pricing.applied_rules)}")
```
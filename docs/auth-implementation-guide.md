# BHEEMDINE Authentication Implementation Guide

## Overview

This guide covers the complete authentication system for BHEEMDINE using Supabase Auth with custom JWT claims, role-based access control, and multi-tenant isolation.

## Authentication Architecture

### User Types

1. **Anonymous Users**: QR code scanners, no authentication required
2. **Guest Users**: Temporary users created after QR scan for ordering
3. **Registered Customers**: Users with email/password accounts
4. **Staff Members**: Restaurant/hotel employees with PIN-based auth

### JWT Claims Structure

```typescript
interface CustomClaims {
  user_id: string;
  tenant_id?: string;        // Which restaurant/hotel
  tenant_slug?: string;      // URL-friendly tenant identifier
  user_type?: 'guest' | 'customer' | 'staff';
  role?: 'guest' | 'customer' | 'staff' | 'anonymous';
  staff_role?: 'ADMIN' | 'MANAGER' | 'CHEF' | 'WAITER' | 'RECEPTIONIST';
  staff_id?: string;         // Reference to Staff table
  customer_id?: string;      // Reference to User table
  room_id?: string;          // Current room assignment
}
```

## Implementation Details

### 1. Database Schema Extensions

#### auth.user_metadata Table
Stores additional user information not in Supabase Auth:
- Links auth.users to public.Staff or public.User
- Tracks tenant association
- Stores user type and context

#### Custom Functions
- `auth.custom_jwt_claims()`: Generates JWT claims
- `auth.create_guest_user()`: Creates temporary guest accounts
- `auth.authenticate_staff_pin()`: PIN-based staff authentication
- `auth.validate_qr_access()`: QR code validation

### 2. Authentication Flows

#### QR Code Guest Flow
```typescript
// 1. Scan QR code
const qrResult = await bheemdineAuth.validateQRCode(qrCode);

// 2. Create guest session
const { session, customerId } = await bheemdineAuth.createGuestSession(
  qrResult.tenant_id,
  qrResult.room_id,
  'Guest User'
);

// 3. Access menu and place orders
```

#### Staff Authentication Flow
```typescript
// 1. Authenticate with PIN
const { session, staffId, staffRole } = await bheemdineAuth.authenticateStaffPIN(
  tenantId,
  '<EMAIL>',
  '1234'
);

// 2. Quick re-auth for sensitive actions
const isValid = await bheemdineAuth.quickStaffAuth('1234');
```

#### Customer Registration Flow
```typescript
// Option 1: Direct registration
const session = await bheemdineAuth.registerCustomer(
  '<EMAIL>',
  'password123',
  'John Doe',
  tenantId
);

// Option 2: Promote guest to customer
const success = await bheemdineAuth.promoteGuestToCustomer(
  '<EMAIL>',
  'password123'
);
```

### 3. Role-Based Access Control

#### Permission Matrix

| Action | Anonymous | Guest | Customer | Staff | Admin |
|--------|-----------|-------|----------|-------|-------|
| View Menu | ✅ | ✅ | ✅ | ✅ | ✅ |
| Place Order | ❌ | ✅ | ✅ | ✅ | ✅ |
| View Own Orders | ❌ | ✅ | ✅ | ✅ | ✅ |
| View All Orders | ❌ | ❌ | ❌ | ✅ | ✅ |
| Manage Menu | ❌ | ❌ | ❌ | ⚠️* | ✅ |
| Manage Staff | ❌ | ❌ | ❌ | ❌ | ✅ |

*Depends on staff role (CHEF can edit menu)

#### Role Checks

```typescript
// Check basic role
const isStaff = await bheemdineAuth.hasRole('staff');

// Check specific staff role
const isManager = await bheemdineAuth.hasRole('MANAGER');

// Component protection
const ProtectedComponent = withAuth(MyComponent, 'ADMIN');
```

### 4. Multi-Tenant Considerations

#### Tenant Isolation
- All data scoped to tenant_id
- JWT claims include tenant context
- RLS policies enforce boundaries

#### Multi-Location Staff
Some staff may work at multiple locations:

```typescript
// Switch tenant context
await bheemdineAuth.switchTenant(newTenantId);

// Get available tenants
const claims = await bheemdineAuth.getCustomClaims();
const currentTenant = claims.tenant_id;
```

## Security Best Practices

### 1. JWT Security
- Short token expiration (15 minutes)
- Automatic refresh tokens
- Custom claims validation on every request

### 2. PIN Security
- 4-6 digit PINs for staff
- Rate limiting on failed attempts
- PIN rotation policies

### 3. Guest Session Management
- Limited session duration (2 hours)
- Cleanup of expired guest accounts
- No persistent storage of sensitive data

### 4. Cross-Tenant Protection
- Validate tenant_id in every request
- Use RLS policies as secondary protection
- Audit cross-tenant access attempts

## Development Patterns

### 1. Auth Context Provider

```typescript
// AuthProvider.tsx
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [claims, setClaims] = useState<CustomClaims>({});

  useEffect(() => {
    // Initialize auth state
    bheemdineAuth.getSession().then(setSession);
    
    // Subscribe to changes
    const { data: { subscription } } = bheemdineAuth.onAuthStateChange(
      (event, session) => {
        setSession(session);
        if (session) {
          bheemdineAuth.getCustomClaims().then(setClaims);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  return (
    <AuthContext.Provider value={{ session, claims }}>
      {children}
    </AuthContext.Provider>
  );
};
```

### 2. Route Protection

```typescript
// middleware.ts
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Public routes
  if (pathname.startsWith('/qr/') || pathname === '/menu') {
    return NextResponse.next();
  }
  
  // Staff routes
  if (pathname.startsWith('/staff/')) {
    // Check staff authentication
    const claims = getClaimsFromRequest(request);
    if (claims.role !== 'staff') {
      return NextResponse.redirect('/login/staff');
    }
  }
  
  return NextResponse.next();
}
```

### 3. API Route Protection

```typescript
// api/orders/route.ts
export async function GET(request: Request) {
  const claims = await getCustomClaims(request);
  
  if (!claims.tenant_id) {
    return new Response('Unauthorized', { status: 401 });
  }
  
  // Query with tenant filtering
  const orders = await supabase
    .from('Order')
    .select('*')
    .eq('tenantId', claims.tenant_id);
    
  return Response.json(orders);
}
```

## Testing Authentication

### 1. Unit Tests
```typescript
describe('BHEEMDINE Auth', () => {
  it('should create guest session from QR', async () => {
    const result = await bheemdineAuth.validateQRCode('TEST-QR-123');
    expect(result.data?.valid).toBe(true);
  });
  
  it('should authenticate staff with PIN', async () => {
    const result = await bheemdineAuth.authenticateStaffPIN(
      'tenant-id',
      '<EMAIL>',
      '1234'
    );
    expect(result.data?.staffRole).toBe('WAITER');
  });
});
```

### 2. Integration Tests
```typescript
describe('Auth Flows', () => {
  it('should complete guest ordering flow', async () => {
    // 1. Scan QR
    const qr = await validateQR('QR-101');
    
    // 2. Create guest
    const guest = await createGuest(qr.tenant_id, qr.room_id);
    
    // 3. Place order
    const order = await placeOrder({ items: [{ id: 'item-1', qty: 2 }] });
    
    expect(order.status).toBe('PENDING');
  });
});
```

## Production Deployment

### 1. Environment Variables
```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Auth configuration
JWT_SECRET=your-jwt-secret
JWT_EXPIRY=900  # 15 minutes
REFRESH_TOKEN_EXPIRY=604800  # 7 days
```

### 2. Security Headers
```typescript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
    ];
  },
};
```

### 3. Monitoring and Alerts
- Track failed authentication attempts
- Monitor JWT token usage patterns
- Alert on cross-tenant access attempts
- Log staff PIN authentication failures

## Troubleshooting

### Common Issues

1. **"Invalid JWT claims"**
   - Check custom_jwt_claims function
   - Verify user_metadata table data

2. **"Cross-tenant access denied"**
   - Validate tenant_id in JWT
   - Check RLS policies

3. **"Staff PIN authentication failed"**
   - Verify staff is active
   - Check tenant is active
   - Ensure PIN matches

4. **"Guest session expired"**
   - Implement session refresh
   - Guide user to re-scan QR code
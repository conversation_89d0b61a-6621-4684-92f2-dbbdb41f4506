# TapDine Order Review & Submission Component Guide

A comprehensive React component system for handling order review, validation, and submission in the TapDine digital menu platform.

## Overview

The Order Review & Submission system provides a complete user experience for:
- Reviewing cart items with customizations
- Multi-step form for customer information, delivery details, and payment
- Real-time validation with user-friendly error handling
- Seamless integration with the backend API
- Mobile-responsive design with accessibility features

## Components

### 1. OrderReviewSubmission (Main Component)

The primary component that orchestrates the entire order submission flow.

```tsx
import { OrderReviewSubmission } from '@/components/order/OrderReviewSubmission';

<OrderReviewSubmission
  tenantId="restaurant-123"
  roomId="room-456" 
  tenantName="The Grand Hotel Restaurant"
  onClose={() => setShowCheckout(false)}
  onSuccess={(response) => console.log('Order placed:', response)}
/>
```

**Props:**
- `tenantId` (string, required): Restaurant/tenant identifier
- `roomId` (string, optional): Room identifier for hotel guests
- `tenantName` (string, optional): Display name for the restaurant
- `onClose` (function, optional): Callback when user closes the component
- `onSuccess` (function, optional): Callback when order is successfully submitted
- `className` (string, optional): Additional CSS classes

### 2. OrderReviewItem

Individual cart item component with editing capabilities.

```tsx
import { OrderReviewItem } from '@/components/order/OrderReviewItem';

<OrderReviewItem
  item={cartItem}
  onUpdateQuantity={(id, qty) => updateQuantity(id, qty)}
  onRemoveItem={(id) => removeItem(id)}
  onEditCustomizations={(id) => openEditModal(id)}
  isEditable={true}
  showEstimatedTime={true}
/>
```

### 3. OrderFormStep

Multi-step form components for collecting order information.

```tsx
import { OrderFormStep } from '@/components/order/OrderFormStep';

<OrderFormStep
  step="customer" // 'customer' | 'delivery' | 'payment'
  data={formData}
  errors={formErrors}
  onChange={(data) => setFormData(data)}
  onValidate={() => validateForm()}
/>
```

### 4. useOrderSubmission Hook

Custom hook for handling order submission logic and API integration.

```tsx
import { useOrderSubmission } from '@/hooks/useOrderSubmission';

const {
  isSubmitting,
  submitError,
  submitOrder,
  validateOrder,
  clearError,
  lastSubmission,
} = useOrderSubmission({
  tenantId: 'restaurant-123',
  onSuccess: (response) => console.log('Success:', response),
  onError: (error) => console.error('Error:', error),
});
```

## Features

### 🔍 Order Review

- **Interactive Item Management**: Add, remove, and modify quantities
- **Customization Display**: Show modifications, special instructions, and notes
- **Price Calculation**: Real-time subtotal, tax, and total calculation
- **Availability Checking**: Visual indicators for unavailable items
- **Estimated Time**: Preparation time display for each item

### ✅ Form Validation

- **Real-time Validation**: Validate fields as users type
- **Visual Feedback**: Color-coded inputs with success/error states
- **Comprehensive Error Messages**: Clear, actionable error descriptions
- **Step-by-step Validation**: Prevent progression with invalid data
- **Business Rule Validation**: Order limits, business hours, etc.

### 💳 Payment Processing

- **Multiple Payment Methods**: Cash, Card, Room Charge, Digital Wallet
- **Secure Integration**: Ready for Stripe, Square, or other processors
- **Tip Calculation**: Preset percentages or custom amounts
- **Billing Validation**: Name and payment method verification

### 🎯 User Experience

- **Progress Indicator**: Visual progress through order steps
- **Mobile Responsive**: Optimized for all screen sizes
- **Loading States**: Smooth transitions with loading indicators
- **Error Recovery**: Clear error messages with retry options
- **Accessibility**: Screen reader support and keyboard navigation

### 🔗 Backend Integration

- **RESTful API**: Seamless integration with FastAPI backend
- **Error Handling**: Comprehensive error parsing and display
- **Request Management**: Automatic retry logic and cancellation
- **State Synchronization**: Cart state management with persistence

## Installation & Setup

### 1. Install Dependencies

```bash
npm install zustand lucide-react
# or
yarn add zustand lucide-react
```

### 2. Add Type Definitions

Copy the type definitions from `src/types/order.ts` to your project.

### 3. Set Up State Management

Ensure you have the Zustand menu store configured (see `src/stores/menuStore.ts`).

### 4. Configure API Endpoint

Update the API endpoint in your environment or component props:

```tsx
const { submitOrder } = useOrderSubmission({
  tenantId: 'your-tenant-id',
  apiEndpoint: '/api/v1/orders/submit', // Your backend endpoint
});
```

## Usage Examples

### Basic Implementation

```tsx
import React, { useState } from 'react';
import { OrderReviewSubmission } from '@/components/order/OrderReviewSubmission';
import { useMenuStore } from '@/stores/menuStore';

function CheckoutPage() {
  const [showCheckout, setShowCheckout] = useState(false);
  const { cartCount } = useMenuStore();

  const handleOrderSuccess = (response) => {
    console.log('Order placed successfully:', response);
    setShowCheckout(false);
    // Redirect to tracking page
    window.location.href = `/track/${response.orderId}`;
  };

  if (showCheckout) {
    return (
      <OrderReviewSubmission
        tenantId="restaurant-123"
        roomId="room-456"
        tenantName="The Grand Hotel Restaurant"
        onClose={() => setShowCheckout(false)}
        onSuccess={handleOrderSuccess}
      />
    );
  }

  return (
    <div>
      <h1>Your Menu</h1>
      {/* Menu items */}
      
      {cartCount > 0 && (
        <button 
          onClick={() => setShowCheckout(true)}
          className="checkout-button"
        >
          Proceed to Checkout ({cartCount} items)
        </button>
      )}
    </div>
  );
}
```

### Modal Implementation

```tsx
import React, { useState } from 'react';
import { OrderReviewSubmission } from '@/components/order/OrderReviewSubmission';

function MenuPage() {
  const [isCheckoutOpen, setIsCheckoutOpen] = useState(false);

  return (
    <>
      {/* Your menu content */}
      
      {/* Modal Overlay */}
      {isCheckoutOpen && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50">
          <div className="h-full overflow-auto">
            <OrderReviewSubmission
              tenantId="restaurant-123"
              onClose={() => setIsCheckoutOpen(false)}
              className="max-w-4xl mx-auto"
            />
          </div>
        </div>
      )}
    </>
  );
}
```

### Custom Validation

```tsx
import { useOrderSubmission } from '@/hooks/useOrderSubmission';

function CustomCheckout() {
  const { validateOrder, submitOrder } = useOrderSubmission({
    tenantId: 'restaurant-123',
    onError: (error) => {
      // Custom error handling
      if (error.errorCode === 'BUSINESS_HOURS') {
        showBusinessHoursModal();
      } else {
        showGenericErrorToast(error.errorMessage);
      }
    },
  });

  const handleCustomValidation = (formData) => {
    const errors = validateOrder(formData);
    
    // Add custom business rules
    if (formData.deliveryInfo.roomNumber.startsWith('PENTHOUSE')) {
      if (!formData.customerInfo.phone) {
        errors.customerInfo = {
          ...errors.customerInfo,
          phone: 'Phone number required for penthouse deliveries'
        };
      }
    }
    
    return errors;
  };
}
```

## API Integration

The component integrates with the FastAPI backend using the following endpoint:

### Endpoint: `POST /api/v1/orders/submit`

**Request Format:**
```json
{
  "tenantId": "restaurant-123",
  "roomId": "room-456",
  "customerInfo": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890"
  },
  "items": [
    {
      "menuItemId": "item-123",
      "quantity": 2,
      "unitPrice": 15.99,
      "customizations": {
        "modifications": ["No onions"],
        "specialInstructions": "Medium rare"
      },
      "notes": "Extra sauce on the side"
    }
  ],
  "deliveryInfo": {
    "roomNumber": "205A",
    "deliveryInstructions": "Knock softly",
    "preferredDeliveryTime": "2024-12-15T19:00:00Z",
    "isContactless": false
  },
  "paymentInfo": {
    "paymentMethod": "CARD",
    "paymentToken": "tok_1234567890",
    "billingName": "John Doe",
    "tipAmount": 5.00
  },
  "orderNotes": "Special occasion dinner",
  "estimatedTotal": 45.98
}
```

**Response Format:**
```json
{
  "orderId": "order-123",
  "orderNumber": "ORD-241215-0042",
  "status": "PENDING",
  "placedAt": "2024-12-15T18:30:00Z",
  "estimatedPreparationTime": 25,
  "estimatedReadyTime": "2024-12-15T18:55:00Z",
  "subtotal": 35.98,
  "taxAmount": 2.88,
  "tipAmount": 5.00,
  "totalAmount": 43.86,
  "confirmationMessage": "Your order has been received!",
  "trackingUrl": "/track/order-123",
  "tenantName": "The Grand Hotel Restaurant",
  "roomNumber": "205A"
}
```

### Error Handling

The component handles various error scenarios:

**Validation Errors (400):**
```json
{
  "errorCode": "VALIDATION_ERROR",
  "errorMessage": "Please fix the validation errors",
  "fieldErrors": {
    "customerInfo.name": ["Name is required"],
    "deliveryInfo.roomNumber": ["Invalid room number format"]
  }
}
```

**Business Rule Errors (409):**
```json
{
  "errorCode": "BUSINESS_HOURS",
  "errorMessage": "Orders can only be placed between 6 AM and 11 PM",
  "details": {
    "currentHour": 23,
    "businessHours": "6:00 AM - 11:00 PM"
  }
}
```

## Styling & Customization

### CSS Classes

The component uses Tailwind CSS classes. Key styling elements:

```css
/* Progress indicator */
.progress-step-active { @apply bg-orange-100 text-orange-700 border-orange-200; }
.progress-step-completed { @apply bg-green-100 text-green-700; }

/* Form validation */
.form-field-valid { @apply border-green-300 bg-green-50; }
.form-field-error { @apply border-red-300 focus:border-red-500; }

/* Loading states */
.loading-spinner { @apply border-2 border-orange-500 border-t-transparent rounded-full animate-spin; }
```

### Customization Options

1. **Theme Colors**: Update color classes throughout components
2. **Layout**: Modify responsive breakpoints and spacing
3. **Validation Rules**: Extend validation functions in the hook
4. **Payment Methods**: Add/remove payment options in OrderFormStep
5. **Field Requirements**: Modify required fields based on business needs

## Testing

### Unit Tests

```tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { OrderReviewSubmission } from '@/components/order/OrderReviewSubmission';

describe('OrderReviewSubmission', () => {
  it('should display cart items correctly', () => {
    // Mock cart with items
    render(<OrderReviewSubmission tenantId="test" />);
    
    expect(screen.getByText('Review Your Order')).toBeInTheDocument();
  });

  it('should validate required fields', async () => {
    render(<OrderReviewSubmission tenantId="test" />);
    
    // Try to submit without required fields
    fireEvent.click(screen.getByText('Place Order'));
    
    await waitFor(() => {
      expect(screen.getByText('Name is required')).toBeInTheDocument();
    });
  });
});
```

### Integration Tests

```tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { rest } from 'msw';
import { setupServer } from 'msw/node';

const server = setupServer(
  rest.post('/api/v1/orders/submit', (req, res, ctx) => {
    return res(ctx.json({ 
      orderId: 'test-order-123',
      orderNumber: 'ORD-TEST-001',
      status: 'PENDING'
    }));
  })
);

describe('Order Submission Flow', () => {
  beforeAll(() => server.listen());
  afterEach(() => server.resetHandlers());
  afterAll(() => server.close());

  it('should complete full order submission', async () => {
    // Test complete flow from cart to confirmation
  });
});
```

## Performance Considerations

### Optimization Strategies

1. **Lazy Loading**: Load form steps on demand
2. **Memoization**: Use React.memo for expensive components
3. **Debounced Validation**: Reduce validation frequency on input
4. **Virtual Scrolling**: For large cart item lists
5. **Image Optimization**: Lazy load and optimize menu item images

### Bundle Size

The component system adds approximately:
- **Core Components**: ~15KB gzipped
- **Dependencies**: Zustand (~5KB), Lucide React (~20KB)
- **Total Impact**: ~40KB for complete order system

## Accessibility

### Features Included

- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Keyboard Navigation**: Full keyboard accessibility
- **Focus Management**: Logical tab order through forms
- **Color Contrast**: WCAG 2.1 AA compliant colors
- **Error Announcements**: Screen reader error notifications

### ARIA Attributes

```tsx
<div role="form" aria-labelledby="checkout-title">
  <h2 id="checkout-title">Order Checkout</h2>
  
  <input
    aria-describedby="name-error"
    aria-invalid={!!errors.name}
    aria-required="true"
  />
  
  <div id="name-error" role="alert">
    {errors.name}
  </div>
</div>
```

## Browser Support

- **Modern Browsers**: Chrome 88+, Firefox 85+, Safari 14+, Edge 88+
- **Mobile**: iOS Safari 14+, Chrome Mobile 88+
- **Progressive Enhancement**: Graceful degradation for older browsers

## Troubleshooting

### Common Issues

1. **Cart State Not Persisting**
   - Ensure Zustand persistence middleware is configured
   - Check localStorage availability

2. **API Calls Failing**
   - Verify backend endpoint URL
   - Check CORS configuration
   - Validate request format

3. **Validation Not Working**
   - Check form data structure matches expected types
   - Verify validation rules in useOrderSubmission hook

4. **Styling Issues**
   - Ensure Tailwind CSS is properly configured
   - Check for conflicting CSS rules

### Debug Mode

Enable debug mode for detailed logging:

```tsx
const { submitOrder } = useOrderSubmission({
  tenantId: 'test',
  debug: true, // Enable detailed logging
});
```

This comprehensive order review and submission system provides a production-ready solution for digital menu applications with robust validation, error handling, and user experience optimizations.
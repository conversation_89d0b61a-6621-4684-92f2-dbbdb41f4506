# TapDine Menu Management CRUD System

A comprehensive, reusable, and responsive CRUD system for managing menu items and categories in TapDine's tenant admin dashboard. Built with React, TypeScript, and modern UX best practices.

## 🎯 Overview

The Menu CRUD System provides:
- **Reusable Components**: Generic CRUD hooks and components that can be adapted for any data type
- **Responsive Design**: Mobile-first approach with table/grid view switching
- **Advanced Filtering**: Multi-criteria search and filtering with real-time updates
- **Bulk Operations**: Select multiple items and perform batch actions
- **Form Validation**: Real-time validation with user-friendly error messages
- **Drag & Drop**: Intuitive reordering for categories and menu items
- **Image Management**: Upload, preview, and manage item images
- **Role-Based Access**: Permission-controlled features based on user roles

## 🏗️ Architecture

### Core Components

```
src/
├── hooks/
│   ├── useCrud.ts              # Generic CRUD operations hook
│   └── useFormValidation.ts    # Form validation with real-time feedback
├── types/
│   └── menu-admin.ts           # TypeScript interfaces and types
└── components/admin/menu/
    ├── MenuManagementDashboard.tsx  # Main dashboard component
    ├── MenuItemForm.tsx             # Create/edit menu item form
    ├── MenuItemsTable.tsx           # Responsive table/grid view
    └── CategoryManager.tsx          # Category CRUD with drag-and-drop
```

### Data Flow

```
Dashboard → CRUD Hook → API → Database
    ↓         ↓
Components ← State Management
```

## 🚀 Quick Start

### 1. Basic Menu Management Dashboard

```typescript
import { MenuManagementDashboard } from '@/components/admin/menu/MenuManagementDashboard';

export default function MenuAdminPage() {
  return <MenuManagementDashboard />;
}
```

### 2. Using the CRUD Hook

```typescript
import { useCrud } from '@/hooks/useCrud';
import type { MenuItemAdmin } from '@/types/menu-admin';

function MyComponent() {
  const [state, actions] = useCrud<MenuItemAdmin>({
    apiEndpoint: '/api/v1/menu-items',
    optimisticUpdates: true,
    onSuccess: (action, item) => {
      console.log(`${action} completed:`, item);
    },
    onError: (action, error) => {
      console.error(`${action} failed:`, error);
    }
  });

  // Create item
  const handleCreate = async (data) => {
    await actions.createItem(data);
  };

  // Update item
  const handleUpdate = async (id, data) => {
    await actions.updateItem(id, data);
  };

  return (
    <div>
      {state.loading && <LoadingSpinner />}
      {state.items.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
}
```

### 3. Form Validation

```typescript
import { useFormValidation } from '@/hooks/useFormValidation';

const VALIDATION_SCHEMA = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    pattern: /^[a-zA-Z0-9\s\-'.&]+$/,
  },
  price: {
    required: true,
    min: 0.01,
    max: 999.99,
    custom: (value) => {
      const num = parseFloat(value);
      return isNaN(num) ? 'Invalid price' : null;
    }
  }
};

function MyForm() {
  const [formData, setFormData] = useState({ name: '', price: 0 });
  const [validation, actions] = useFormValidation(VALIDATION_SCHEMA);

  const handleSubmit = (e) => {
    e.preventDefault();
    const errors = actions.validateForm(formData);
    if (Object.keys(errors).length === 0) {
      // Submit form
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        value={formData.name}
        onChange={(e) => {
          setFormData({...formData, name: e.target.value});
          validation.createFieldValidator('name').onChange(e.target.value);
        }}
        className={validation.getFieldProps('name').showError ? 'error' : ''}
      />
      {validation.getFieldProps('name').showError && (
        <span className="error">{validation.getFieldProps('name').error}</span>
      )}
    </form>
  );
}
```

## 📱 Components Guide

### MenuManagementDashboard

The main dashboard component that orchestrates all menu management functionality.

**Features:**
- Tabbed interface (Items, Categories, Analytics)
- Permission-based access control
- Bulk operations toolbar
- Real-time success/error messaging
- Data refresh functionality

**Props:**
```typescript
interface MenuManagementDashboardProps {
  className?: string;
}
```

**Usage:**
```typescript
<MenuManagementDashboard className="custom-dashboard" />
```

### MenuItemForm

Comprehensive form for creating and editing menu items with advanced validation.

**Features:**
- Real-time validation with visual feedback
- Image upload with preview
- Dietary options and allergen management
- Ingredient management with tag input
- Collapsible advanced options
- Auto-save functionality

**Props:**
```typescript
interface MenuItemFormProps {
  item?: MenuItemAdmin;
  categories: MenuCategoryAdmin[];
  isLoading?: boolean;
  onSubmit: (data: MenuItemFormData) => Promise<void>;
  onCancel: () => void;
  className?: string;
}
```

**Usage:**
```typescript
<MenuItemForm
  item={editingItem}
  categories={categories}
  onSubmit={handleSubmit}
  onCancel={() => setShowForm(false)}
/>
```

### MenuItemsTable

Responsive table/grid view with advanced filtering, sorting, and bulk operations.

**Features:**
- Switch between table and grid layouts
- Advanced filtering (search, category, price, dietary)
- Multi-column sorting
- Bulk selection and actions
- Responsive design
- Pagination support

**Props:**
```typescript
interface MenuItemsTableProps {
  items: MenuItemAdmin[];
  isLoading: boolean;
  selectedItems: Set<string>;
  onItemSelect: (id: string) => void;
  onSelectAll: () => void;
  onClearSelection: () => void;
  onItemEdit: (item: MenuItemAdmin) => void;
  onItemDelete: (id: string) => void;
  onItemClone: (item: MenuItemAdmin) => void;
  onBulkAction: (action: string, itemIds: string[]) => Promise<void>;
  onFiltersChange?: (filters: MenuFilters) => void;
  className?: string;
}
```

### CategoryManager

Category management with drag-and-drop reordering and inline editing.

**Features:**
- Drag-and-drop reordering
- Inline creation and editing
- Image upload for categories
- Active/inactive status toggle
- Item count per category
- Bulk operations

**Props:**
```typescript
interface CategoryManagerProps {
  tenantId: string;
  onCategoryUpdate?: (categories: MenuCategoryAdmin[]) => void;
  className?: string;
}
```

## 🎨 UI/UX Best Practices

### 1. **Progressive Disclosure**
- Advanced options are hidden by default
- Collapsible sections for complex forms
- Step-by-step guidance for complex workflows

### 2. **Real-Time Feedback**
- Immediate validation on form fields
- Loading states for all async operations
- Success/error messages with auto-dismiss
- Optimistic updates for better perceived performance

### 3. **Responsive Design**
- Mobile-first approach
- Adaptive layouts (table ↔ grid)
- Touch-friendly interactions
- Keyboard navigation support

### 4. **Visual Hierarchy**
- Clear information architecture
- Consistent spacing and typography
- Color-coded status indicators
- Intuitive iconography

### 5. **Error Prevention**
- Form validation before submission
- Confirmation dialogs for destructive actions
- Undo functionality where possible
- Clear error messages with suggested fixes

## 🔧 Advanced Features

### Bulk Operations

```typescript
const BULK_ACTIONS: BulkAction[] = [
  {
    id: 'toggle-availability',
    label: 'Toggle Availability',
    icon: Eye,
    action: async (selectedIds) => {
      // Batch update availability
    },
  },
  {
    id: 'delete-items',
    label: 'Delete Items',
    icon: Trash2,
    action: async (selectedIds) => {
      // Batch delete with confirmation
    },
    destructive: true,
    requiresConfirmation: true,
    confirmMessage: 'Are you sure?',
  },
];
```

### Custom Validation Rules

```typescript
const customValidation: ValidationRule = {
  custom: (value, formData) => {
    if (formData.isVegan && !formData.isVegetarian) {
      return 'Vegan items must also be vegetarian';
    }
    return null;
  }
};
```

### Advanced Filtering

```typescript
const filters: MenuFilters = {
  search: 'pizza',
  category: 'Main Courses',
  isAvailable: true,
  isVegetarian: true,
  priceMin: 10,
  priceMax: 25,
  tags: ['Popular', 'Spicy'],
  allergens: ['Gluten', 'Dairy'],
};
```

### Drag and Drop Implementation

```typescript
const handleDrop = async (draggedId: string, targetId: string) => {
  // Optimistic update
  const reorderedItems = reorderArray(items, draggedId, targetId);
  setItems(reorderedItems);
  
  try {
    // Persist to backend
    await updateSortOrders(reorderedItems);
  } catch (error) {
    // Revert on failure
    setItems(originalItems);
  }
};
```

## 📊 Performance Optimizations

### 1. **Optimistic Updates**
```typescript
const [state, actions] = useCrud({
  optimisticUpdates: true, // Updates UI immediately
  // ... other options
});
```

### 2. **Debounced Search**
```typescript
const useFormValidation = (schema, {
  debounceMs: 300, // Debounce validation
});
```

### 3. **Virtual Scrolling** (for large lists)
```typescript
// Render only visible items
const visibleItems = items.slice(startIndex, endIndex);
```

### 4. **Memoization**
```typescript
const filteredItems = useMemo(() => {
  return items.filter(item => matchesFilters(item, filters));
}, [items, filters]);
```

## 🔒 Security Considerations

### 1. **Permission-Based Access**
```typescript
<RequirePermission permissions={[ADMIN_PERMISSIONS.MENU_EDIT]}>
  <EditButton />
</RequirePermission>
```

### 2. **Input Sanitization**
```typescript
const sanitizeInput = (value: string) => {
  return value.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
};
```

### 3. **File Upload Security**
```typescript
const validateImageFile = (file: File) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
  const maxSize = 5 * 1024 * 1024; // 5MB
  
  if (!allowedTypes.includes(file.type)) {
    throw new Error('Invalid file type');
  }
  
  if (file.size > maxSize) {
    throw new Error('File too large');
  }
};
```

## 🧪 Testing

### Unit Tests

```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { MenuItemForm } from '@/components/admin/menu/MenuItemForm';

describe('MenuItemForm', () => {
  it('validates required fields', async () => {
    render(<MenuItemForm categories={[]} onSubmit={jest.fn()} onCancel={jest.fn()} />);
    
    fireEvent.click(screen.getByText('Create Item'));
    
    expect(screen.getByText('Name is required')).toBeInTheDocument();
  });
  
  it('submits valid form data', async () => {
    const onSubmit = jest.fn();
    render(<MenuItemForm categories={[]} onSubmit={onSubmit} onCancel={jest.fn()} />);
    
    fireEvent.change(screen.getByLabelText(/name/i), { target: { value: 'Pizza' } });
    fireEvent.change(screen.getByLabelText(/description/i), { target: { value: 'Delicious pizza' } });
    fireEvent.change(screen.getByLabelText(/price/i), { target: { value: '15.99' } });
    
    fireEvent.click(screen.getByText('Create Item'));
    
    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalledWith({
        name: 'Pizza',
        description: 'Delicious pizza',
        basePrice: 15.99,
        // ... other fields
      });
    });
  });
});
```

### Integration Tests

```typescript
describe('Menu Management Integration', () => {
  it('creates, edits, and deletes menu items', async () => {
    render(<MenuManagementDashboard />);
    
    // Create item
    fireEvent.click(screen.getByText('Add Item'));
    // ... fill form and submit
    
    // Edit item
    fireEvent.click(screen.getByText('Edit'));
    // ... modify and save
    
    // Delete item
    fireEvent.click(screen.getByText('Delete'));
    fireEvent.click(screen.getByText('Confirm'));
    
    // Verify item is removed
    expect(screen.queryByText('Pizza')).not.toBeInTheDocument();
  });
});
```

## 🚀 Deployment

### Environment Variables

```bash
# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://api.tapdine.com
NEXT_PUBLIC_CDN_URL=https://cdn.tapdine.com

# File Upload
MAX_FILE_SIZE=5242880  # 5MB
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# Performance
ENABLE_OPTIMISTIC_UPDATES=true
DEBOUNCE_DELAY=300
```

### Build Optimization

```javascript
// next.config.js
module.exports = {
  images: {
    domains: ['cdn.tapdine.com'],
    formats: ['image/webp', 'image/avif'],
  },
  experimental: {
    optimizeCss: true,
    optimizeImages: true,
  },
};
```

## 📈 Analytics and Monitoring

### Performance Metrics

```typescript
// Track component render times
const ComponentPerformance = ({ children }) => {
  useEffect(() => {
    const startTime = performance.now();
    return () => {
      const endTime = performance.now();
      analytics.track('component_render_time', {
        duration: endTime - startTime,
        component: 'MenuManagementDashboard'
      });
    };
  });
  
  return children;
};
```

### User Interaction Tracking

```typescript
const trackUserAction = (action: string, data: any) => {
  analytics.track('menu_management_action', {
    action,
    data,
    timestamp: new Date().toISOString(),
    userId: user.id,
    tenantId: tenant.id,
  });
};
```

## 🎯 Future Enhancements

### Planned Features

1. **AI-Powered Suggestions**
   - Automatic category suggestions
   - Price optimization recommendations
   - Ingredient auto-completion

2. **Advanced Analytics**
   - Performance metrics per item
   - A/B testing for descriptions
   - Customer preference insights

3. **Bulk Import/Export**
   - CSV/Excel import with validation
   - PDF menu generation
   - Template system

4. **Collaborative Editing**
   - Real-time collaboration
   - Change tracking
   - Approval workflows

5. **Enhanced Image Management**
   - Multiple images per item
   - Automatic image optimization
   - Background removal tools

## 📚 API Reference

### Menu Items Endpoints

```typescript
// GET /api/v1/admin/tenants/{tenantId}/menu-items
interface GetMenuItemsResponse {
  items: MenuItemAdmin[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// POST /api/v1/admin/tenants/{tenantId}/menu-items
interface CreateMenuItemRequest {
  name: string;
  description: string;
  category: string;
  basePrice: number;
  // ... other fields
}

// PUT /api/v1/admin/tenants/{tenantId}/menu-items/{itemId}
interface UpdateMenuItemRequest {
  name?: string;
  description?: string;
  // ... partial update fields
}

// DELETE /api/v1/admin/tenants/{tenantId}/menu-items/{itemId}
// Bulk operations
// PUT /api/v1/admin/tenants/{tenantId}/menu-items/bulk
// DELETE /api/v1/admin/tenants/{tenantId}/menu-items/bulk
```

### Categories Endpoints

```typescript
// GET /api/v1/admin/tenants/{tenantId}/categories
interface GetCategoriesResponse {
  categories: MenuCategoryAdmin[];
  total: number;
}

// POST /api/v1/admin/tenants/{tenantId}/categories
// PUT /api/v1/admin/tenants/{tenantId}/categories/{categoryId}
// DELETE /api/v1/admin/tenants/{tenantId}/categories/{categoryId}
```

This comprehensive CRUD system provides a solid foundation for menu management while maintaining flexibility for future enhancements. The reusable components and hooks can be easily adapted for other data types in the TapDine ecosystem.
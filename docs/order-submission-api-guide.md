# BHEEMDINE Order Submission API Guide

## Overview

This guide covers the comprehensive FastAPI implementation for secure guest order submissions in BHEEMDINE. The API provides multi-tenant isolation, robust validation, and secure payment processing for restaurant and hotel ordering systems.

## Architecture Overview

### Technology Stack
- **FastAPI** with Python 3.11+
- **Prisma ORM** for database operations
- **Pydantic v2** for data validation
- **PostgreSQL** with row-level security
- **Redis** for rate limiting (production)
- **Structured logging** for audit trails

### Security Features
- **Multi-tenant isolation** with tenant-scoped operations
- **Rate limiting** to prevent abuse
- **Input sanitization** to prevent injection attacks
- **Request validation** with comprehensive error handling
- **Audit logging** for all order activities
- **Payment security** with tokenized transactions

## API Endpoints

### 1. Submit Guest Order

**Endpoint**: `POST /api/v1/orders/submit`

**Description**: Complete order submission with validation, payment processing, and inventory management.

#### Request Schema

```typescript
interface OrderSubmissionRequest {
  // Tenant and location
  tenant_id: string;           // UUID of restaurant/hotel
  room_id: string;            // UUID of room/table
  
  // Customer information (optional for guests)
  customer_info?: {
    name?: string;            // Guest name
    email?: string;           // Email for notifications
    phone?: string;           // Phone for updates
  };
  
  // Order items (required)
  items: OrderItem[];         // Array of items being ordered
  
  // Delivery information
  delivery_info?: {
    room_number?: string;     // Room/table number
    delivery_instructions?: string;
    preferred_delivery_time?: string;
    is_contactless: boolean;
  };
  
  // Payment information
  payment_info: {
    payment_method: "CASH" | "CARD" | "DIGITAL_WALLET" | "ROOM_CHARGE";
    payment_token?: string;   // Required for CARD/DIGITAL_WALLET
    billing_name?: string;
    tip_amount?: number;
  };
  
  // Order metadata
  order_notes?: string;       // General order notes
  estimated_total: number;    // Client-calculated total for validation
  client_info?: object;       // Client app information
}

interface OrderItem {
  menu_item_id: string;       // UUID of menu item
  quantity: number;           // Quantity (1-99)
  unit_price: number;         // Unit price for validation
  customizations?: {
    modifications: string[];   // Item modifications
    special_instructions?: string;
    spice_level?: "mild" | "medium" | "hot" | "extra_hot";
  };
  notes?: string;            // Item-specific notes
}
```

#### Sample Request

```json
{
  "tenant_id": "123e4567-e89b-12d3-a456-************",
  "room_id": "789e1234-e89b-12d3-a456-************",
  "customer_info": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890"
  },
  "items": [
    {
      "menu_item_id": "456e7890-e89b-12d3-a456-************",
      "quantity": 2,
      "unit_price": 15.99,
      "customizations": {
        "modifications": ["No onions", "Extra cheese"],
        "special_instructions": "Medium spice level",
        "spice_level": "medium"
      },
      "notes": "Please cook well done"
    },
    {
      "menu_item_id": "654e3210-e89b-12d3-a456-************",
      "quantity": 1,
      "unit_price": 8.50,
      "customizations": {
        "modifications": ["Extra sauce"]
      }
    }
  ],
  "delivery_info": {
    "room_number": "205A",
    "delivery_instructions": "Knock softly, baby sleeping",
    "preferred_delivery_time": "2024-12-15T19:00:00Z",
    "is_contactless": true
  },
  "payment_info": {
    "payment_method": "CARD",
    "payment_token": "tok_1234567890abcdef",
    "billing_name": "John Doe",
    "tip_amount": 5.00
  },
  "order_notes": "Anniversary dinner, please make it special",
  "estimated_total": 45.48
}
```

#### Response Schema

```typescript
interface OrderSubmissionResponse {
  // Order identification
  order_id: string;           // UUID of created order
  order_number: string;       // Human-readable order number
  
  // Status and timing
  status: "PENDING" | "CONFIRMED" | "PREPARING" | "READY" | "DELIVERED";
  placed_at: string;          // ISO timestamp
  estimated_preparation_time: number;  // Minutes
  estimated_ready_time: string;        // ISO timestamp
  
  // Financial information
  subtotal: number;
  tax_amount: number;
  tip_amount?: number;
  total_amount: number;
  
  // Order details
  items: OrderItemResponse[];
  
  // Customer and location
  customer_id?: string;       // UUID if registered
  tenant_name: string;
  room_number?: string;
  
  // Communication
  confirmation_message: string;
  tracking_url: string;
}
```

#### Sample Response

```json
{
  "order_id": "987e6543-e89b-12d3-a456-************",
  "order_number": "ORD-241215-0042",
  "status": "PENDING",
  "placed_at": "2024-12-15T18:30:00Z",
  "estimated_preparation_time": 25,
  "estimated_ready_time": "2024-12-15T18:55:00Z",
  "subtotal": 40.48,
  "tax_amount": 3.24,
  "tip_amount": 5.00,
  "total_amount": 48.72,
  "items": [
    {
      "id": "abc12345-e89b-12d3-a456-************",
      "menu_item_id": "456e7890-e89b-12d3-a456-************",
      "menu_item_name": "Grilled Salmon",
      "quantity": 2,
      "unit_price": 15.99,
      "total_price": 31.98,
      "status": "PENDING"
    }
  ],
  "customer_id": "cust_789e1234-e89b-12d3-a456-************",
  "confirmation_message": "Your order #ORD-241215-0042 has been received!",
  "tracking_url": "/orders/987e6543-e89b-12d3-a456-************/track",
  "tenant_name": "The Grand Hotel Restaurant",
  "room_number": "205A"
}
```

### 2. Additional Endpoints

#### Get Order Details
- **Endpoint**: `GET /api/v1/orders/{order_id}`
- **Purpose**: Retrieve complete order information
- **Auth**: Optional (guest access with order ID)

#### Track Order Status
- **Endpoint**: `GET /api/v1/orders/track/{order_id}`
- **Purpose**: Real-time order status tracking
- **Response**: Status history and estimated times

#### Health Check
- **Endpoint**: `GET /api/v1/orders/health`
- **Purpose**: Service health and dependency status
- **Response**: Database, payment processor, and service status

## Validation and Security

### Input Validation

#### Comprehensive Field Validation
```python
# Price validation with decimal precision
unit_price: Decimal = Field(
    ...,
    ge=0,
    le=9999.99,
    decimal_places=2,
    description="Unit price at time of order"
)

# Email validation with format checking
email: Optional[str] = Field(
    None,
    regex=r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    description="Email for order notifications"
)

# Phone validation with international support
phone: Optional[str] = Field(
    None,
    description="Phone number (10-15 digits)"
)

@validator('phone')
def validate_phone(cls, v):
    if v is None:
        return v
    cleaned = re.sub(r'\D', '', v)
    if len(cleaned) < 10 or len(cleaned) > 15:
        raise ValueError('Invalid phone number length')
    return cleaned
```

#### Business Rule Validation
```python
# Order limits
MAX_ORDER_AMOUNT = Decimal('500.00')
MAX_ITEMS_PER_ORDER = 20
ORDER_TIMEOUT_MINUTES = 15

# Total verification
@validator('estimated_total')
def validate_estimated_total(cls, v, values):
    items = values.get('items', [])
    calculated_total = sum(item.unit_price * item.quantity for item in items)
    variance = abs(calculated_total - v)
    
    if variance > Decimal('0.05'):  # 5 cent tolerance
        raise ValueError('Total mismatch with item calculations')
    return v
```

### Security Measures

#### SQL Injection Prevention
```python
# Text field sanitization
suspicious_patterns = ['<script', 'javascript:', 'SELECT ', 'DROP ', 'INSERT ']

def sanitize_text_input(text: str) -> str:
    for pattern in suspicious_patterns:
        if pattern.lower() in text.lower():
            raise SecurityError('Invalid characters detected')
    return text.strip()
```

#### Rate Limiting
```python
# Rate limits per IP, tenant, and room
RATE_LIMITS = {
    'ip': (10, 3600),        # 10 orders per hour per IP
    'room': (5, 3600),       # 5 orders per hour per room  
    'tenant': (100, 3600)    # 100 orders per hour per tenant
}

async def check_rate_limits(client_ip: str, tenant_id: str, room_id: str):
    # Implementation with Redis for distributed rate limiting
    pass
```

#### Authentication Support
```python
# Optional authentication with JWT
@router.post("/submit")
async def submit_order(
    request: OrderSubmissionRequest,
    current_user: Optional[Dict] = Depends(get_current_user_optional)
):
    # Supports both authenticated and anonymous submissions
    if current_user:
        # Validate user has access to tenant
        validate_user_tenant_access(current_user, request.tenant_id)
```

## Database Integration

### Prisma ORM Operations

#### Transaction-Based Order Creation
```python
async def create_order_with_items(self, ...):
    async with self.client.tx() as transaction:
        # 1. Create order record
        order = await transaction.order.create(data=order_data)
        
        # 2. Create order items
        await transaction.orderitem.create_many(data=items_data)
        
        # 3. Update inventory
        for item in validated_items:
            await transaction.menuitem.update(
                where={'id': item['menu_item'].id},
                data={'inventory': {'decrement': item['quantity']}}
            )
        
        # 4. Create audit event
        await transaction.orderevent.create(data=audit_data)
        
        # 5. Update room status
        await transaction.room.update(
            where={'id': room_id},
            data={'status': 'OCCUPIED'}
        )
        
        return order
```

#### Multi-Tenant Data Validation
```python
async def validate_tenant_and_room(self, tenant_id: str, room_id: str):
    # Parallel fetch for performance
    tenant_task = self.client.tenant.find_unique(
        where={'id': tenant_id}
    )
    room_task = self.client.room.find_unique(
        where={'id': room_id},
        include={'tenant': True}
    )
    
    tenant, room = await asyncio.gather(tenant_task, room_task)
    
    # Validation checks
    if not tenant or not tenant.isActive:
        raise ValueError('Tenant not found or inactive')
    
    if not room or room.tenantId != tenant_id:
        raise ValueError('Room does not belong to tenant')
    
    return {'tenant': tenant, 'room': room}
```

### Menu Item Validation
```python
async def validate_menu_items(self, tenant_id: str, item_requests: list):
    # Bulk fetch for performance
    menu_item_ids = [str(item.menu_item_id) for item in item_requests]
    menu_items = await self.client.menuitem.find_many(
        where={
            'id': {'in': menu_item_ids},
            'tenantId': tenant_id,
            'isAvailable': True
        }
    )
    
    # Create lookup map
    items_map = {item.id: item for item in menu_items}
    
    # Validate each request
    for item_request in item_requests:
        item_id = str(item_request.menu_item_id)
        
        if item_id not in items_map:
            raise ValueError(f'Menu item {item_id} not found')
        
        menu_item = items_map[item_id]
        
        # Price validation with tolerance
        price_variance = abs(float(menu_item.price) - float(item_request.unit_price))
        if price_variance > 0.05:
            raise ValueError(f'Price mismatch for {menu_item.name}')
        
        # Inventory check
        if menu_item.inventory and menu_item.inventory < item_request.quantity:
            raise ValueError(f'Insufficient inventory for {menu_item.name}')
    
    return validated_items
```

## Error Handling

### Structured Error Responses

#### Validation Errors (400)
```json
{
  "error_code": "VALIDATION_ERROR",
  "error_message": "Request validation failed",
  "field_errors": {
    "items.0.unit_price": ["Price must be positive"],
    "customer_info.email": ["Invalid email format"]
  },
  "timestamp": "2024-12-15T18:30:00Z",
  "request_id": "req_1702659000_12345"
}
```

#### Security Errors (403)
```json
{
  "error_code": "SECURITY_ERROR",
  "error_message": "Security validation failed",
  "details": {},
  "timestamp": "2024-12-15T18:30:00Z",
  "request_id": "req_1702659000_12345"
}
```

#### Business Logic Errors (409)
```json
{
  "error_code": "INVENTORY_INSUFFICIENT",
  "error_message": "Insufficient inventory for requested items",
  "details": {
    "unavailable_items": [
      {
        "menu_item_id": "456e7890-e89b-12d3-a456-************",
        "requested": 5,
        "available": 2
      }
    ]
  },
  "timestamp": "2024-12-15T18:30:00Z",
  "request_id": "req_1702659000_12345"
}
```

#### Rate Limit Errors (429)
```json
{
  "error_code": "RATE_LIMIT_EXCEEDED",
  "error_message": "Too many order attempts",
  "details": {
    "retry_after": 3600,
    "limit": "10 orders per hour"
  },
  "timestamp": "2024-12-15T18:30:00Z",
  "request_id": "req_1702659000_12345"
}
```

### Error Handling Implementation

```python
try:
    response = await order_service.submit_guest_order(request)
    return response
    
except OrderValidationError as e:
    # Client-side errors (400)
    raise HTTPException(
        status_code=400,
        detail={
            "error_code": e.error_code,
            "error_message": e.message,
            "details": e.details
        }
    )
    
except OrderSecurityError as e:
    # Security violations (403)
    logger.error(f"SECURITY ALERT: {e.message}")
    raise HTTPException(
        status_code=403,
        detail={
            "error_code": "SECURITY_ERROR",
            "error_message": "Access denied"
        }
    )
    
except Exception as e:
    # Unexpected errors (500)
    logger.error(f"Unexpected error: {e}")
    raise HTTPException(
        status_code=500,
        detail={
            "error_code": "INTERNAL_ERROR",
            "error_message": "An unexpected error occurred"
        }
    )
```

## Testing and Quality Assurance

### Unit Testing

```python
import pytest
from fastapi.testclient import TestClient

def test_submit_valid_order():
    """Test successful order submission"""
    request_data = {
        "tenant_id": "123e4567-e89b-12d3-a456-************",
        "room_id": "789e1234-e89b-12d3-a456-************",
        "items": [
            {
                "menu_item_id": "456e7890-e89b-12d3-a456-************",
                "quantity": 2,
                "unit_price": 15.99
            }
        ],
        "payment_info": {
            "payment_method": "CASH"
        },
        "estimated_total": 31.98
    }
    
    response = client.post("/api/v1/orders/submit", json=request_data)
    
    assert response.status_code == 201
    assert "order_id" in response.json()
    assert response.json()["status"] == "PENDING"

def test_invalid_tenant_id():
    """Test order submission with invalid tenant"""
    request_data = {
        "tenant_id": "invalid-uuid",
        # ... rest of request
    }
    
    response = client.post("/api/v1/orders/submit", json=request_data)
    
    assert response.status_code == 422
    assert "VALIDATION_ERROR" in response.json()["error_code"]

def test_price_mismatch():
    """Test order submission with price mismatch"""
    # Mock menu item with different price
    request_data = {
        # ... valid request but wrong unit_price
        "items": [
            {
                "menu_item_id": "456e7890-e89b-12d3-a456-************",
                "quantity": 1,
                "unit_price": 10.00  # Wrong price
            }
        ]
    }
    
    response = client.post("/api/v1/orders/submit", json=request_data)
    
    assert response.status_code == 400
    assert "TOTAL_MISMATCH" in response.json()["error_code"]
```

### Integration Testing

```python
@pytest.mark.asyncio
async def test_complete_order_flow():
    """Test complete order submission flow"""
    
    # Setup test data
    tenant = await create_test_tenant()
    room = await create_test_room(tenant.id)
    menu_item = await create_test_menu_item(tenant.id)
    
    # Submit order
    order_request = build_test_order_request(tenant.id, room.id, menu_item.id)
    response = await submit_order(order_request)
    
    # Verify order creation
    assert response.status_code == 201
    order_id = response.json()["order_id"]
    
    # Verify database state
    order = await get_order_from_db(order_id)
    assert order.status == "PENDING"
    assert len(order.items) == len(order_request["items"])
    
    # Verify inventory update
    updated_item = await get_menu_item_from_db(menu_item.id)
    assert updated_item.inventory == menu_item.inventory - order_request["items"][0]["quantity"]
```

## Deployment and Operations

### Environment Configuration

```python
# settings.py
from pydantic import BaseSettings

class Settings(BaseSettings):
    # Database
    database_url: str
    database_pool_size: int = 20
    
    # Security
    jwt_secret: str
    api_key_header: str = "X-API-Key"
    rate_limit_redis_url: str
    
    # Payment processing
    payment_processor_url: str
    payment_processor_key: str
    
    # Monitoring
    log_level: str = "INFO"
    sentry_dsn: str = None
    
    class Config:
        env_file = ".env"

settings = Settings()
```

### Performance Monitoring

```python
import time
from fastapi import Request

@app.middleware("http")
async def performance_monitoring(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    
    # Log slow requests
    if process_time > 1.0:  # 1 second threshold
        logger.warning(f"Slow request: {request.url.path} took {process_time:.2f}s")
    
    return response
```

### Health Checks and Monitoring

```python
@router.get("/health")
async def health_check():
    """Comprehensive health check"""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "components": {}
    }
    
    # Database health
    try:
        await db.execute("SELECT 1")
        health_status["components"]["database"] = {"status": "healthy"}
    except Exception as e:
        health_status["components"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "degraded"
    
    # Payment processor health
    try:
        # Check payment processor connectivity
        health_status["components"]["payment"] = {"status": "healthy"}
    except Exception as e:
        health_status["components"]["payment"] = {
            "status": "unhealthy", 
            "error": str(e)
        }
        health_status["status"] = "degraded"
    
    status_code = 200 if health_status["status"] != "unhealthy" else 503
    return JSONResponse(status_code=status_code, content=health_status)
```

## Production Considerations

### Security Best Practices

1. **Input Validation**: All inputs validated with Pydantic models
2. **SQL Injection Prevention**: Parameterized queries with Prisma ORM
3. **Rate Limiting**: Distributed rate limiting with Redis
4. **Authentication**: JWT tokens with proper validation
5. **Audit Logging**: Comprehensive logging of all operations
6. **Error Handling**: Secure error messages without information leakage

### Performance Optimization

1. **Database Connections**: Connection pooling with proper sizing
2. **Caching**: Redis caching for frequently accessed data
3. **Async Operations**: Non-blocking I/O for all database operations
4. **Batch Processing**: Bulk operations where possible
5. **Monitoring**: Real-time performance metrics and alerting

### Scalability Features

1. **Horizontal Scaling**: Stateless API design for multiple instances
2. **Database Optimization**: Proper indexing and query optimization
3. **Caching Strategy**: Multi-layer caching for performance
4. **Queue Processing**: Async processing for non-critical operations
5. **CDN Integration**: Static asset optimization

This comprehensive API implementation provides a robust, secure, and scalable foundation for BHEEMDINE's order submission system.
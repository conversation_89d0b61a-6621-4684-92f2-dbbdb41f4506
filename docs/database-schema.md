# BHEEMDINE Database Schema Documentation

## Overview
This multi-tenant database schema is designed for BHEEMDINE, a QR-code based ordering system for restaurants and hotels. The schema supports multiple tenants (restaurant chains or individual establishments) with complete data isolation.

## Recent Improvements (v2)
1. **Fixed NULL uniqueness issues**: Added defaults for email/phone/pin fields
2. **Normalized allergens**: Replaced array column with proper junction table
3. **Tenant-scoped QR codes**: Changed from global to per-tenant uniqueness
4. **Soft deletes**: Added isDeleted flag and trigger to prevent hard deletes
5. **Removed denormalization**: OrderItem.totalPrice calculated dynamically
6. **Hot path indexes**: Added partial indexes for common queries
7. **Audit trail**: Added OrderEvent table for status change tracking

## Key Design Decisions

### 1. Multi-Tenancy Strategy
- **Row-level security**: Each table has a `tenantId` foreign key
- **Data isolation**: All queries must filter by tenantId
- **Scalability**: Single database can support multiple tenants efficiently

### 2. Primary Keys
- All tables use UUID primary keys for:
  - Better distribution in distributed systems
  - No sequential ID exposure
  - Easier data migration

### 3. Indexes Strategy
- Composite indexes for common query patterns
- Single column indexes for foreign keys
- Unique constraints that include tenantId for tenant-scoped uniqueness

## Table Relationships

### Tenant (Root Entity)
- One-to-many with all other tables
- Soft delete via `isActive` flag
- Stores tenant-specific settings as JSON

### Users → Orders
- Users can place multiple orders
- Guest checkout supported (email/phone optional)
- Room assignment links guests to physical locations

### Rooms → Orders
- Each room can have multiple orders
- QR codes provide unique access points
- Status tracking for availability

### MenuItems → OrderItems
- Menu items linked to orders through OrderItems
- Price captured at order time for historical accuracy
- Supports dietary preferences and allergen tracking

### Staff → Orders
- Orders can be assigned to staff members
- Role-based access control
- PIN-based quick authentication

## Security Considerations

1. **Row Level Security (RLS)**
   - Enable RLS on all tables in Supabase
   - Policies should always check tenantId

2. **Cascading Deletes**
   - Tenant deletion cascades to all related data
   - Order/User deletion is restricted to maintain history

3. **Unique Constraints**
   - Email/Phone unique per tenant, not globally
   - Room numbers unique per tenant
   - Order numbers unique per tenant

## Performance Optimizations

1. **Composite Indexes**
   - `[tenantId, category, sortOrder]` for menu queries
   - `[tenantId, email]` for user lookups
   - `[tenantId, roomNumber]` for room searches

2. **Status Indexes**
   - Order status for kitchen displays
   - Room status for availability checks
   - Staff isActive for login queries

3. **Timestamp Indexes**
   - placedAt for order history queries
   - lastActive for user activity tracking

## Prisma Usage Examples

### Creating a new order
```typescript
const order = await prisma.order.create({
  data: {
    tenantId: 'tenant-uuid',
    userId: 'user-uuid',
    roomId: 'room-uuid',
    orderNumber: 'ORD-2024-0001',
    totalAmount: 45.50,
    items: {
      create: [
        {
          menuItemId: 'menu-item-uuid',
          quantity: 2,
          unitPrice: 15.00,
          totalPrice: 30.00
        }
      ]
    }
  },
  include: {
    items: {
      include: {
        menuItem: true
      }
    }
  }
});
```

### Querying tenant-specific data
```typescript
const menuItems = await prisma.menuItem.findMany({
  where: {
    tenantId: 'tenant-uuid',
    isAvailable: true,
    category: 'Main Course'
  },
  orderBy: {
    sortOrder: 'asc'
  }
});
```

## Migration Considerations

1. **Initial Setup**
   - Create tenant first
   - Seed with default menu categories
   - Generate QR codes for rooms

2. **Data Migration**
   - UUIDs allow easy import/export
   - Maintain referential integrity
   - Update indexes after bulk imports

3. **Schema Evolution**
   - Use Prisma migrations for version control
   - Test migrations on staging first
   - Plan for zero-downtime deployments
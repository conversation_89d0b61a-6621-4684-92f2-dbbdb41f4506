# WhatsApp Notifications for TapDine Orders

This guide explains how to use the WhatsApp notification system for real-time order notifications in the BHEEMDINE/TapDine platform.

## Overview

The WhatsApp notification system uses Twilio's WhatsApp Business API to send real-time notifications to:
- Kitchen staff when new orders arrive
- Management staff for order monitoring
- Customers for order status updates

## Features

- **Real-time notifications** - Instant WhatsApp messages for new orders
- **Automatic integration** - Works seamlessly with existing order flow
- **Rich messaging** - Formatted messages with order details, items, and instructions
- **Error handling** - Robust retry logic and graceful failure handling
- **Multi-tenant support** - Different phone numbers per restaurant/tenant
- **Bulk messaging** - Send announcements to multiple recipients

## Setup

### 1. Twilio Configuration

First, set up your Twilio WhatsApp Business account and configure environment variables:

```bash
# Required Twilio credentials
export TWILIO_ACCOUNT_SID="your_account_sid_here"
export TWILIO_AUTH_TOKEN="your_auth_token_here"
export TWILIO_WHATSAPP_FROM="whatsapp:+***********"  # Your Twilio WhatsApp number

# Default staff phone numbers (comma-separated)
export BHEEMDINE_STAFF_PHONES="+**********,+**********"
export BHEEMDINE_KITCHEN_PHONES="+**********,+**********"

# Tenant-specific phone numbers (optional)
export TENANT_123e4567_STAFF_PHONES="+**********,+**********"
export TENANT_123e4567_KITCHEN_PHONES="+**********"
```

### 2. Install Dependencies

Add Twilio to your Python requirements:

```bash
pip install twilio httpx
```

### 3. Enable in Your Application

The WhatsApp notification service is automatically integrated with the order submission flow. No additional configuration needed once environment variables are set.

## Usage Examples

### 1. Automatic Order Notifications

When a customer places an order, notifications are automatically sent:

```python
# This happens automatically in the order submission endpoint
# No additional code needed - notifications are sent in background
```

**Example notification to staff:**
```
🚨 NEW ORDER ALERT 🚨

🏪 The Grand Hotel Restaurant
📋 Order #ORD-241215-0042
🏠 Room: 205A
👤 Customer: John Doe
💰 Total: $45.99

📦 Items (3):
• 2x Grilled Salmon
• 1x Caesar Salad
• 1x Chocolate Cake

📝 Special Instructions:
Anniversary dinner, please make it special

⏰ Received: 6:30 PM

Please confirm receipt and estimated preparation time.
```

### 2. Manual Order Notifications

Send notifications programmatically using the API:

```python
import httpx
import asyncio

async def send_order_notification():
    order_data = {
        "order_id": "987e6543-e89b-12d3-a456-426614174003",
        "order_number": "ORD-241215-0042",
        "customer_name": "John Doe",
        "room_number": "205A",
        "total_amount": 45.99,
        "item_count": 3,
        "order_notes": "Anniversary dinner, please make it special",
        "tenant_name": "The Grand Hotel Restaurant",
        "staff_phones": [
            {"number": "+**********"},
            {"number": "+**********"}
        ],
        "items": [
            {
                "menu_item_name": "Grilled Salmon",
                "quantity": 2,
                "unit_price": 18.99,
                "notes": "Medium rare",
                "customizations": {"modifications": ["No onions"]}
            },
            {
                "menu_item_name": "Caesar Salad", 
                "quantity": 1,
                "unit_price": 8.99
            }
        ]
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/api/v1/notifications/order/new",
            json=order_data
        )
        
    print(f"Notification sent: {response.json()}")

# Run the example
asyncio.run(send_order_notification())
```

### 3. Order Status Updates

Send status updates to customers:

```python
async def send_status_update():
    status_data = {
        "order_id": "987e6543-e89b-12d3-a456-426614174003",
        "order_number": "ORD-241215-0042",
        "customer_phone": {"number": "+**********"},
        "new_status": "PREPARING",
        "estimated_time": "25 minutes",
        "tenant_name": "The Grand Hotel Restaurant"
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/api/v1/notifications/order/status-update",
            json=status_data
        )
        
    print(f"Status update sent: {response.json()}")
```

**Example customer notification:**
```
📱 Order Update 📱

🏪 The Grand Hotel Restaurant
📋 Order #ORD-241215-0042

👨‍🍳 Your order is currently being prepared.
⏱️ Estimated time: 25 minutes

Thank you for your patience! 🙏
```

### 4. Kitchen Notifications

Send detailed notifications to kitchen staff:

```python
async def send_kitchen_notification():
    kitchen_data = {
        "order_id": "987e6543-e89b-12d3-a456-426614174003",
        "order_number": "ORD-241215-0042",
        "room_number": "205A",
        "kitchen_phones": [
            {"number": "+**********"},
            {"number": "+**********"}
        ],
        "priority": "urgent",
        "order_notes": "Anniversary dinner",
        "delivery_instructions": "Knock softly, baby sleeping",
        "items": [
            {
                "menu_item_name": "Grilled Salmon",
                "quantity": 2,
                "notes": "Medium rare, no onions",
                "customizations": {
                    "modifications": ["No onions", "Extra lemon"],
                    "special_instructions": "Cook well"
                }
            }
        ]
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/api/v1/notifications/kitchen/order",
            json=kitchen_data
        )
        
    print(f"Kitchen notification sent: {response.json()}")
```

### 5. Bulk Notifications

Send announcements to multiple staff members:

```python
async def send_bulk_announcement():
    bulk_data = {
        "message": "🚨 IMPORTANT: Kitchen will close early today at 9 PM for maintenance. Please plan accordingly.",
        "phone_numbers": [
            {"number": "+**********"},
            {"number": "+**********"},
            {"number": "+**********"}
        ],
        "message_type": "announcement"
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/api/v1/notifications/bulk",
            json=bulk_data
        )
        
    print(f"Bulk notification sent: {response.json()}")
```

## API Endpoints

### POST `/api/v1/notifications/order/new`
Send new order notification to staff.

**Request Body:**
```json
{
    "order_id": "uuid",
    "order_number": "string",
    "customer_name": "string", 
    "room_number": "string",
    "total_amount": 0.0,
    "item_count": 0,
    "staff_phones": [{"number": "+**********"}],
    "order_notes": "string (optional)",
    "tenant_name": "string (optional)",
    "items": [] // optional detailed items
}
```

### POST `/api/v1/notifications/order/status-update`
Send order status update to customer.

**Request Body:**
```json
{
    "order_id": "uuid",
    "order_number": "string",
    "customer_phone": {"number": "+**********"},
    "new_status": "PREPARING",
    "estimated_time": "25 minutes (optional)",
    "tenant_name": "string (optional)"
}
```

### POST `/api/v1/notifications/kitchen/order`
Send detailed order notification to kitchen.

**Request Body:**
```json
{
    "order_id": "uuid",
    "order_number": "string",
    "room_number": "string",
    "kitchen_phones": [{"number": "+**********"}],
    "priority": "normal|urgent|special",
    "items": [
        {
            "menu_item_name": "string",
            "quantity": 1,
            "notes": "string",
            "customizations": {}
        }
    ]
}
```

### POST `/api/v1/notifications/bulk`
Send bulk message to multiple recipients.

**Request Body:**
```json
{
    "message": "string (max 1600 chars)",
    "phone_numbers": [{"number": "+**********"}],
    "message_type": "general|announcement|alert"
}
```

### GET `/api/v1/notifications/health`
Check WhatsApp service health.

**Response:**
```json
{
    "status": "healthy|degraded|unhealthy",
    "account_status": "active",
    "from_number": "whatsapp:+***********",
    "tested_at": "2024-12-15T18:30:00Z"
}
```

## Error Handling

The system includes comprehensive error handling:

### Common Error Codes

- `CONFIGURATION_ERROR` - Missing Twilio credentials
- `MESSAGE_SEND_FAILED` - Failed to send message after retries
- `INVALID_PHONE_NUMBER` - Phone number format invalid
- `NOTIFICATION_SEND_ERROR` - General notification failure

### Example Error Response

```json
{
    "error_code": "MESSAGE_SEND_FAILED",
    "error_message": "Failed to send WhatsApp message after 3 attempts",
    "details": {
        "to_number": "+**********",
        "attempts": 3
    }
}
```

### Retry Logic

- Automatic retry up to 3 times for failed messages
- 5-second delay between retries
- Certain errors (invalid phone numbers) don't retry
- Partial failures in bulk operations are reported individually

## Testing

### Test Individual Components

```python
# Test phone number validation
response = requests.post(
    "http://localhost:8000/api/v1/notifications/validate-phone",
    params={"phone": "+**********"}
)
print(response.json())

# Test service health
response = requests.get("http://localhost:8000/api/v1/notifications/health")
print(response.json())
```

### Test Complete Flow

```python
import asyncio
from src.api.services.whatsapp_notification import create_whatsapp_service

async def test_notifications():
    # Create service
    service = create_whatsapp_service()
    
    # Test connection
    health = await service.test_connection()
    print(f"Service health: {health}")
    
    # Test order notification
    order_data = {
        "order_id": "test-123",
        "order_number": "TEST-001",
        "customer_name": "Test Customer",
        "room_number": "101",
        "total_amount": 25.99,
        "item_count": 2,
        "items": [
            {"menu_item_name": "Test Item", "quantity": 2}
        ]
    }
    
    # Use your test phone number
    result = await service.send_new_order_notification(
        order_data=order_data,
        staff_phones=["+**********"],  # Your test number
        tenant_info={"name": "Test Restaurant"}
    )
    
    print(f"Notification result: {result}")

# Run test
asyncio.run(test_notifications())
```

## Best Practices

### 1. Phone Number Format
- Always include country code (e.g., +1 for US)
- Use the validation endpoint to verify numbers
- Store numbers in international format

### 2. Message Content
- Keep messages concise but informative
- Use emojis for visual appeal and quick scanning
- Include essential information: order number, room, items
- Provide estimated times when available

### 3. Error Handling
- Handle partial failures in bulk operations
- Log all notification attempts for debugging
- Don't fail order processing if notifications fail
- Implement fallback notification methods

### 4. Performance
- Use background tasks for notifications
- Send notifications in parallel when possible
- Don't block order response for notification delivery

### 5. Security
- Validate all phone numbers before sending
- Rate limit notification endpoints
- Sanitize message content
- Protect Twilio credentials

## Troubleshooting

### Common Issues

1. **"Configuration Error"**
   - Check Twilio credentials are set correctly
   - Verify WhatsApp Business API is enabled
   - Confirm from number is WhatsApp-enabled

2. **"Message Send Failed"**
   - Check recipient has WhatsApp installed
   - Verify phone number format includes country code
   - Ensure recipient hasn't blocked your number

3. **"Invalid Phone Number"**
   - Use international format (+**********)
   - Validate numbers before storing in database
   - Check for extra characters or spaces

4. **Notifications Not Sending**
   - Check environment variables are loaded
   - Verify service is initialized in order flow
   - Check logs for specific error messages

### Debug Mode

Enable debug logging for detailed troubleshooting:

```python
import logging
logging.getLogger('twilio').setLevel(logging.DEBUG)
logging.getLogger('src.api.services.whatsapp_notification').setLevel(logging.DEBUG)
```

## Environment Variables Reference

```bash
# Required
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token  
TWILIO_WHATSAPP_FROM=whatsapp:+***********

# Optional - Default phone numbers
BHEEMDINE_STAFF_PHONES=+**********,+**********
BHEEMDINE_KITCHEN_PHONES=+**********,+**********

# Optional - Tenant-specific phone numbers
TENANT_{tenant_id}_STAFF_PHONES=+**********
TENANT_{tenant_id}_KITCHEN_PHONES=+**********
```

## Production Deployment

### 1. Twilio Setup
- Apply for WhatsApp Business API access
- Complete business verification process
- Set up webhook endpoints for delivery receipts
- Configure approved message templates

### 2. Phone Number Management
- Store staff phone numbers in database
- Implement admin interface for managing contacts
- Add phone number validation on user registration
- Support opt-in/opt-out functionality

### 3. Monitoring
- Monitor delivery rates and failures
- Set up alerts for service outages
- Track message costs and usage
- Log all notification attempts

### 4. Scaling
- Use Redis for rate limiting
- Implement message queues for high volume
- Cache frequently used tenant configurations
- Consider multiple Twilio accounts for high throughput

This completes the WhatsApp notification system for BHEEMDINE/TapDine. The system provides real-time, reliable notifications for order management while maintaining robust error handling and scalability.
# BHEEMDINE Row-Level Security Implementation Guide

## Overview

This guide explains the RLS (Row-Level Security) implementation for BHEEMDINE's multi-tenant architecture in Supabase/PostgreSQL. The policies ensure complete data isolation between tenants while supporting different user roles and access patterns.

## Architecture

### JWT Token Structure

The RLS policies expect JWT tokens with the following claims:

```json
{
  "sub": "user-uuid",           // User ID
  "tenant_id": "tenant-uuid",   // Tenant ID for multi-tenancy
  "role": "customer|staff",     // Primary role
  "staff_role": "ADMIN|MANAGER|CHEF|WAITER|RECEPTIONIST" // Staff-specific role
}
```

### User Types

1. **Anonymous Users**: Can scan QR codes and view public menu
2. **Customers**: Can place orders and view their order history
3. **Staff**: Can manage orders, with permissions based on staff_role

## Policy Design Patterns

### 1. Tenant Isolation Pattern

Every policy includes tenant check:
```sql
"tenantId" = auth.tenant_id()::UUID
```

### 2. Role-Based Access Pattern

Policies check user role and staff role:
```sql
auth.user_role() = 'staff' AND auth.staff_role() IN ('ADMIN', 'MANAGER')
```

### 3. Self-Access Pattern

Users can access their own data:
```sql
id = auth.user_id() AND auth.user_role() = 'customer'
```

### 4. Public Access Pattern

Some data is publicly accessible with tenant validation:
```sql
EXISTS (
  SELECT 1 FROM "Tenant" t 
  WHERE t.id = "MenuItem"."tenantId" 
  AND t."isActive" = true 
  AND t."isDeleted" = false
)
```

## Implementation by Table

### Tenant Table
- **View**: Only active, non-deleted tenant you belong to
- **Update**: Only ADMIN staff role
- **Delete**: Prevented by trigger (soft delete only)

### User Table
- **View**: Staff see all; customers see only themselves
- **Insert**: Allow guest user creation
- **Update**: Users can update own profile; staff can update any

### Room Table
- **View**: Public (for QR scanning)
- **Insert/Update**: Only ADMIN/MANAGER staff

### MenuItem Table
- **View**: Public for available items
- **All Operations**: ADMIN/MANAGER/CHEF staff only

### Order Table
- **View**: Customers see own; staff see all in tenant
- **Insert**: Customers can create
- **Update**: Staff can update any; customers can only cancel pending

### OrderItem Table
- **Permissions**: Follow parent order's visibility

### Staff Table
- **View**: Only other staff members
- **Insert/Update**: Only ADMIN/MANAGER
- **Special**: Staff can update own lastLogin

## Security Best Practices

### 1. Always Check Tenant
Every policy must validate tenant_id to prevent cross-tenant access.

### 2. Use SECURITY DEFINER Functions Sparingly
Only for operations that need elevated privileges:
```sql
CREATE FUNCTION has_room_access(room_id UUID)
RETURNS BOOLEAN AS $$
...
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 3. Explicit Deny Over Implicit
Use `onDelete: Restrict` in schema to prevent accidental cascades.

### 4. Audit Trail
OrderEvent table provides immutable audit log with RLS protection.

## Testing RLS Policies

### 1. Test Tenant Isolation
```sql
-- Set context for Tenant A
SET request.jwt.claims = '{"tenant_id": "tenant-a-id", ...}';
-- Try to access Tenant B data (should fail)
```

### 2. Test Role Permissions
```sql
-- Test as customer
SET request.jwt.claims = '{"role": "customer", ...}';
-- Test as staff
SET request.jwt.claims = '{"role": "staff", "staff_role": "WAITER", ...}';
```

### 3. Test Anonymous Access
```sql
-- Clear JWT claims
RESET request.jwt.claims;
-- Test public endpoints (menu, rooms)
```

## Common Patterns

### 1. QR Code Flow
1. Anonymous user scans QR code
2. Can view room and menu without auth
3. Creates guest user on first order
4. Order linked to room and guest user

### 2. Staff Order Management
1. Staff authenticates with PIN
2. Can view all orders in tenant
3. Can update order status
4. All changes logged in OrderEvent

### 3. Customer Order Flow
1. Customer places order
2. Can view own orders only
3. Can cancel if status is PENDING
4. Cannot modify after confirmation

## Troubleshooting

### Issue: "new row violates row-level security policy"
- Check tenant_id in JWT matches data
- Verify user role and permissions
- Ensure all required fields are provided

### Issue: No data returned
- Check RLS is enabled on table
- Verify JWT claims are set correctly
- Check tenant is active and not deleted

### Issue: Can see other tenant's data
- Review policy conditions
- Ensure tenant_id check is present
- Check for missing AND conditions

## Performance Considerations

1. **Indexes**: All foreign keys and policy conditions are indexed
2. **Partial Indexes**: Used for hot paths (active orders, available items)
3. **Function Calls**: Minimized in policies for performance

## Migration Checklist

- [ ] Enable RLS on all tables
- [ ] Create auth helper functions
- [ ] Implement policies for each table
- [ ] Test all user scenarios
- [ ] Verify cross-tenant isolation
- [ ] Test anonymous access paths
- [ ] Validate performance with explain analyze
- [ ] Document custom JWT claims needed
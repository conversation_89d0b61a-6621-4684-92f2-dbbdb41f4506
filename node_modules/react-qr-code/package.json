{"name": "react-qr-code", "version": "2.0.18", "description": "A QR code generator for React and React Native.", "keywords": ["code", "generator", "qr", "react", "react-native"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"demo-web-watch": "babel ./src --out-dir=./demo/src/lib --watch", "demo": "cd demo && npm run start", "demo:build": "cd demo && npx expo export -p web", "demo:deploy": "gh-pages -d demo/dist", "clean": "<PERSON><PERSON><PERSON> lib", "lint": "biome check --apply ./src", "build": "npm run clean && babel src --out-dir lib", "release": "release-it"}, "license": "MIT", "repository": {"type": "git", "url": "**************:rosskhanas/react-qr-code.git"}, "files": ["lib", "types"], "main": "lib/index.js", "types": "types/index.d.ts", "dependencies": {"prop-types": "^15.8.1", "qr.js": "0.0.0"}, "peerDependencies": {"react": "*"}, "devDependencies": {"@biomejs/biome": "^1.7.3", "@release-it/conventional-changelog": "^8.0.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-es2015": "^6.24.1", "babel-preset-react": "^6.24.1", "gh-pages": "^6.1.1", "release-it": "^17.3.0", "rimraf": "^5.0.7"}}